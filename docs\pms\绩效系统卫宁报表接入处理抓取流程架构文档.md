# 绩效系统卫宁报表接入处理抓取流程架构文档

## 📋 概述

本文档详细描述了绩效系统卫宁报表接入处理抓取流程的完整架构设计，包括数据流转、组件职责、技术实现等方面。

## 🏗️ 整体架构

### 架构层次
1. **外部数据源层** - 卫宁报表系统
2. **数据接入层** - WinningReportExporter核心导出器
3. **文件存储层** - OSS文件存储
4. **调度执行层** - 批量调度器和单任务调度器
5. **ETL处理层** - 数据抽取、转换、加载
6. **数据存储层** - PMS数据库
7. **前端展示层** - Vue组件

## 🔄 核心流程

### 1. 数据抓取流程
```
卫宁报表系统 → WinningReportExporter → OSS文件存储
```

**核心组件：WinningReportExporter**
- 🔐 登录认证：自动登录卫宁系统
- ⚙️ 报表配置获取：获取报表参数和配置
- 📥 Excel下载解析：下载并解析Excel报表文件

### 2. 调度执行流程
```
BatchPmsCaptureScheduler → PmsCaptureScheduler → ETL流程
```

**批量采集调度器 (BatchPmsCaptureScheduler)**
- ⏰ 定时调度任务：按配置的时间间隔执行
- 📦 批次管理：管理多个采集任务的批次
- 🔄 并行执行控制：控制并发执行数量

**单任务调度器 (PmsCaptureScheduler)**
- 🎯 ETL流程执行：执行具体的数据处理流程
- 🔄 数据抽取转换：处理数据格式转换
- 📊 状态监控：监控任务执行状态

### 3. ETL数据处理流程
```
数据抽取(Extract) → 数据转换(Transform) → 数据加载(Load)
```

#### 数据抽取 (Extract)
- 📊 报表数据获取：从卫宁系统获取原始数据
- 📥 Excel文件下载：下载Excel格式的报表文件
- 💾 文件缓存处理：将文件缓存到OSS存储

#### 数据转换 (Transform)
- 🔄 数据格式转换：将Excel数据转换为标准格式
- 📋 业务规则处理：应用业务逻辑和规则
- 🧹 数据清洗验证：清洗和验证数据质量

#### 数据加载 (Load)
- 💰 成本控制数据：加载到成本控制相关表
- 🏆 绩效计算数据：加载到绩效计算相关表
- 💾 数据库存储：持久化存储到PMS数据库

## 🗄️ 数据存储

### 数据库表结构
1. **成本控制数据表 (pmsIaeReportItem)**
   - 存储成本控制相关的报表数据
   - 用于成本分析和控制

2. **绩效计算数据表 (pmsAwardCalcItem)**
   - 存储绩效计算相关的数据
   - 用于绩效考核和奖励计算

## 🖥️ 前端Vue组件

### 核心组件
1. **pmsDataETL.vue** - 数据ETL管理
   - 📊 ETL任务管理和监控
   - ⚙️ 数据处理配置

2. **wnReportConfig.vue** - 卫宁报表配置
   - 🔧 卫宁系统连接配置
   - 📋 报表参数设置

3. **CaptureMonitor.vue** - 采集监控组件
   - 📈 实时监控采集状态
   - 📊 任务执行进度展示

4. **DataPreview.vue** - 数据预览组件
   - 👀 数据预览和查看
   - 🔍 数据质量检查

5. **LogViewer.vue** - 日志查看组件
   - 📝 系统日志查看
   - 🐛 错误日志分析

## 🔧 技术特点

### 高可靠性
- 🔄 自动重试机制
- 📝 完整的日志记录
- ⚠️ 异常处理和告警

### 高性能
- 🚀 并行处理支持
- 💾 文件缓存机制
- 📊 批量数据处理

### 可扩展性
- 🔌 模块化设计
- 🎯 插件化架构
- 📈 水平扩展支持

## 🔄 数据流向

```mermaid
graph TD
    A[卫宁报表系统] -->|HTTP请求| B[WinningReportExporter]
    B -->|文件上传| C[OSS文件存储]
    D[BatchPmsCaptureScheduler] -->|调度执行| E[PmsCaptureScheduler]
    E --> F[数据抽取]
    F -->|1. 抽取| G[数据转换]
    G -->|2. 转换| H[数据加载]
    H -->|成本控制数据| I[pmsIaeReportItem]
    H -->|绩效计算数据| J[pmsAwardCalcItem]
```

## 📋 配置说明

### 卫宁系统配置
- 🌐 服务器地址和端口
- 👤 登录用户名和密码
- 📊 报表ID和参数配置

### 调度配置
- ⏰ 执行时间间隔
- 🔢 并发执行数量
- 📝 重试次数和策略

### 存储配置
- 💾 OSS存储配置
- 🗄️ 数据库连接配置
- 📁 文件路径配置

## 🚀 部署和运维

### 部署要求
- ☕ Java 8+ 运行环境
- 🗄️ MySQL数据库
- 💾 OSS对象存储
- 🌐 网络连接到卫宁系统

### 监控指标
- 📊 任务执行成功率
- ⏱️ 数据处理耗时
- 💾 存储空间使用率
- 🔄 系统资源使用情况

## 🔍 故障排查

### 常见问题
1. **连接失败** - 检查网络和认证配置
2. **数据格式错误** - 检查Excel文件格式和字段映射
3. **存储空间不足** - 清理历史文件或扩容
4. **性能问题** - 调整并发数量和批次大小

### 日志分析
- 📝 查看系统日志定位问题
- 🔍 分析错误堆栈信息
- 📊 监控性能指标变化

---

*本文档描述了绩效系统卫宁报表接入的完整架构设计，为系统开发、部署和运维提供参考。*
