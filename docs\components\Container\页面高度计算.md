# `src/components/common/container/index.vue` 页面高度计算逻辑文档 📄

该文档旨在说明 `container/index.vue` 组件中关于页面布局高度动态计算的核心逻辑。

## 核心函数与目标

主要的计算逻辑封装在 `adjustHeight` 方法中。其目标是根据容器的实际尺寸、头部（查询区域）和底部（分页区域）的显示状态和内容，动态计算出中间内容区域的可用高度，并将其通过 `contentHeight` 事件传递给父组件，常用于设置表格 (`n-data-table`) 的 `max-height`。

## 影响高度计算的因素 📐

1.  **容器 (`containerRef`)**: 提供计算的总高度基准。
2.  **头部 (`headerRef`)**:
    - 是否显示 (`showHeader` prop)。
    - 头部内容的实际渲染高度 (`headerDom.scrollHeight`)。这会受到表单项数量、是否启用精确查询 (`accurateQueryFlag`) 等因素影响。
    - `calSpace` 变量：一个微调间距，根据 `defaultItemsIsComp` (默认表单项是否为组件) 和 `showFormItems` (是否显示默认表单项) 等条件调整内容区域与头部的垂直间距。
3.  **底部/分页 (`footerRef`)**:
    - 是否显示分页 (`pagination?.show` prop)。
    - 显示时，底部占用固定的高度 (`footerHeight`)。
4.  **内容区域的特殊元素**:
    - **表格头部 (`.n-data-table-thead`)**: 如果 `calcTableHeight` prop 为 `true`，其高度会被计算并从最终 `contentHeight` 中减去。组件内部使用 `MutationObserver` 监听表头 DOM 变化，以应对多行表头等场景。
    - **Tab 头部 (`.n-tabs-nav-scroll-content`)**: 如果 `calcTabHeight` prop 为 `true`，其高度会被计算并从最终 `contentHeight` 中减去。

## 计算流程 ⚙️

1.  **获取 DOM 元素**: 获取 `containerRef`、`headerRef`、`footerRef` (如果显示分页) 的 DOM 实例。
2.  **计算头部高度**: 获取 `headerRef` 的实际滚动高度 (`scrollHeight`)。
3.  **设置内容区域顶部 (`contentStyle.top`)**: 等于头部高度加上 `calSpace` 调整值。
4.  **设置内容区域底部 (`contentStyle.bottom`)**: 如果显示分页，则设置为 `footerHeight`。
5.  **计算内容区域高度 (`contentStyle.height`)**: 容器总高 (`containerDom.scrollHeight`) 减去计算出的头部高度和底部高度 (如果有)，再加上固定的内边距补偿 (20px)。
6.  **计算并发出 `contentHeight`**:
    - 从 `contentStyle.height` 中减去表格头部高度 (如果 `calcTableHeight` 为 true)。
    - 减去 Tab 头部高度 (如果 `calcTabHeight` 为 true)。
    - 减去 `calSpace` 调整值。
    - 通过 `ctx.emit('contentHeight', calculatedHeight + 'px')` 发出计算结果。

## 触发计算的时机 ⏰

`adjustHeight` 函数会在以下时机被调用：

1.  **组件首次挂载 (`onMounted`)**: 进行初始化计算。
2.  **窗口大小调整 (`resize` event)**: 监听 `window` 的 `resize` 事件 (使用 `useDebounceFn` 防抖)，自适应窗口变化。
3.  **精确查询切换 (`accurateQuery` function call)**: 当点击精确查询按钮切换显示更多表单项时，会重新计算高度。
4.  **全局初始化状态变更 (`watch sysStore.$state.initOnload`)**: 当依赖的全局状态变化时，可能需要重新初始化表单并调整高度。
5.  **表格头部 DOM 变化 (`MutationObserver` callback)**: 当表格头部的结构或属性发生变化时，自动触发高度调整。

## 注意事项 ⚠️

- 计算依赖于 DOM 元素的实际渲染尺寸，因此部分计算放在 `nextTick` 中执行，确保 DOM 更新完毕。
- `calSpace` 的引入是为了在不同表单配置下（纯组件 vs 非组件、有无默认项）优化视觉间距。
- `contentHeight` 事件是该组件与其他需要动态高度的内容（如图表、表格）交互的关键。

希望这份文档能帮助你理解该组件的高度计算机制！🎉
