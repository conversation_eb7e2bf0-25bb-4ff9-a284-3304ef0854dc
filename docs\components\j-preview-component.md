---
description:
globs:
alwaysApply: false
---
# j-preview 组件

## 📝 组件概述
`j-preview` 和 `j-npreview` 组件是用于文件预览的组件，支持多种文件格式的在线预览，包括图片、PDF、Office文档、视频等。新版本推荐使用 `j-npreview` 组件，它提供了更好的用户体验和更多的功能。

## 🔧 Props 属性

### j-preview Props

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| show | Boolean | false | 控制预览组件的显示/隐藏 |
| file | Object | null | 文件对象，包含文件信息 |
| ossPath | String | '' | 文件在OSS中的路径 |
| bucket | String | 'core' | OSS的bucket名称 |
| ossPathName | String | '' | OSS文件名称 |
| showHeader | Boolean | true | 是否显示预览头部 |
| showFooter | Boolean | true | 是否显示预览底部 |
| autoHeight | Boolean | false | 是否自动调整高度 |
| previewStyle | Object | {} | 预览区域样式 |

### j-npreview Props (新版本)

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| show | Boolean | false | 控制预览组件的显示/隐藏 |
| file | Object | null | 文件对象，包含文件信息 |
| ossPath | String | '' | 文件在OSS中的路径 |
| bucket | String | 'core' | OSS的bucket名称 |
| ossPathName | String | '' | OSS文件名称 |
| width | [String, Number] | '100%' | 预览区域宽度 |
| height | [String, Number] | '600px' | 预览区域高度 |
| enableDownload | Boolean | true | 是否允许下载 |
| enablePrint | Boolean | true | 是否允许打印 |
| enableFullscreen | Boolean | true | 是否允许全屏 |
| showToolbar | Boolean | true | 是否显示工具栏 |
| fileType | String | '' | 强制指定文件类型 |

## 🎯 事件

| 事件名 | 参数 | 说明 |
| --- | --- | --- |
| update:show | (value: Boolean) | 预览显示/隐藏状态变更事件 |
| error | (error: Error) | 预览错误事件 |
| load | (file: Object) | 文件加载完成事件 |
| download | (file: Object) | 文件下载事件 |
| print | (file: Object) | 文件打印事件 |

## 📋 插槽

| 插槽名 | 说明 |
| --- | --- |
| default | 默认插槽，当文件无法预览时显示 |
| header | 自定义预览头部 |
| footer | 自定义预览底部 |
| loading | 自定义加载中状态 |
| error | 自定义错误状态 |
| toolbar | 自定义工具栏 |

## 🌟 使用示例

### 基础使用
```vue
<template>
  <n-button @click="previewVisible = true">预览文件</n-button>
  
  <n-modal v-model:show="previewVisible" preset="card" style="width: 80%; max-width: 1200px">
    <template #header>
      <div class="preview-header">
        <span>文件预览</span>
        <n-button circle quaternary @click="previewVisible = false">
          <template #icon>
            <n-icon><close-outline /></n-icon>
          </template>
        </n-button>
      </div>
    </template>
    <div class="preview-content">
      <j-npreview
        v-if="previewVisible"
        v-model:show="previewVisible"
        :file="fileObject"
        :oss-path="fileInfo.ossPath"
        :bucket="fileInfo.bucket || 'core'"
        :oss-path-name="fileInfo.ossPathName || ''"
      />
    </div>
  </n-modal>
</template>

<script lang="tsx" setup>
  import { ref, reactive } from 'vue'
  import { CloseOutline } from '@vicons/ionicons5'
  
  const previewVisible = ref(false)
  const fileObject = ref(null)
  const fileInfo = reactive({
    ossPath: 'path/to/file.pdf',
    bucket: 'core',
    ossPathName: 'file.pdf'
  })
  
  const openPreview = (file) => {
    fileObject.value = file
    previewVisible.value = true
  }
</script>

<style lang="scss" scoped>
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .preview-content {
    min-height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
```

### 不同文件类型的预览
```vue
<template>
  <j-npreview
    v-model:show="previewVisible"
    :file="currentFile"
    :oss-path="currentFile?.ossPath"
    :bucket="currentFile?.bucket"
    :oss-path-name="currentFile?.name"
    :height="700"
    @error="handlePreviewError"
  />
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { useMessage } from 'naive-ui'
  
  const message = useMessage()
  const previewVisible = ref(false)
  const currentFile = ref(null)
  
  const previewFile = (file) => {
    currentFile.value = file
    previewVisible.value = true
  }
  
  const handlePreviewError = (error) => {
    message.error('文件预览失败: ' + error.message)
  }
</script>
```

### 自定义工具栏
```vue
<template>
  <j-npreview
    v-model:show="previewVisible"
    :file="fileObject"
    :oss-path="ossPath"
    :bucket="bucket"
    :enable-download="false"
    :enable-print="false"
    :show-toolbar="true"
  >
    <template #toolbar>
      <div class="custom-toolbar">
        <n-button type="primary" size="small" @click="handleApprove">
          审批通过
        </n-button>
        <n-button type="error" size="small" @click="handleReject">
          驳回
        </n-button>
        <n-button size="small" @click="handleDownload">
          下载
        </n-button>
      </div>
    </template>
  </j-npreview>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { download } from '@/api/common/common'
  
  const previewVisible = ref(false)
  const fileObject = ref(null)
  const ossPath = ref('')
  const bucket = ref('core')
  
  const handleApprove = () => {
    // 处理审批逻辑
  }
  
  const handleReject = () => {
    // 处理驳回逻辑
  }
  
  const handleDownload = () => {
    download({
      ossPath: ossPath.value,
      bucket: bucket.value,
      filename: fileObject.value?.name
    })
  }
</script>

<style scoped>
.custom-toolbar {
  display: flex;
  gap: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
```

## 🔍 组件实现细节

`j-preview` 和 `j-npreview` 组件基于第三方预览库实现，支持多种文件格式的在线预览。组件根据文件类型自动选择合适的预览方式，如：

- 图片：直接使用 `<img>` 标签渲染
- PDF：使用 PDF.js 渲染
- Office文档：使用第三方服务转换后预览
- 视频：使用 HTML5 `<video>` 标签渲染
- 音频：使用 HTML5 `<audio>` 标签渲染

对于无法直接预览的文件，组件会提供下载选项。

`j-npreview` 是新版本的预览组件，提供了更好的用户体验和更多功能，包括缩放、旋转、全屏等，建议优先使用。

## ⚠️ 注意事项

1. 预览组件需要与OSS服务配合使用，确保文件可以通过OSS访问
2. 对于大型文件，预览可能需要较长时间，建议添加加载提示
3. Office文档预览可能需要转换服务支持，确保服务可用
4. 使用 `v-if="previewVisible"` 条件渲染可以避免组件在未显示时消耗资源
5. 不同的文件类型可能有不同的预览效果和支持的功能
