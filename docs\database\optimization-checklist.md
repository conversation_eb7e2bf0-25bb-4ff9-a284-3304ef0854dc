# MySQL 5.7 优化实施检查清单 ✅

## 实施前准备工作

### 1. 环境准备 🔧

- [ ] **备份当前数据库**
  ```bash
  mysqldump -u root -p --single-transaction --routines --triggers your_database > backup_$(date +%Y%m%d_%H%M%S).sql
  ```

- [ ] **记录当前配置**
  ```sql
  SHOW VARIABLES;
  SHOW GLOBAL STATUS;
  ```

- [ ] **性能基线测试**
  ```bash
  # 使用sysbench进行基准测试
  sysbench oltp_read_write --mysql-host=localhost --mysql-user=root --mysql-password=password --mysql-db=testdb --tables=10 --table-size=100000 prepare
  sysbench oltp_read_write --mysql-host=localhost --mysql-user=root --mysql-password=password --mysql-db=testdb --tables=10 --table-size=100000 --threads=16 --time=300 run
  ```

- [ ] **测试环境验证**
  - [ ] 测试环境与生产环境配置一致
  - [ ] 测试数据量与生产环境相当
  - [ ] 网络环境模拟生产场景

### 2. 风险评估 ⚠️

- [ ] **评估停机时间窗口**
  - [ ] 确定维护时间窗口
  - [ ] 通知相关业务部门
  - [ ] 准备回滚方案

- [ ] **影响范围分析**
  - [ ] 识别依赖系统
  - [ ] 评估业务影响
  - [ ] 制定应急预案

## 第一阶段：索引优化 📊

### 1. 分析现有索引

- [ ] **检查索引使用情况**
  ```sql
  SELECT 
      TABLE_SCHEMA,
      TABLE_NAME,
      INDEX_NAME,
      CARDINALITY,
      SUB_PART,
      PACKED,
      NULLABLE,
      INDEX_TYPE
  FROM information_schema.STATISTICS 
  WHERE TABLE_SCHEMA = 'your_database_name'
  ORDER BY TABLE_NAME, SEQ_IN_INDEX;
  ```

- [ ] **识别未使用的索引**
  ```sql
  SELECT 
      object_schema,
      object_name,
      index_name
  FROM performance_schema.table_io_waits_summary_by_index_usage 
  WHERE index_name IS NOT NULL
  AND count_star = 0
  AND object_schema = 'your_database_name';
  ```

### 2. 创建新索引

#### som_dip_grp_info 表
- [ ] `idx_hospital_dip_time (HOSPITAL_ID, dip_codg, dscg_time)`
- [ ] `idx_settle_patient (SETTLE_LIST_ID, PATIENT_ID)`
- [ ] `idx_time_range (dscg_time, adm_time, setl_end_time)`

#### som_cd_grp_info 表
- [ ] `idx_hospital_cd_time (HOSPITAL_ID, cd_codg, dscg_time)`
- [ ] `idx_settle_list (SETTLE_LIST_ID)`

#### som_drg_grp_info 表
- [ ] `idx_hospital_drg_time (HOSPITAL_ID, drg_codg, setl_end_time)`
- [ ] `idx_settle_list (SETTLE_LIST_ID)`

#### 标准信息表
- [ ] som_dip_standard: `idx_standard_lookup (HOSPITAL_ID, STANDARD_YEAR, dip_codg)`
- [ ] som_cd_standard_info: `idx_standard_lookup (HOSPITAL_ID, STANDARD_YEAR, cd_codg)`
- [ ] som_drg_standard: `idx_standard_lookup (HOSPITAL_ID, STANDARD_YEAR, drg_codg)`

#### 评分表
- [ ] som_dip_sco: `idx_settle_ym (SETTLE_LIST_ID, ym)`
- [ ] som_drg_sco: `idx_settle_ym (SETTLE_LIST_ID, ym)`

### 3. 索引创建后验证

- [ ] **检查索引创建状态**
  ```sql
  SHOW INDEX FROM som_dip_grp_info;
  ```

- [ ] **验证索引使用**
  ```sql
  EXPLAIN SELECT * FROM som_dip_grp_info WHERE HOSPITAL_ID = 'H001' AND dip_codg = 'DIP001';
  ```

## 第二阶段：配置参数优化 ⚙️

### 1. 内存配置

- [ ] **调整InnoDB缓冲池**
  ```ini
  innodb_buffer_pool_size = 8G
  innodb_buffer_pool_instances = 8
  ```

- [ ] **优化查询缓存**
  ```ini
  query_cache_type = 0
  query_cache_size = 0
  ```

- [ ] **调整临时表大小**
  ```ini
  tmp_table_size = 256M
  max_heap_table_size = 256M
  ```

### 2. InnoDB引擎配置

- [ ] **文件和I/O配置**
  ```ini
  innodb_file_per_table = 1
  innodb_flush_method = O_DIRECT
  innodb_io_capacity = 2000
  innodb_io_capacity_max = 4000
  ```

- [ ] **日志配置**
  ```ini
  innodb_log_file_size = 1G
  innodb_log_files_in_group = 2
  innodb_log_buffer_size = 64M
  ```

- [ ] **线程配置**
  ```ini
  innodb_read_io_threads = 8
  innodb_write_io_threads = 8
  innodb_purge_threads = 4
  ```

### 3. 连接和超时配置

- [ ] **连接数配置**
  ```ini
  max_connections = 500
  max_connect_errors = 1000
  ```

- [ ] **超时配置**
  ```ini
  wait_timeout = 28800
  interactive_timeout = 28800
  connect_timeout = 10
  ```

### 4. 配置生效验证

- [ ] **重启MySQL服务**
  ```bash
  sudo systemctl restart mysql
  ```

- [ ] **验证配置生效**
  ```sql
  SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
  SHOW VARIABLES LIKE 'innodb_io_capacity';
  ```

## 第三阶段：查询优化 🔍

### 1. 创建物化视图

- [ ] **标准费用缓存表**
  ```sql
  CREATE TABLE som_standard_fee_cache AS ...
  ```

- [ ] **诊断信息汇总表**
  ```sql
  CREATE TABLE som_diagnosis_summary AS ...
  ```

- [ ] **手术信息汇总表**
  ```sql
  CREATE TABLE som_operation_summary AS ...
  ```

- [ ] **费用信息汇总表**
  ```sql
  CREATE TABLE som_fee_summary AS ...
  ```

### 2. 为缓存表创建索引

- [ ] som_standard_fee_cache: `PRIMARY KEY (group_type, code, HOSPITAL_ID, STANDARD_YEAR)`
- [ ] som_diagnosis_summary: `PRIMARY KEY (settle_list_id)`
- [ ] som_operation_summary: `PRIMARY KEY (settle_list_id)`
- [ ] som_fee_summary: `PRIMARY KEY (hi_setl_invy_id)`

### 3. 更新应用代码

- [ ] **修改MyBatis XML文件**
  - [ ] 使用优化后的查询语句
  - [ ] 替换复杂子查询为JOIN
  - [ ] 优化条件判断逻辑

- [ ] **更新Service层代码**
  - [ ] 实现缓存逻辑
  - [ ] 添加分页优化
  - [ ] 实现批量查询

## 第四阶段：缓存策略实施 🚀

### 1. Redis缓存配置

- [ ] **安装和配置Redis**
  ```bash
  sudo apt-get install redis-server
  sudo systemctl start redis-server
  ```

- [ ] **配置Redis参数**
  ```conf
  maxmemory 4gb
  maxmemory-policy allkeys-lru
  save 900 1
  save 300 10
  save 60 10000
  ```

### 2. 应用层缓存实现

- [ ] **添加Redis依赖**
  ```xml
  <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
  </dependency>
  ```

- [ ] **实现缓存Service**
  - [ ] 标准费用信息缓存
  - [ ] 科室信息缓存
  - [ ] 诊断信息缓存

- [ ] **配置缓存策略**
  - [ ] 设置合适的过期时间
  - [ ] 实现缓存预热
  - [ ] 处理缓存穿透

## 第五阶段：监控和告警 📈

### 1. 性能监控设置

- [ ] **安装监控工具**
  ```bash
  # 安装Prometheus
  wget https://github.com/prometheus/prometheus/releases/download/v2.40.0/prometheus-2.40.0.linux-amd64.tar.gz
  
  # 安装Grafana
  sudo apt-get install -y software-properties-common
  sudo add-apt-repository "deb https://packages.grafana.com/oss/deb stable main"
  sudo apt-get update
  sudo apt-get install grafana
  ```

- [ ] **配置MySQL监控**
  ```yaml
  # prometheus.yml
  scrape_configs:
    - job_name: 'mysql'
      static_configs:
        - targets: ['localhost:9104']
  ```

### 2. 关键指标监控

- [ ] **数据库连接数**
- [ ] **查询响应时间**
- [ ] **慢查询数量**
- [ ] **缓冲池命中率**
- [ ] **磁盘I/O使用率**
- [ ] **CPU和内存使用率**

### 3. 告警规则配置

- [ ] **慢查询告警**
  ```yaml
  - alert: MySQLSlowQueries
    expr: mysql_global_status_slow_queries > 10
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "MySQL slow queries detected"
  ```

- [ ] **连接数告警**
  ```yaml
  - alert: MySQLHighConnections
    expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections > 0.8
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "MySQL connection usage is high"
  ```

## 第六阶段：性能测试验证 🧪

### 1. 功能测试

- [ ] **基础功能验证**
  - [ ] DIP分组查询功能
  - [ ] CD分组查询功能
  - [ ] DRG分组查询功能
  - [ ] 复杂条件查询功能
  - [ ] 数据导出功能

- [ ] **数据一致性验证**
  - [ ] 对比优化前后查询结果
  - [ ] 验证计算字段准确性
  - [ ] 检查关联数据完整性

### 2. 性能测试

- [ ] **单用户性能测试**
  ```bash
  # 使用JMeter进行单用户测试
  jmeter -n -t single_user_test.jmx -l results.jtl
  ```

- [ ] **并发性能测试**
  ```bash
  # 使用JMeter进行并发测试
  jmeter -n -t concurrent_test.jmx -l concurrent_results.jtl
  ```

- [ ] **压力测试**
  ```bash
  # 使用sysbench进行压力测试
  sysbench oltp_read_write --mysql-host=localhost --mysql-user=root --mysql-password=password --mysql-db=testdb --tables=10 --table-size=1000000 --threads=100 --time=600 run
  ```

### 3. 性能指标对比

- [ ] **响应时间对比**
- [ ] **吞吐量对比**
- [ ] **资源使用率对比**
- [ ] **错误率对比**

## 第七阶段：生产环境部署 🚀

### 1. 部署前检查

- [ ] **测试环境验证通过**
- [ ] **性能提升达到预期**
- [ ] **功能测试全部通过**
- [ ] **回滚方案准备就绪**

### 2. 部署步骤

- [ ] **数据库配置更新**
  - [ ] 在维护窗口内重启MySQL
  - [ ] 验证配置参数生效
  - [ ] 检查数据库服务状态

- [ ] **索引创建**
  - [ ] 按优先级顺序创建索引
  - [ ] 监控索引创建进度
  - [ ] 验证索引创建成功

- [ ] **应用代码部署**
  - [ ] 部署优化后的代码
  - [ ] 启动应用服务
  - [ ] 验证应用功能正常

### 3. 部署后验证

- [ ] **功能验证**
  - [ ] 核心业务功能测试
  - [ ] 用户界面响应测试
  - [ ] 数据准确性验证

- [ ] **性能验证**
  - [ ] 查询响应时间测试
  - [ ] 并发用户访问测试
  - [ ] 系统资源使用监控

## 第八阶段：持续优化 🔄

### 1. 定期维护任务

- [ ] **每周任务**
  - [ ] 检查慢查询日志
  - [ ] 监控系统性能指标
  - [ ] 更新缓存数据

- [ ] **每月任务**
  - [ ] 分析表统计信息
  - [ ] 优化表结构
  - [ ] 清理历史数据

- [ ] **每季度任务**
  - [ ] 性能基准测试
  - [ ] 容量规划评估
  - [ ] 优化策略调整

### 2. 监控和告警维护

- [ ] **监控指标调整**
- [ ] **告警阈值优化**
- [ ] **监控面板更新**

### 3. 文档更新

- [ ] **更新运维文档**
- [ ] **记录优化经验**
- [ ] **培训团队成员**

---

## 检查清单总结

### 关键成功因素 ✨

1. **充分的测试验证** - 确保在生产环境部署前完成全面测试
2. **详细的回滚计划** - 准备完整的回滚方案以应对意外情况
3. **持续的监控** - 建立完善的监控体系跟踪优化效果
4. **团队协作** - 确保开发、运维、业务团队密切配合

### 风险控制措施 ⚠️

1. **分阶段实施** - 避免一次性大规模变更
2. **备份策略** - 确保数据安全和可恢复性
3. **性能基线** - 建立性能基准用于对比验证
4. **应急预案** - 制定详细的应急处理流程

通过严格按照此检查清单执行，可以确保MySQL 5.7优化项目的成功实施，实现预期的性能提升目标。
