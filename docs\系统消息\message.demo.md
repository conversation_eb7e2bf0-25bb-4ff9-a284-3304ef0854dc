# 🌟 工作流消息处理系统开发文档 🌟

## 📋 1. 系统概述

工作流消息处理是我们整个消息系统的一个典型应用场景。本文档将以工作流消息处理为例，详细说明消息处理系统的架构设计、实现方式和使用方法。

## 🔍 2. 工作流消息类型

工作流消息类型定义在 `src/types/sys/SysMessageTypeEnum.ts` 中：

```typescript
// 工作流相关消息
BPM_TASK = 'bpm-2001',    // 任务消息
BPM_TODO = 'bpm-2002',    // 待办消息
BPM_PASS = 'bpm-2003',    // 通过消息
BPM_NOT_PASS = 'bpm-2004', // 不通过消息
BPM_RECALL = 'bpm-2005',  // 撤回消息
BPM_CANCEL = 'bpm-2006',  // 取消消息
```

这些消息类型通过 `bpmClassifyEnum` 数组进行分组：

```typescript
// 工作流消息类型
export const bpmClassifyEnum = [
  SysMessageTypeEnum.BPM_TASK,
  SysMessageTypeEnum.BPM_TODO,
  SysMessageTypeEnum.BPM_PASS,
  SysMessageTypeEnum.BPM_NOT_PASS,
  SysMessageTypeEnum.BPM_RECALL,
  SysMessageTypeEnum.BPM_CANCEL,
]
```

## 🔄 3. 工作流消息处理流程

### 3.1 流程图

```
┌─────────────────┐      ┌───────────────────────┐      ┌───────────────────────┐
│                 │      │                       │      │                       │
│  消息点击事件    │─────►│   消息处理服务        │─────►│  工作流消息处理器     │
│  (message.vue)  │      │(MessageHandlerService)│      │  (BpmMessageHandler)  │
│                 │      │                       │      │                       │
└─────────────────┘      └───────────────────────┘      └───────────┬───────────┘
                                                                    │
                                                                    ▼
                                                        ┌───────────────────────┐
                                                        │                       │
                                                        │     事件总线          │
                                                        │    (EventBus)         │
                                                        │                       │
                                                        └───────────┬───────────┘
                                                                    │
                                                                    ▼
                                                        ┌───────────────────────┐
                                                        │                       │
                                                        │  打开流程详情弹窗     │
                                                        │  (App.vue)            │
                                                        │                       │
                                                        └───────────────────────┘
```

### 3.2 具体步骤

1. **消息显示**:

   - 用户看到工作流消息（任务、待办、审批结果等）
   - 消息按照类型分组显示在消息面板中

2. **消息点击**:

   - 用户点击工作流相关消息
   - `message.vue` 中的 `handleMessageClick` 方法被调用
   - 该方法调用 `messageHandlerService.handleMessage(messageData)`

3. **消息处理器识别**:

   - 消息处理服务根据消息类型找到对应的处理器
   - 对于工作流消息，`BpmMessageHandler` 的 `canHandle` 方法返回 `true`
   - 消息处理服务调用 `BpmMessageHandler` 的 `handle` 方法

4. **工作流消息处理**:

   - `BpmMessageHandler` 处理消息
   - 通过事件总线触发 `open-process-detail` 事件
   - 传递流程实例 ID (`processInstanceId`)

5. **流程详情展示**:
   - 应用监听器捕获 `open-process-detail` 事件
   - 打开流程详情弹窗，展示相关流程信息

## 🧩 4. 关键组件详解

### 4.1 工作流消息处理器 (BpmMessageHandler)

```typescript
export class BpmMessageHandler implements MessageHandler {
  // 优先级设置为10
  priority = 10

  // 判断是否可以处理此消息
  canHandle(messageType: string): boolean {
    return bpmClassifyEnum.some(type => String(type) === messageType)
  }

  // 处理工作流消息
  handle(messageData: any): void {
    if (messageData && messageData.gotoUrl) {
      // 使用事件总线触发打开流程详情的事件
      useEventBus().emit('open-process-detail', {
        processInstanceId: messageData.gotoUrl,
      })
    }
  }
}
```

**关键点**:

- `canHandle` 方法检查消息类型是否属于工作流消息
- `handle` 方法从消息数据中提取流程实例 ID 并触发事件

### 4.2 消息点击处理 (message.vue)

```typescript
// 处理消息点击
const handleMessageClick = (messageData: MessageItem) => {
  // 使用消息处理服务处理消息
  const handled = messageHandlerService.handleMessage(messageData)

  if (!handled) {
    // 如果没有处理器处理此消息，提示用户
    message.info('暂不支持此类消息的处理')
  }

  // 关闭消息窗口
  emit('close')

  // 如果在模态窗口中，也关闭模态窗口
  if (showAllMessagesModal.value) {
    showAllMessagesModal.value = false
  }
}
```

**关键点**:

- 调用消息处理服务的 `handleMessage` 方法
- 根据处理结果提供用户反馈
- 处理完成后关闭消息窗口

## 📝 5. 添加新的工作流消息类型

如果需要添加新的工作流消息类型，请按照以下步骤操作：

### 步骤 1: 在 SysMessageTypeEnum 中添加新的消息类型

```typescript
// 在 src/types/sys/SysMessageTypeEnum.ts 中添加
export enum SysMessageTypeEnum {
  // 现有类型...

  // 新增工作流消息类型
  BPM_DELEGATE = 'bpm-2007', // 委托消息
}
```

### 步骤 2: 将新类型添加到工作流分组中

```typescript
// 在 bpmClassifyEnum 数组中添加新类型
export const bpmClassifyEnum = [
  // 现有类型...
  SysMessageTypeEnum.BPM_DELEGATE, // 添加新类型
]
```

### 步骤 3: 添加消息类型信息

```typescript
// 在 sysMessageTypeInfo 对象中添加新类型信息
export const sysMessageTypeInfo: Record<string, SysMessageTypeInfo> = {
  // 现有信息...

  [SysMessageTypeEnum.BPM_DELEGATE]: {
    code: SysMessageTypeEnum.BPM_DELEGATE,
    message: '委托消息',
    url: '',
    icon: h(DocumentTextOutline),
    color: '#409EFF',
    group: '工作流',
  },
}
```

## 🔧 6. 工作流消息处理的最佳实践

1. **统一处理逻辑**：

   - 所有工作流消息统一由 `BpmMessageHandler` 处理
   - 根据不同消息类型在处理器内部区分处理逻辑

2. **URL 格式规范**：

   - 工作流消息的 `gotoUrl` 应始终包含流程实例 ID
   - 格式建议：`processInstanceId`

3. **事件传递规范**：

   - 通过事件总线传递时，使用统一的事件名 `open-process-detail`
   - 数据格式保持一致：`{ processInstanceId: string }`

4. **错误处理**：
   - 在处理器中添加适当的错误处理
   - 对空数据和异常情况进行防御性编程

```typescript
// 改进的错误处理示例
handle(messageData: any): void {
  try {
    if (!messageData) {
      console.warn('工作流消息数据为空');
      return;
    }

    const processInstanceId = messageData.gotoUrl;
    if (!processInstanceId) {
      console.warn('工作流消息缺少流程实例ID');
      return;
    }

    // 使用事件总线触发打开流程详情的事件
    useEventBus().emit('open-process-detail', {
      processInstanceId,
    });
  } catch (error) {
    console.error('处理工作流消息时出错:', error);
  }
}
```

## 📊 7. 工作流消息数据结构

工作流消息的标准数据结构如下：

```typescript
interface BpmMessageData {
  id: string | number // 消息ID
  title: string // 消息标题，如"您有一个新的审批任务"
  pushText: string // 消息内容，如"销售合同审批流程需要您处理"
  pushTime: string // 推送时间
  type: string // 消息类型，如"bpm-2001"
  gotoUrl: string // 流程实例ID，用于打开流程详情
  readFlag: string // 读取状态：0未读，1已读
}
```

## 🚀 8. 调试与测试

### 8.1 模拟工作流消息

```typescript
// 创建测试消息
const testBpmMessage = {
  id: 'test-1',
  title: '测试工作流消息',
  pushText: '这是一个测试工作流消息，点击查看详情',
  pushTime: new Date().toISOString(),
  type: SysMessageTypeEnum.BPM_TASK,
  gotoUrl: 'process-123456',
  readFlag: '0',
}

// 手动触发处理
messageHandlerService.handleMessage(testBpmMessage)
```

### 8.2 监听事件调试

```typescript
// 在组件中添加调试代码
onMounted(() => {
  // 调试用：监听工作流事件
  useEventBus().on('open-process-detail', data => {
    console.log('收到打开流程详情事件，processInstanceId:', data.processInstanceId)
  })
})
```

## 🔍 9. 常见问题排查

1. **消息点击无反应**

   - 检查消息类型是否正确
   - 确认 `BpmMessageHandler` 是否已注册
   - 检查 `canHandle` 方法是否返回 `true`

2. **找不到流程详情**

   - 检查 `gotoUrl` 是否包含有效的流程实例 ID
   - 确认事件监听器是否正确设置
   - 验证监听器是否能正确接收事件

3. **消息未分类至工作流组**
   - 确认消息类型是否正确添加到 `bpmClassifyEnum` 数组

---

🎉 以上是工作流消息处理的完整文档。开发人员可以参考此文档理解工作流消息的处理流程，并按照类似的模式实现其他类型的消息处理。
