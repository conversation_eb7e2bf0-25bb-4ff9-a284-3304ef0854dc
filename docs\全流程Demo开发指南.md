# 🎯 实习生全流程Demo开发指南

## 📋 系统架构分析

### 🏗️ 整体技术架构

#### 前端技术栈
- **核心框架**: Vue 3.5.8 + TypeScript + Vite 5.4.11
- **UI组件库**: Element Plus + Naive UI + Ant Design Vue
- **状态管理**: Pinia + Vuex 4
- **工具库**: Axios、Lodash、Day.js、ECharts
- **工作流**: BPMN.js 流程设计器
- **特色功能**: 表单设计器、富文本编辑、文件处理

#### 后端技术栈
- **核心框架**: Spring Boot 2.3.12 + Spring Cloud Hoxton.SR12
- **数据库**: PostgreSQL + Redis + 支持多数据库
- **ORM框架**: MyBatis-Plus 3.5.3
- **微服务**: Nacos服务发现 + Gateway网关
- **工作流**: Activiti/Flowable BPM引擎
- **分布式事务**: Seata 2.0.0

#### 系统模块架构
```
sfm_web (前端)
├── 人力资源管理 (HRM) - 员工、组织、招聘、排班
├── 绩效管理系统 (PMS) - 绩效计算、数据采集、报表
├── 工作流系统 (BPM) - 流程设计、审批、任务管理
├── 办公自动化 (OA) - 审批流程、文档管理
├── 内容管理 (CMS) - 文档、公告、知识库
└── 其他业务模块...

sfm_back (后端)
├── med-common - 公共模块
├── med-bpm-biz - BPM业务模块
└── 各业务模块微服务
```

## 🎯 Demo项目设计：请假申请管理系统

### 📋 项目概述
设计一个**员工请假申请管理系统**，涵盖前端界面、后端API、工作流程、数据库设计的完整开发流程。

### ✨ 核心功能
1. **请假申请** - 员工提交请假申请
2. **审批流程** - 多级审批工作流
3. **数据管理** - 请假记录CRUD操作  
4. **统计报表** - 请假数据统计分析
5. **消息通知** - 审批状态通知

### 🎨 技术亮点
- 使用现有j-crud、j-container组件快速开发
- 集成BPMN工作流设计
- 实现前后端分离架构
- 包含完整的权限控制
- 支持文件上传（请假证明）
- 数据可视化报表

## 📅 阶段性开发计划

### 🎯 第一阶段（1-2周）：环境搭建与基础开发
**目标**: 熟悉项目结构，完成基础CRUD功能

#### Week 1: 环境准备
**Day 1-2: 项目初始化**
- [ ] 克隆项目代码并完成环境搭建
- [ ] 熟悉前端项目结构和组件使用
- [ ] 阅读AI开发规则和组件使用指南
- [ ] 运行项目并了解主要功能模块

**Day 3-5: 数据库设计**
```sql
-- 请假申请表
CREATE TABLE leave_application (
    id BIGSERIAL PRIMARY KEY,
    applicant_id VARCHAR(50) NOT NULL COMMENT '申请人ID',
    applicant_name VARCHAR(100) NOT NULL COMMENT '申请人姓名',
    dept_id VARCHAR(50) COMMENT '部门ID',
    dept_name VARCHAR(100) COMMENT '部门名称',
    leave_type VARCHAR(20) NOT NULL COMMENT '请假类型(事假/病假/年假/调休)',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    days DECIMAL(3,1) NOT NULL COMMENT '请假天数',
    reason TEXT COMMENT '请假原因',
    attachment_url VARCHAR(500) COMMENT '附件地址',
    status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '状态(草稿/申请中/已通过/已拒绝)',
    process_instance_id VARCHAR(100) COMMENT '流程实例ID',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50)
);

-- 审批记录表
CREATE TABLE leave_approval_record (
    id BIGSERIAL PRIMARY KEY,
    application_id BIGINT NOT NULL,
    approver_id VARCHAR(50) NOT NULL COMMENT '审批人ID',
    approver_name VARCHAR(100) NOT NULL COMMENT '审批人姓名',
    approval_level INTEGER NOT NULL COMMENT '审批级别',
    action VARCHAR(20) NOT NULL COMMENT '审批动作(同意/拒绝)',
    comment TEXT COMMENT '审批意见',
    approval_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Week 2: 基础CRUD开发
**Day 1-3: 后端开发**
- [ ] 创建实体类(Entity)、DTO、VO类
- [ ] 实现Mapper接口和XML映射
- [ ] 开发Service业务逻辑层
- [ ] 创建Controller REST接口
- [ ] 添加Swagger API文档

**Day 4-5: 前端开发**
- [ ] 创建请假申请管理页面
- [ ] 使用j-crud组件实现列表展示
- [ ] 配置表格列和表单字段
- [ ] 实现新增、编辑、删除功能
- [ ] 添加查询筛选功能

### 🎯 第二阶段（2-3周）：工作流集成
**目标**: 集成BPM工作流，实现审批流程

#### Week 3: 流程设计
**Day 1-2: BPMN流程设计**
- [ ] 使用BPMN.js设计请假审批流程
- [ ] 定义流程节点和审批条件
- [ ] 配置审批人员和权限
- [ ] 测试流程的正确性

**Day 3-5: 流程集成开发**
- [ ] 集成Activiti/Flowable工作流引擎
- [ ] 实现流程启动接口
- [ ] 开发任务处理接口
- [ ] 实现流程状态查询

#### Week 4: 审批功能完善
**Day 1-3: 审批页面开发**
- [ ] 创建待办任务列表页面
- [ ] 实现审批详情查看
- [ ] 开发审批操作界面
- [ ] 添加审批历史记录

**Day 4-5: 消息通知**
- [ ] 集成消息推送功能
- [ ] 实现审批状态通知
- [ ] 添加邮件/短信提醒
- [ ] 完善消息模板配置

### 🎯 第三阶段（1-2周）：高级功能与优化
**目标**: 完善系统功能，添加统计报表

#### Week 5: 高级功能
**Day 1-2: 文件上传功能**
- [ ] 集成j-upload组件
- [ ] 实现请假证明上传
- [ ] 添加文件预览功能
- [ ] 完善文件管理

**Day 3-5: 权限控制**
- [ ] 实现基于角色的权限控制
- [ ] 添加数据权限过滤
- [ ] 完善操作权限验证
- [ ] 集成用户认证

#### Week 6: 统计报表
**Day 1-3: 数据统计**
- [ ] 开发请假统计API
- [ ] 实现部门请假汇总
- [ ] 添加个人请假历史
- [ ] 创建请假趋势分析

**Day 4-5: 可视化报表**
- [ ] 使用ECharts创建图表
- [ ] 实现工作台Dashboard
- [ ] 添加数据导出功能
- [ ] 完善报表展示

## 🗂️ 详细开发指导

### 📁 项目文件结构
```
src/views/modules/leave/
├── application/                  # 请假申请模块
│   ├── index.vue                # 申请列表页(使用j-crud模板)
│   ├── form.vue                 # 申请表单页
│   └── detail.vue               # 申请详情页
├── approval/                    # 审批模块  
│   ├── todo.vue                 # 待办任务列表
│   ├── done.vue                 # 已办任务列表
│   └── approval-form.vue        # 审批表单
├── statistics/                  # 统计模块
│   ├── dashboard.vue            # 数据看板(使用工作台模板)
│   └── report.vue               # 统计报表
└── bpmn/                        # 流程设计
    ├── designer.vue             # 流程设计器
    └── viewer.vue               # 流程查看器
```

### 🎨 关键代码示例

#### 1. 请假申请列表页面
```vue
<template>
  <j-crud
    ref="crudRef"
    :queryMethod="queryLeaveApplications"
    :addMethod="addLeaveApplication"
    :updateMethod="updateLeaveApplication"
    :delMethod="deleteLeaveApplication"
    :columns="columns"
    :queryForm="queryForm"
    name="请假申请"
    showAddButton
  >
    <template #extendFormItems>
      <n-form-item label="申请日期">
        <n-date-picker
          v-model:formatted-value="queryForm.dateRange"
          type="daterange"
          clearable
        />
      </n-form-item>
      <n-form-item label="请假类型">
        <n-select
          v-model:value="queryForm.leaveType"
          :options="leaveTypeOptions"
          clearable
        />
      </n-form-item>
    </template>

    <template #extendButtons>
      <n-button type="primary" @click="showStatistics">
        统计报表
      </n-button>
    </template>
  </j-crud>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { CRUDColumn } from '@/types/comps/crud'
import { ContainerValueType, DataType } from '@/types/enums/enums'

const crudRef = ref()

const queryForm = reactive({
  applicantName: '',
  leaveType: '',
  status: '',
  dateRange: [],
  pageNum: 1,
  pageSize: 20
})

const columns = ref<CRUDColumn[]>([
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center',
    show: false
  },
  {
    title: '申请人',
    key: 'applicantName',
    width: 120,
    show: true,
    required: true,
    type: ContainerValueType.INPUT,
    placeholder: '请输入申请人姓名'
  },
  {
    title: '部门',
    key: 'deptName',
    width: 150,
    show: true,
    type: ContainerValueType.ORG,
    placeholder: '请选择部门'
  },
  {
    title: '请假类型',
    key: 'leaveType',
    width: 100,
    show: true,
    required: true,
    type: ContainerValueType.SELECT,
    selection: [
      { label: '事假', value: 'PERSONAL' },
      { label: '病假', value: 'SICK' },
      { label: '年假', value: 'ANNUAL' },
      { label: '调休', value: 'COMPENSATORY' }
    ],
    tagRender: true
  },
  {
    title: '开始日期',
    key: 'startDate',
    width: 120,
    show: true,
    required: true,
    type: ContainerValueType.DATE,
    dataType: DataType.DATE
  },
  {
    title: '结束日期',
    key: 'endDate',
    width: 120,
    show: true,
    required: true,
    type: ContainerValueType.DATE,
    dataType: DataType.DATE
  },
  {
    title: '请假天数',
    key: 'days',
    width: 100,
    align: 'center',
    show: true,
    required: true,
    type: ContainerValueType.NUMBER,
    dataType: DataType.NUMBER
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    show: false,
    tagRender: true,
    render: (row: any) => {
      const statusMap = {
        'DRAFT': { label: '草稿', type: 'default' },
        'PENDING': { label: '审批中', type: 'warning' },
        'APPROVED': { label: '已通过', type: 'success' },
        'REJECTED': { label: '已拒绝', type: 'error' }
      }
      const status = statusMap[row.status] || statusMap['DRAFT']
      return h('n-tag', { type: status.type }, status.label)
    }
  },
  {
    title: '申请时间',
    key: 'createdTime',
    width: 180,
    align: 'center',
    show: false
  }
])

// API方法实现
const queryLeaveApplications = async (params: any) => {
  // 调用后端查询接口
  return await api.leave.queryApplications(params)
}

const addLeaveApplication = async (data: any) => {
  // 调用后端新增接口
  return await api.leave.addApplication(data)
}

// ... 其他方法
</script>
```

#### 2. 后端Controller示例
```java
@RestController
@RequestMapping("/leave/application")
@Api(tags = "请假申请管理")
public class LeaveApplicationController {
    
    @Autowired
    private LeaveApplicationService leaveApplicationService;
    
    @GetMapping("/list")
    @ApiOperation("查询请假申请列表")
    public CommonResult<PageInfo<LeaveApplicationVO>> queryList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize,
            LeaveApplicationQueryDTO queryDTO) {
        
        PageHelper.startPage(pageNum, pageSize);
        List<LeaveApplicationVO> list = leaveApplicationService.queryList(queryDTO);
        PageInfo<LeaveApplicationVO> pageInfo = new PageInfo<>(list);
        
        return CommonResult.success(pageInfo);
    }
    
    @PostMapping("/add")
    @ApiOperation("提交请假申请")
    public CommonResult<String> addApplication(@RequestBody LeaveApplicationDTO dto) {
        String applicationId = leaveApplicationService.addApplication(dto);
        return CommonResult.success(applicationId);
    }
    
    @PostMapping("/start-process/{applicationId}")
    @ApiOperation("启动审批流程")
    public CommonResult<String> startApprovalProcess(@PathVariable String applicationId) {
        String processInstanceId = leaveApplicationService.startApprovalProcess(applicationId);
        return CommonResult.success(processInstanceId);
    }
}
```

#### 3. 工作流集成示例
```java
@Service
public class LeaveApplicationServiceImpl implements LeaveApplicationService {
    
    @Autowired
    private RuntimeService runtimeService;
    
    @Autowired
    private TaskService taskService;
    
    @Override
    @Transactional
    public String startApprovalProcess(String applicationId) {
        // 1. 获取请假申请信息
        LeaveApplication application = leaveApplicationMapper.selectById(applicationId);
        
        // 2. 启动工作流程
        Map<String, Object> variables = new HashMap<>();
        variables.put("applicantId", application.getApplicantId());
        variables.put("deptId", application.getDeptId());
        variables.put("days", application.getDays());
        variables.put("applicationId", applicationId);
        
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(
            "leave_approval_process", 
            applicationId, 
            variables
        );
        
        // 3. 更新申请状态
        application.setStatus("PENDING");
        application.setProcessInstanceId(processInstance.getId());
        leaveApplicationMapper.updateById(application);
        
        return processInstance.getId();
    }
    
    @Override
    public List<TaskVO> getMyTasks(String userId) {
        // 查询当前用户的待办任务
        List<Task> tasks = taskService.createTaskQuery()
            .taskAssignee(userId)
            .active()
            .orderByTaskCreateTime()
            .desc()
            .list();
            
        return tasks.stream()
            .map(this::convertToTaskVO)
            .collect(Collectors.toList());
    }
}
```

### 📊 工作台Dashboard示例
```vue
<template>
  <j-container :showHeader="false" @contentHeight="val => (contentHeight = val)">
    <template #content>
      <div class="leave-dashboard" :style="{ height: contentHeight, padding: '16px' }">
        <!-- 统计卡片 -->
        <n-grid :cols="4" :x-gap="16" :y-gap="16" class="stats-section">
          <n-grid-item v-for="stat in statsData" :key="stat.key">
            <div class="stat-card">
              <div class="stat-content">
                <div class="stat-icon">
                  <n-icon :size="32" :color="stat.iconColor">
                    <component :is="stat.icon" />
                  </n-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-title">{{ stat.title }}</div>
                  <div class="stat-value">{{ stat.value }}</div>
                </div>
              </div>
            </div>
          </n-grid-item>
        </n-grid>

        <!-- 图表展示 -->
        <n-grid :cols="24" :x-gap="16" :y-gap="16" class="charts-section">
          <n-grid-item :span="12">
            <n-card title="月度请假趋势">
              <div ref="trendChartRef" style="height: 300px;"></div>
            </n-card>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-card title="请假类型分布">
              <div ref="typeChartRef" style="height: 300px;"></div>
            </n-card>
          </n-grid-item>
        </n-grid>
      </div>
    </template>
  </j-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const contentHeight = ref<string>('')
const trendChartRef = ref()
const typeChartRef = ref()

const statsData = ref([
  {
    key: 'totalApplications',
    title: '本月申请',
    value: '25',
    icon: 'DocumentText',
    iconColor: '#409EFF'
  },
  {
    key: 'pendingApprovals',
    title: '待审批',
    value: '8',
    icon: 'Clock',
    iconColor: '#E6A23C'
  },
  {
    key: 'approvedCount',
    title: '已通过',
    value: '15',
    icon: 'CheckmarkCircle',
    iconColor: '#67C23A'
  },
  {
    key: 'rejectedCount',
    title: '已拒绝',
    value: '2',
    icon: 'CloseCircle',
    iconColor: '#F56C6C'
  }
])

onMounted(() => {
  initCharts()
  loadDashboardData()
})

const initCharts = () => {
  // 初始化趋势图
  const trendChart = echarts.init(trendChartRef.value)
  const trendOption = {
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [12, 18, 25, 20, 28, 25],
      type: 'line',
      smooth: true
    }]
  }
  trendChart.setOption(trendOption)

  // 初始化类型分布图
  const typeChart = echarts.init(typeChartRef.value)
  const typeOption = {
    series: [{
      type: 'pie',
      data: [
        { value: 10, name: '事假' },
        { value: 8, name: '病假' },
        { value: 5, name: '年假' },
        { value: 2, name: '调休' }
      ]
    }]
  }
  typeChart.setOption(typeOption)
}
</script>
```

## 📚 学习资源与参考文档

### 📖 必读文档
1. **AI开发规则-主要组件使用指南.md** - 核心组件使用方法
2. **CRUD类型定义使用说明.md** - CRUD组件详细配置
3. **项目README.md** - 项目整体介绍和环境搭建

### 🔧 开发工具推荐
- **IDE**: Visual Studio Code + Vue3、TypeScript插件
- **数据库**: pgAdmin 4 (PostgreSQL管理工具)
- **API测试**: Postman
- **版本控制**: Git + SourceTree
- **接口文档**: Swagger UI

### 📝 代码规范
- 遵循项目现有的代码风格
- 使用TypeScript进行类型定义
- 添加适当的注释说明
- 遵循Vue 3 Composition API最佳实践

## 🎯 验收标准

### ✅ 第一阶段验收
- [ ] 环境搭建成功，项目可以正常运行
- [ ] 数据库表设计合理，字段完整
- [ ] 基础CRUD功能完整，页面展示正常
- [ ] 代码规范符合项目要求

### ✅ 第二阶段验收  
- [ ] BPMN流程设计正确，逻辑清晰
- [ ] 工作流集成成功，审批流程正常
- [ ] 消息通知功能正常工作
- [ ] 权限控制有效

### ✅ 第三阶段验收
- [ ] 文件上传功能正常
- [ ] 统计报表数据准确
- [ ] 图表展示美观
- [ ] 整体系统功能完整，性能良好

## 🚀 进阶任务（可选）

### 🔥 高级功能扩展
1. **移动端适配** - 开发移动端请假申请H5页面
2. **消息推送** - 集成钉钉、企业微信推送
3. **数据导出** - Excel报表导出功能
4. **批量操作** - 批量审批、批量导入
5. **API文档** - 完善Swagger接口文档
6. **单元测试** - 编写前后端单元测试
7. **性能优化** - 数据库查询优化、前端性能优化

### 📈 技能提升方向
- 深入学习Vue 3高级特性
- 掌握Spring Boot微服务开发
- 学习工作流引擎原理
- 了解数据库优化技巧
- 掌握前端工程化技能

## 🎊 项目总结

通过这个Demo项目，实习生将获得：
- 🎯 **全栈开发经验** - 前后端分离开发实践
- 🔧 **企业级技术栈** - Vue3、Spring Boot、工作流引擎
- 📊 **业务理解能力** - 真实业务场景的需求分析
- 🚀 **工程化思维** - 代码规范、版本控制、团队协作
- 💡 **解决问题能力** - 独立分析和解决技术问题

这个项目既能让实习生快速上手现有技术栈，又能通过完整的开发流程培养工程化思维，是一个理想的学习项目！🌟 