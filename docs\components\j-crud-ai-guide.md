 # J-CRUD 组件 AI 编写指南

## 概述 🤖

本文档旨在为 AI 助手和开发者提供使用 `j-crud` 组件的最佳实践和代码生成指南。通过遵循这些准则，可以快速生成高质量、规范的 CRUD 页面代码。

## AI 编写准则 📝

### 1. 基础模板结构

当生成 CRUD 页面时，应始终遵循以下基础模板：

```vue
<template>
  <j-crud
    ref="crudRef"
    :columns="columns"
    :queryMethod="queryMethod"
    :addMethod="addMethod"
    :updateMethod="updateMethod"
    :delMethod="delMethod"
    :name="moduleName"
    v-model:queryForm="queryForm"
    @queryComplete="onQueryComplete"
    @success="onSuccess"
  />
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ContainerValueType } from '@jtypes'
import type { CRUDColumnInterface } from '@jtypes'
import * as api from './api' // 根据实际API路径调整

// 定义组件引用
const crudRef = ref()

// 定义模块名称
const moduleName = '数据管理' // 根据具体业务调整

// 定义查询表单
const queryForm = reactive({
  // 根据业务需求添加查询字段
  pageNum: 1,
  pageSize: 20
})

// 定义表格列
const columns = ref<CRUDColumnInterface[]>([
  // 根据业务需求定义列
])

// API 方法定义
const queryMethod = async (params: any) => {
  return await api.getList(params)
}

const addMethod = async (data: any) => {
  return await api.add(data)
}

const updateMethod = async (data: any) => {
  return await api.update(data)
}

const delMethod = async (row: any) => {
  return await api.delete(row.id)
}

// 事件处理
const onQueryComplete = (data: any[]) => {
  console.log('查询完成', data)
}

const onSuccess = (data: any) => {
  console.log('操作成功', data)
}
</script>
```

### 2. 列配置生成规则

#### 2.1 基础字段类型映射

AI 应根据字段类型自动选择合适的 ContainerValueType：

```typescript
// 字段类型 -> ContainerValueType 映射表
const fieldTypeMapping = {
  'string': ContainerValueType.INPUT,
  'text': ContainerValueType.TEXTAREA,
  'number': ContainerValueType.NUMBER,
  'integer': ContainerValueType.NUMBER,
  'decimal': ContainerValueType.NUMBER,
  'date': ContainerValueType.DATE,
  'datetime': ContainerValueType.DATE_TIME,
  'boolean': ContainerValueType.SWITCH,
  'select': ContainerValueType.SELECT,
  'multiSelect': ContainerValueType.SELECT, // 需要设置 multiple: true
  'treeSelect': ContainerValueType.TREE_SELECT,
  'upload': ContainerValueType.UPLOAD,
  'cascader': ContainerValueType.CASCADER,
}
```

#### 2.2 常见业务字段模板

AI 遇到以下字段名时，应自动应用对应的配置模板：

```typescript
// 常见字段配置模板
const commonFieldTemplates = {
  id: {
    key: 'id',
    title: 'ID',
    width: 80,
    tableColumnShow: true,
    show: false, // 通常不在表单中显示
    align: 'center'
  },
  
  name: {
    key: 'name',
    title: '名称',
    type: ContainerValueType.INPUT,
    required: true,
    width: 120
  },
  
  status: {
    key: 'status',
    title: '状态',
    type: ContainerValueType.SELECT,
    dictType: 'status', // 需要根据具体业务调整
    tagRender: true,
    width: 100,
    align: 'center'
  },
  
  createTime: {
    key: 'createTime',
    title: '创建时间',
    type: ContainerValueType.DATE_TIME,
    width: 180,
    show: false, // 通常不在表单中显示
    sorter: 'default'
  },
  
  updateTime: {
    key: 'updateTime',
    title: '更新时间',
    type: ContainerValueType.DATE_TIME,
    width: 180,
    show: false,
    tableColumnShow: false // 通常不在表格中显示
  },
  
  remark: {
    key: 'remark',
    title: '备注',
    type: ContainerValueType.TEXTAREA,
    width: 200,
    tableColumnShow: false // 通常只在表单中显示
  }
}
```

### 3. API 方法生成规则

#### 3.1 标准 API 结构

```typescript
// api.ts 文件结构模板
import { http } from '@/utils/request'

// 查询列表 - 分页
export const getList = (params: any) => {
  return http.get('/api/module/list', { params })
}

// 查询列表 - 不分页
export const getAll = (params: any) => {
  return http.get('/api/module/all', { params })
}

// 新增
export const add = (data: any) => {
  return http.post('/api/module', data)
}

// 更新
export const update = (data: any) => {
  return http.put('/api/module', data)
}

// 删除
export const remove = (id: string | number) => {
  return http.delete(`/api/module/${id}`)
}

// 批量删除
export const batchRemove = (ids: (string | number)[]) => {
  return http.delete('/api/module/batch', { data: { ids } })
}

// 获取详情
export const getDetail = (id: string | number) => {
  return http.get(`/api/module/${id}`)
}
```

#### 3.2 错误处理模板

```typescript
// 带错误处理的 API 方法模板
const queryMethod = async (params: any) => {
  try {
    const result = await api.getList(params)
    return result
  } catch (error) {
    console.error('查询失败:', error)
    window.$message.error('数据加载失败，请重试')
    throw error
  }
}

const addMethod = async (data: any) => {
  try {
    const result = await api.add(data)
    window.$message.success('新增成功')
    return result
  } catch (error) {
    console.error('新增失败:', error)
    window.$message.error('新增失败，请检查数据')
    throw error
  }
}
```

### 4. 高级功能模板

#### 4.1 Tab 页签模式

```typescript
// 当需要 Tab 页签时使用此模板
const tabs = ref([
  {
    name: 'all',
    tab: '全部',
    columns: baseColumns.value
  },
  {
    name: 'active',
    tab: '启用',
    useBadge: true,
    badge: 0,
    columns: baseColumns.value,
    tabChange: async (tab) => {
      queryForm.status = '1'
    }
  },
  {
    name: 'inactive',
    tab: '禁用',
    useBadge: true,
    badge: 0,
    columns: baseColumns.value,
    tabChange: async (tab) => {
      queryForm.status = '0'
    }
  }
])
```

#### 4.2 树形结构模式

```typescript
// 树形结构配置模板
const treeConfig = {
  id: 'id',
  parentId: 'parentId'
}

// 树形结构的列配置应包含父级选择
const treeColumns = [
  {
    key: 'name',
    title: '名称',
    type: ContainerValueType.INPUT,
    required: true
  },
  {
    key: 'parentId',
    title: '上级',
    type: ContainerValueType.TREE_SELECT,
    show: true
  },
  {
    key: 'sort',
    title: '排序',
    type: ContainerValueType.NUMBER,
    defaultValue: 0
  }
]
```

#### 4.3 导出功能配置

```typescript
// 导出功能配置模板
const exportConfig = ref({
  excelName: `${moduleName}数据导出`,
  dontExport: ['id', 'createTime', 'updateTime'], // 通常不导出的字段
  isComplex: false
})
```

### 5. TypeScript 类型定义

AI 应为每个模块生成相应的类型定义：

```typescript
// types.ts 文件模板
export interface ModuleEntity {
  id?: string | number
  name: string
  status: string
  createTime?: string
  updateTime?: string
  remark?: string
}

export interface ModuleQueryParams {
  name?: string
  status?: string
  dateRange?: [string, string]
  pageNum: number
  pageSize: number
}

export interface ModuleFormData extends Omit<ModuleEntity, 'id' | 'createTime' | 'updateTime'> {
  id?: string | number
}
```

## 代码生成检查清单 ✅

AI 在生成 CRUD 页面代码时，应确保包含以下要素：

### 必需项
- [ ] 正确的 template 结构，包含 j-crud 组件
- [ ] 完整的 script setup 语法
- [ ] 基础的 CRUD 方法定义（queryMethod, addMethod, updateMethod, delMethod）
- [ ] 合理的列配置（columns）
- [ ] 查询表单定义（queryForm）
- [ ] 正确的 TypeScript 类型导入

### 推荐项
- [ ] 错误处理逻辑
- [ ] 加载状态管理
- [ ] 适当的默认值设置
- [ ] 权限控制考虑
- [ ] 响应式数据声明

### 高级项
- [ ] 自定义验证规则
- [ ] 联动表单项
- [ ] 自定义操作按钮
- [ ] 导出功能配置
- [ ] Tab 页签（如果需要）

## 常见场景代码模板 🎯

### 1. 简单列表页

```vue
<template>
  <j-crud
    ref="crudRef"
    :columns="columns"
    :queryMethod="queryMethod"
    :addMethod="addMethod"
    :updateMethod="updateMethod"
    :delMethod="delMethod"
    name="用户管理"
    v-model:queryForm="queryForm"
    :showExport="true"
    :exportConfig="exportConfig"
  />
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ContainerValueType } from '@jtypes'
import * as api from './api'

const crudRef = ref()

const queryForm = reactive({
  name: '',
  status: '',
  pageNum: 1,
  pageSize: 20
})

const columns = ref([
  {
    key: 'id',
    title: 'ID',
    width: 80,
    show: false
  },
  {
    key: 'name',
    title: '姓名',
    type: ContainerValueType.INPUT,
    required: true,
    width: 120
  },
  {
    key: 'email',
    title: '邮箱',
    type: ContainerValueType.INPUT,
    width: 200
  },
  {
    key: 'status',
    title: '状态',
    type: ContainerValueType.SELECT,
    dictType: 'user_status',
    tagRender: true,
    width: 100
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: 180,
    show: false
  }
])

const exportConfig = ref({
  excelName: '用户数据导出',
  dontExport: ['id']
})

const queryMethod = async (params: any) => await api.getUserList(params)
const addMethod = async (data: any) => await api.addUser(data)
const updateMethod = async (data: any) => await api.updateUser(data)
const delMethod = async (row: any) => await api.deleteUser(row.id)
</script>
```

### 2. 带状态筛选的 Tab 页面

```vue
<template>
  <j-crud
    ref="crudRef"
    :columns="columns"
    :tabs="tabs"
    :queryMethod="queryMethod"
    :addMethod="addMethod"
    :updateMethod="updateMethod"
    :delMethod="delMethod"
    name="订单管理"
    v-model:queryForm="queryForm"
    :enableRouteTab="true"
  />
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ContainerValueType } from '@jtypes'
import * as api from './api'

const crudRef = ref()

const queryForm = reactive({
  orderNo: '',
  status: '',
  pageNum: 1,
  pageSize: 20
})

const baseColumns = ref([
  {
    key: 'orderNo',
    title: '订单号',
    type: ContainerValueType.INPUT,
    width: 150
  },
  {
    key: 'customerName',
    title: '客户名称',
    type: ContainerValueType.INPUT,
    width: 120
  },
  {
    key: 'amount',
    title: '金额',
    type: ContainerValueType.NUMBER,
    width: 100,
    summary: true,
    summaryFormat: (value) => `￥${value.toFixed(2)}`
  },
  {
    key: 'status',
    title: '状态',
    type: ContainerValueType.SELECT,
    dictType: 'order_status',
    tagRender: true,
    width: 100
  }
])

const tabs = ref([
  {
    name: 'all',
    tab: '全部订单',
    columns: baseColumns.value
  },
  {
    name: 'pending',
    tab: '待处理',
    useBadge: true,
    badge: 0,
    columns: baseColumns.value,
    tabChange: async () => { queryForm.status = 'pending' }
  },
  {
    name: 'completed',
    tab: '已完成',
    useBadge: true,
    badge: 0,
    columns: baseColumns.value,
    tabChange: async () => { queryForm.status = 'completed' }
  }
])

const queryMethod = async (params: any) => await api.getOrderList(params)
const addMethod = async (data: any) => await api.addOrder(data)
const updateMethod = async (data: any) => await api.updateOrder(data)
const delMethod = async (row: any) => await api.deleteOrder(row.id)
</script>
```

### 3. 树形结构管理页面

```vue
<template>
  <j-crud
    ref="crudRef"
    :columns="columns"
    :queryMethod="queryMethod"
    :addMethod="addMethod"
    :updateMethod="updateMethod"
    :delMethod="delMethod"
    :treeNode="treeConfig"
    :defaultExpandAll="true"
    :paging="false"
    name="部门管理"
    v-model:queryForm="queryForm"
  />
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ContainerValueType } from '@jtypes'
import * as api from './api'

const crudRef = ref()

const treeConfig = {
  id: 'id',
  parentId: 'parentId'
}

const queryForm = reactive({
  name: '',
  status: ''
})

const columns = ref([
  {
    key: 'name',
    title: '部门名称',
    type: ContainerValueType.INPUT,
    required: true,
    width: 200
  },
  {
    key: 'parentId',
    title: '上级部门',
    type: ContainerValueType.TREE_SELECT,
    width: 150,
    show: true
  },
  {
    key: 'sort',
    title: '排序',
    type: ContainerValueType.NUMBER,
    width: 80,
    defaultValue: 0
  },
  {
    key: 'status',
    title: '状态',
    type: ContainerValueType.SELECT,
    dictType: 'dept_status',
    tagRender: true,
    width: 100
  }
])

const queryMethod = async (params: any) => await api.getDeptList(params)
const addMethod = async (data: any) => await api.addDept(data)
const updateMethod = async (data: any) => await api.updateDept(data)
const delMethod = async (row: any) => await api.deleteDept(row.id)
</script>
```

## AI 优化建议 🔧

### 1. 智能字段推断

AI 应根据字段名称智能推断配置：

```typescript
const fieldInference = {
  // 以 Time/Date 结尾的字段
  /.*Time$|.*Date$/: { type: ContainerValueType.DATE_TIME, show: false },
  
  // 以 Status/State 结尾的字段
  /.*Status$|.*State$/: { 
    type: ContainerValueType.SELECT, 
    dictType: 'status',
    tagRender: true 
  },
  
  // 以 Id 结尾的字段（除主键外）
  /^(?!id$).*Id$/: { type: ContainerValueType.SELECT },
  
  // 以 Name/Title 结尾的字段
  /.*Name$|.*Title$/: { type: ContainerValueType.INPUT, required: true },
  
  // 描述类字段
  /.*Desc$|.*Description$|.*Remark$/: { 
    type: ContainerValueType.TEXTAREA,
    tableColumnShow: false 
  }
}
```

### 2. 自动权限集成

```typescript
// 自动添加权限控制
const needsPermission = true // 根据项目需求设置

if (needsPermission) {
  // 在模板中自动添加权限相关配置
  template += `
    :showConAddButton="hasPermission('add')"
    @add="checkPermission('add', handleAdd)"
    @edit="checkPermission('edit', handleEdit)"
  `
}
```

### 3. 响应式优化

```typescript
// 自动添加响应式优化
const useVirtualScroll = dataSize > 100
const useQuickQuery = queryFields.length > 2

template += `
  :virtualScroll="${useVirtualScroll}"
  :quickQueryConfig="{ enable: ${useQuickQuery} }"
`
```

## 代码质量检查 🔍

AI 生成代码后应进行以下检查：

### 1. 语法检查
- TypeScript 类型正确性
- Vue 3 Composition API 语法正确性
- 导入语句完整性

### 2. 业务逻辑检查
- 必填字段合理性
- 字段类型匹配度
- 默认值合理性

### 3. 性能检查
- 大数据集虚拟滚动
- 不必要的响应式数据
- API 调用优化

### 4. 用户体验检查
- 加载状态提示
- 错误处理完整性
- 操作反馈及时性

## 扩展功能模板 🚀

### 1. 批量操作

```typescript
// 批量操作模板
const selectedRows = ref([])

const handleBatchDelete = async () => {
  if (!selectedRows.value.length) {
    window.$message.warning('请选择要删除的数据')
    return
  }
  
  try {
    await api.batchDelete(selectedRows.value.map(row => row.id))
    window.$message.success('批量删除成功')
    crudRef.value.reload()
    selectedRows.value = []
  } catch (error) {
    window.$message.error('批量删除失败')
  }
}
```

### 2. 表单联动

```typescript
// 表单联动模板
const columns = ref([
  {
    key: 'province',
    title: '省份',
    type: ContainerValueType.SELECT,
    selection: provinceOptions,
    callBack: (value) => {
      // 省份变化时，更新城市选项
      updateCityOptions(value)
    }
  },
  {
    key: 'city',
    title: '城市',
    type: ContainerValueType.SELECT,
    selection: cityOptions
  }
])
```

### 3. 自定义验证

```typescript
// 自定义验证模板
const columns = ref([
  {
    key: 'email',
    title: '邮箱',
    type: ContainerValueType.INPUT,
    required: true,
    validator: (rule, value) => {
      if (!value) return new Error('请输入邮箱')
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return new Error('邮箱格式不正确')
      }
      return true
    }
  }
])
```

--

通过遵循本指南，AI 助手可以生成更加规范、高质量的 j-crud 组件代码，提高开发效率和代码维护性。 🎉