<template>
  <div class="p-4 space-y-4">
    <n-card title="单列布局测试" size="small">
      <template #header-extra>
        <n-space>
          <n-tag :type="isMobileDevice ? 'success' : 'info'">
            {{ isMobileDevice ? '移动端' : 'PC端' }}
          </n-tag>
          <n-button size="small" @click="toggleColumns">
            切换列数: {{ cardColumns }}列
          </n-button>
        </n-space>
      </template>

      <!-- 自适应数据表格 - 单列布局 -->
      <adaptive-data-table
        :data="tableData"
        :columns="tableColumns"
        :loading="loading"
        :pagination="false"
        row-key="id"
        mobile-title="员工详情列表"
        :card-columns="cardColumns"
        :show-actions="true"
        @row-click="handleRowClick"
      >
        <!-- 操作按钮插槽 -->
        <template #actions="{ row }">
          <n-space :size="8">
            <n-button size="small" type="primary">
              查看详情
            </n-button>
            <n-button size="small" type="info">
              编辑
            </n-button>
            <n-button size="small" type="warning">
              联系
            </n-button>
          </n-space>
        </template>
      </adaptive-data-table>
    </n-card>

    <!-- 布局说明 -->
    <n-card title="布局说明" size="small">
      <n-descriptions :column="1" bordered>
        <n-descriptions-item label="当前列数">
          {{ cardColumns }} 列
        </n-descriptions-item>
        <n-descriptions-item label="卡片内边距">
          {{ cardColumns === 1 ? '20px (更大)' : '16px (标准)' }}
        </n-descriptions-item>
        <n-descriptions-item label="网格间距">
          {{ cardColumns === 1 ? '0px (无间距)' : '16px (标准间距)' }}
        </n-descriptions-item>
        <n-descriptions-item label="字体大小">
          {{ cardColumns === 1 ? '更大字体' : '标准字体' }}
        </n-descriptions-item>
        <n-descriptions-item label="最小高度">
          {{ cardColumns === 1 ? '140px' : '120px' }}
        </n-descriptions-item>
      </n-descriptions>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, h } from 'vue'
import { 
  NCard, 
  NSpace, 
  NButton, 
  NTag, 
  NDescriptions, 
  NDescriptionsItem,
  useMessage 
} from 'naive-ui'
import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'

// 移动端检测
const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice') || ref(false)
const message = useMessage()

// 响应式数据
const loading = ref(false)
const cardColumns = ref(1) // 默认单列

// 模拟数据 - 更丰富的员工信息
const tableData = ref([
  {
    id: 1,
    name: '张三',
    age: 28,
    department: '技术部',
    position: '高级前端工程师',
    salary: 25000,
    status: '在职',
    joinDate: '2023-01-15',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
    address: '北京市朝阳区望京SOHO',
    skills: 'Vue.js, React, TypeScript',
    experience: '5年'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    department: '产品部',
    position: '资深产品经理',
    salary: 28000,
    status: '在职',
    joinDate: '2022-08-20',
    email: '<EMAIL>',
    phone: '13800138002',
    avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
    address: '上海市浦东新区陆家嘴',
    skills: '产品设计, 用户研究, 数据分析',
    experience: '7年'
  },
  {
    id: 3,
    name: '王五',
    age: 26,
    department: '设计部',
    position: 'UI/UX设计师',
    salary: 18000,
    status: '试用期',
    joinDate: '2023-06-01',
    email: '<EMAIL>',
    phone: '13800138003',
    avatar: 'https://avatars.githubusercontent.com/u/3?v=4',
    address: '深圳市南山区科技园',
    skills: 'Figma, Sketch, Principle',
    experience: '3年'
  }
])

// 列配置 - 适合单列显示的详细信息
const tableColumns = ref([
  {
    key: 'name',
    title: '姓名',
    width: 100,
    mobileTitle: true,  // 移动端主标题
    mobileOrder: 1,
    render: (row: any) => {
      return h('div', { class: 'flex items-center space-x-3' }, [
        h('img', {
          src: row.avatar,
          alt: row.name,
          class: 'w-12 h-12 rounded-full border-2 border-blue-100'
        }),
        h('div', [
          h('div', { class: 'font-bold text-lg' }, row.name),
          h('div', { class: 'text-sm text-gray-500' }, `工号: ${row.id}`)
        ])
      ])
    }
  },
  {
    key: 'position',
    title: '职位',
    width: 120,
    mobileSubtitle: true,  // 移动端副标题
    mobileOrder: 2,
    render: (row: any) => {
      return h('div', { class: 'text-blue-600 font-medium' }, row.position)
    }
  },
  {
    key: 'status',
    title: '状态',
    width: 80,
    mobilePosition: 'header',  // 移动端头部显示
    mobileOrder: 3,
    render: (row: any) => {
      const type = row.status === '在职' ? 'success' : 'warning'
      return h(NTag, { type, size: 'medium' }, () => row.status)
    }
  },
  {
    key: 'department',
    title: '部门',
    width: 100,
    mobileOrder: 4
  },
  {
    key: 'experience',
    title: '工作经验',
    width: 100,
    mobileOrder: 5
  },
  {
    key: 'salary',
    title: '薪资',
    width: 100,
    mobileOrder: 6,
    render: (row: any) => {
      return h('div', { class: 'text-green-600 font-bold text-lg' }, 
        `¥${row.salary.toLocaleString()}`
      )
    }
  },
  {
    key: 'skills',
    title: '技能',
    width: 200,
    mobileOrder: 7,
    render: (row: any) => {
      return h('div', { class: 'text-sm text-gray-700' }, row.skills)
    }
  },
  {
    key: 'address',
    title: '工作地点',
    width: 200,
    mobileOrder: 8,
    render: (row: any) => {
      return h('div', { class: 'text-sm text-gray-600' }, row.address)
    }
  },
  {
    key: 'joinDate',
    title: '入职日期',
    width: 120,
    mobilePosition: 'footer',  // 移动端底部显示
    mobileOrder: 9,
    render: (row: any) => {
      return h('div', { class: 'text-gray-500 text-sm' }, 
        `入职: ${row.joinDate}`
      )
    }
  },
  {
    key: 'contact',
    title: '联系方式',
    width: 200,
    mobilePosition: 'footer',
    mobileOrder: 10,
    render: (row: any) => {
      return h('div', { class: 'text-gray-500 text-sm' }, 
        `${row.phone} | ${row.email}`
      )
    }
  }
])

// 事件处理
const handleRowClick = (row: any, index: number) => {
  message.info(`查看 ${row.name} 的详细信息`)
}

const toggleColumns = () => {
  cardColumns.value = cardColumns.value === 1 ? 2 : cardColumns.value === 2 ? 3 : 1
  message.info(`切换到 ${cardColumns.value} 列布局`)
}
</script>

<style scoped lang="less">
// 测试页面样式
</style>
