# 移动端组件 defineModel 修复

## 问题描述 🐛

在移动端 CRUD 组件中遇到了 Vue 3 双向绑定的错误：

```
v-model cannot be used on a prop, because local prop bindings are not writable.
Use a v-bind binding combined with a v-on listener that emits update:x event instead.
```

这个错误是因为在 Vue 3 中，不能直接在 prop 上使用 `v-model`，需要使用 `defineModel` 来正确处理双向绑定。

## 解决方案 ✅

### 1. 使用 defineModel 替代 props + emit

#### 修复前的代码
```typescript
// Props
interface Props {
  showModal: boolean
  // ... 其他属性
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:showModal': [value: boolean]
  // ... 其他事件
}>()
```

```vue
<template>
  <n-modal v-model:show="showModal">
    <!-- 内容 -->
  </n-modal>
</template>
```

#### 修复后的代码
```typescript
// Props (移除 showModal)
interface Props {
  title: string
  // ... 其他属性，不包含 showModal
}

const props = defineProps<Props>()

// 使用 defineModel 处理双向绑定
const modalVisible = defineModel<boolean>('showModal', { default: false })

// Emits (移除 update:showModal)
const emit = defineEmits<{
  close: []
  // ... 其他事件，不包含 update:showModal
}>()
```

```vue
<template>
  <n-modal v-model:show="modalVisible">
    <!-- 内容 -->
  </n-modal>
</template>
```

### 2. 修复的组件

#### MobileModal 组件
**文件**: `src/components/common/crud/components/mobileModal.vue`

**主要修改**:
- 移除 `showModal` prop
- 添加 `defineModel<boolean>('showModal', { default: false })`
- 移除 `'update:showModal'` emit
- 将模板中的 `showModal` 替换为 `modalVisible`

#### MobileDrawer 组件
**文件**: `src/components/common/crud/components/mobileDrawer.vue`

**主要修改**:
- 移除 `showModal` prop
- 添加 `defineModel<boolean>('showModal', { default: false })`
- 移除 `'update:showModal'` emit
- 将模板中的 `showModal` 替换为 `drawerVisible`

### 3. 父组件调用方式更新

#### 主 CRUD 组件
**文件**: `src/components/common/crud/index.vue`

**修改前**:
```vue
<MobileModal
  :show-modal="showModal"
  <!-- 其他属性 -->
/>
```

**修改后**:
```vue
<MobileModal
  v-model:show-modal="showModal"
  <!-- 其他属性 -->
/>
```

#### 测试页面
**文件**: `src/views/test/mobile-crud-test.vue`

同样的修改，将 `:show-modal="testModal"` 改为 `v-model:show-modal="testModal"`

## 技术细节 🔧

### defineModel 的优势

1. **简化代码**: 不需要手动处理 props 和 emit
2. **类型安全**: TypeScript 支持更好
3. **Vue 3 推荐**: 官方推荐的双向绑定方式
4. **自动处理**: 自动生成 `update:propName` 事件

### defineModel 语法

```typescript
// 基本用法
const modelValue = defineModel<string>()

// 带默认值
const modelValue = defineModel<string>({ default: '' })

// 自定义属性名
const visible = defineModel<boolean>('show', { default: false })

// 带验证
const count = defineModel<number>('count', {
  default: 0,
  validator: (value) => value >= 0
})
```

### 在父组件中使用

```vue
<!-- 基本 v-model -->
<MyComponent v-model="value" />

<!-- 自定义属性名 -->
<MyComponent v-model:show="visible" />

<!-- 多个 v-model -->
<MyComponent 
  v-model:title="title"
  v-model:visible="visible"
  v-model:data="data"
/>
```

## 修复验证 ✅

### 1. 编译检查
- ✅ 移除了 TypeScript 类型错误
- ✅ 移除了 Vue 模板编译警告
- ✅ 组件正常导入和注册

### 2. 功能测试
- ✅ 模态框正常显示和隐藏
- ✅ 抽屉正常显示和隐藏
- ✅ 双向绑定正常工作
- ✅ 表单提交和重置功能正常

### 3. 兼容性测试
- ✅ 桌面端功能不受影响
- ✅ 移动端自动切换正常
- ✅ 所有事件处理正常

## 最佳实践 💡

### 1. 使用 defineModel 的场景
- 组件需要双向绑定时
- 替代 `props + emit('update:propName')` 模式
- 简化父子组件通信

### 2. 命名规范
```typescript
// 推荐：使用描述性的变量名
const modalVisible = defineModel<boolean>('show')
const drawerVisible = defineModel<boolean>('visible')
const formData = defineModel<object>('data')

// 避免：使用 prop 名称作为变量名
const show = defineModel<boolean>('show') // 可能造成混淆
```

### 3. 类型定义
```typescript
// 推荐：明确指定类型
const count = defineModel<number>('count', { default: 0 })

// 推荐：复杂类型使用接口
interface FormData {
  name: string
  email: string
}
const formData = defineModel<FormData>('data')
```

### 4. 默认值设置
```typescript
// 推荐：为所有 defineModel 设置合理的默认值
const visible = defineModel<boolean>('show', { default: false })
const title = defineModel<string>('title', { default: '' })
const items = defineModel<any[]>('items', { default: () => [] })
```

## 相关文档 📚

- [Vue 3 defineModel 官方文档](https://vuejs.org/api/sfc-script-setup.html#definemodel)
- [Vue 3 组件 v-model 指南](https://vuejs.org/guide/components/v-model.html)
- [TypeScript 与 Vue 3 组合式 API](https://vuejs.org/guide/typescript/composition-api.html)

## 总结 🎉

通过使用 Vue 3 的 `defineModel` API，我们成功修复了移动端组件的双向绑定问题：

✅ **代码更简洁**: 减少了 props 和 emit 的样板代码  
✅ **类型更安全**: TypeScript 支持更好  
✅ **维护更容易**: 双向绑定逻辑集中管理  
✅ **性能更好**: Vue 3 内部优化  

这次修复不仅解决了当前的错误，还提升了代码质量和开发体验，为后续的组件开发提供了最佳实践参考。
