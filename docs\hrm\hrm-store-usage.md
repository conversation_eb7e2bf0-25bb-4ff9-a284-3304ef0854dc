# HRM Store 使用指南

## 📋 概述

HRM Store 提供了人员和科室数据的统一管理，采用一次性加载全部数据的策略，在前端进行各种操作，提高性能和用户体验。

## 🚀 快速开始

### 基本使用

```typescript
import { useHrmStore } from '@/store/hrm'

// 在组件中使用
const hrmStore = useHrmStore()

// 初始化数据（通常在应用启动时调用）
await hrmStore.initializeHrmData()
```

### 单独使用人员或科室Store

```typescript
import { useHrmEmployeeStore, useHrmOrganizationStore } from '@/store/hrm'

const employeeStore = useHrmEmployeeStore()
const organizationStore = useHrmOrganizationStore()
```

## 👥 人员数据操作

### 获取人员数据

```typescript
// 获取全部人员
const allEmployees = await hrmStore.getAllEmployees()

// 根据工号获取人员
const employee = await hrmStore.getEmployeeByCode('E001')

// 根据工号数组获取人员
const employees = await hrmStore.getEmployeesByCodes(['E001', 'E002'])

// 根据科室获取人员
const deptEmployees = await hrmStore.getEmployeesByDept('ORG001')

// 根据多个科室获取人员
const multiDeptEmployees = await hrmStore.getEmployeesByDepts(['ORG001', 'ORG002'])
```

### 搜索人员

```typescript
// 搜索人员（支持姓名、工号模糊匹配）
const searchResults = await hrmStore.searchEmployees('张三')

// 根据职工类型获取人员
const typeEmployees = await hrmStore.getEmployeesByType('医生')

// 高级查询
const queryResults = await hrmStore.queryEmployees({
  empCodeOrEmpName: '张',
  orgIds: ['ORG001', 'ORG002'],
  restrictedEmpType: '医生,护士'
})
```

## 🏥 科室数据操作

### 获取科室数据

```typescript
// 获取全部科室
const allOrgs = await hrmStore.getAllOrganizations()

// 根据ID获取科室
const org = await hrmStore.getOrganizationById('ORG001')

// 根据ID数组获取科室
const orgs = await hrmStore.getOrganizationsByIds(['ORG001', 'ORG002'])

// 获取科室树形数据
const orgTree = await hrmStore.getOrganizationTree()

// 获取子科室
const childOrgs = await hrmStore.getChildOrganizations('ORG001')
```

### 搜索科室

```typescript
// 搜索科室（支持科室名称、代码模糊匹配）
const searchResults = await hrmStore.searchOrganizations('内科')

// 根据科室类型获取科室
const typeOrgs = await hrmStore.getOrganizationsByType(['临床科室'])

// 高级查询
const queryResults = await hrmStore.queryOrganizations({
  activeFlag: '1',
  keyword: '内科',
  orgTypes: ['临床科室']
})
```

## 🔄 组合查询

### 人员与科室关联查询

```typescript
// 根据科室名称获取该科室的所有人员
const employees = await hrmStore.getEmployeesByOrgName('内科')

// 获取人员的完整科室信息
const employeeWithOrg = await hrmStore.getEmployeeWithOrgInfo('E001')

// 获取科室及其所有人员信息
const orgWithEmployees = await hrmStore.getOrgWithEmployees('ORG001')
```

## 📊 状态管理

### 加载状态

```typescript
// 检查加载状态
console.log('正在加载:', hrmStore.isLoading)
console.log('已全部加载:', hrmStore.isAllLoaded)

// 获取统计信息
const stats = hrmStore.getHrmStats
console.log('人员总数:', stats.totalEmployees)
console.log('科室总数:', stats.totalOrganizations)
```

### 缓存管理

```typescript
// 刷新所有数据
await hrmStore.refreshAllData()

// 清空缓存
hrmStore.clearAllCache()

// 检查缓存是否有效
const isValid = hrmStore.isAllCacheValid()
```

## 🎯 在组件中使用

### Vue 组件示例

```vue
<template>
  <div>
    <n-select
      v-model:value="selectedEmployee"
      :options="employeeOptions"
      :loading="hrmStore.isLoading"
      filterable
      placeholder="选择人员"
    />
    
    <n-tree-select
      v-model:value="selectedOrg"
      :options="orgTree"
      :loading="hrmStore.isLoading"
      placeholder="选择科室"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useHrmStore } from '@/store/hrm'

const hrmStore = useHrmStore()
const selectedEmployee = ref('')
const selectedOrg = ref('')

// 计算属性
const employeeOptions = computed(() => 
  hrmStore.employeeStore.employeeOptions
)

const orgTree = computed(() => 
  hrmStore.organizationStore.activeOrganizationTree
)

// 初始化
onMounted(async () => {
  await hrmStore.initializeHrmData()
})
</script>
```

### 在业务逻辑中使用

```typescript
// composables/useEmployeeSelection.ts
import { ref, computed } from 'vue'
import { useHrmStore } from '@/store/hrm'

export function useEmployeeSelection() {
  const hrmStore = useHrmStore()
  const selectedDept = ref('')
  const searchKeyword = ref('')

  // 根据选择的科室和搜索关键词过滤人员
  const filteredEmployees = computed(async () => {
    let employees = await hrmStore.getAllEmployees()
    
    if (selectedDept.value) {
      employees = await hrmStore.getEmployeesByDept(selectedDept.value)
    }
    
    if (searchKeyword.value) {
      employees = await hrmStore.searchEmployees(searchKeyword.value)
    }
    
    return employees
  })

  return {
    selectedDept,
    searchKeyword,
    filteredEmployees,
    isLoading: hrmStore.isLoading
  }
}
```

## ⚡ 性能优化

### 缓存策略

- 数据缓存时间：5分钟
- 一次性加载全部数据，避免重复请求
- 支持手动刷新和清空缓存

### 最佳实践

1. **应用启动时初始化**：在应用启动时调用 `initializeHrmData()`
2. **避免重复加载**：Store会自动管理缓存，避免重复请求
3. **按需使用**：可以单独使用人员或科室Store
4. **错误处理**：所有异步方法都会抛出错误，需要适当处理

```typescript
// main.ts 或 App.vue
import { useHrmStore } from '@/store/hrm'

const app = createApp(App)

// 应用启动时初始化HRM数据
const hrmStore = useHrmStore()
hrmStore.initializeHrmData().catch(error => {
  console.error('HRM数据初始化失败:', error)
})
```

## 🔧 配置说明

### 查询参数

#### 人员查询参数 (EmployeeQueryParams)
- `empCodeOrEmpName`: 工号或姓名
- `orgId`: 科室ID
- `orgIds`: 科室ID数组
- `restrictedEmpType`: 限制员工类型
- `restrictedEmps`: 限制员工代码
- `empCodeList`: 员工代码列表

#### 科室查询参数 (OrganizationQueryParams)
- `activeFlag`: 激活标志 (默认: "1")
- `orgTypes`: 科室类型数组
- `keyword`: 搜索关键词
- `parentOrgId`: 父科室ID

## 📝 注意事项

1. **数据一致性**：Store中的数据与后端保持一致，缓存时间为5分钟
2. **错误处理**：所有异步方法都可能抛出错误，需要适当的错误处理
3. **内存使用**：一次性加载全部数据会占用一定内存，但提高了查询性能
4. **网络请求**：初始加载时会发起网络请求，后续操作都在本地进行

## 🎉 总结

HRM Store 提供了完整的人员和科室数据管理解决方案，通过一次性加载和前端操作的策略，大大提高了应用的性能和用户体验。支持多种查询方式和组合操作，满足各种业务需求。
