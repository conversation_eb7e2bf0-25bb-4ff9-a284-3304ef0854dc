# 移动端预览组件适配指南 📱

## 🎯 概述

移动端预览组件是对原有桌面端预览组件的移动端优化版本，提供了专为移动设备设计的文件预览体验。组件支持PDF、图片、Excel等多种文件格式，并针对移动端交互进行了深度优化。

## ✨ 核心特性

### 1. **智能设备检测** 🔍
- 自动检测设备类型，移动端使用专用组件
- 桌面端保持原有预览体验
- 无缝切换，无需手动配置

### 2. **移动端优化设计** 📱
- **全屏预览**：充分利用移动端屏幕空间
- **触摸友好**：专为触摸操作优化的UI设计
- **手势支持**：双指缩放、单指拖拽等自然手势
- **安全区域适配**：支持刘海屏和底部安全区域

### 3. **丰富的文件格式支持** 📄
- **PDF文档**：支持在线PDF预览
- **图片文件**：JPG、PNG、GIF、WebP等格式
- **Excel表格**：XLS、XLSX格式支持
- **多文件轮播**：支持多个文件的轮播预览

### 4. **高级交互功能** 🎮
- **图片操作**：旋转、缩放、重置等操作
- **下载功能**：支持文件下载到本地
- **分享功能**：支持原生分享API
- **打印功能**：支持移动端打印

## 🚀 快速开始

### 基本使用

```vue
<template>
  <div>
    <!-- 触发预览的按钮 -->
    <n-button @click="showPreview = true">
      预览文件
    </n-button>
    
    <!-- 预览组件 -->
    <Preview
      v-model:show="showPreview"
      :url="fileUrl"
      :type="PreviewType.PDF"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { PreviewType } from '@jtypes'
import Preview from '@/components/common/preview/index.vue'

const showPreview = ref(false)
const fileUrl = ref('/path/to/your/file.pdf')
</script>
```

### 不同文件类型示例

```vue
<script setup>
// PDF预览
const showPdfPreview = () => {
  previewType.value = PreviewType.PDF
  previewUrl.value = '/documents/sample.pdf'
  showPreview.value = true
}

// 图片预览
const showImagePreview = () => {
  previewType.value = PreviewType.IMAGE
  previewUrl.value = '/images/sample.jpg'
  showPreview.value = true
}

// Excel预览
const showExcelPreview = () => {
  previewType.value = PreviewType.XLSX
  previewUrl.value = '/spreadsheets/sample.xlsx'
  showPreview.value = true
}

// 多图片预览
const showMultiImagePreview = () => {
  previewType.value = PreviewType.IMAGES
  previewUrls.value = [
    '/images/image1.jpg',
    '/images/image2.jpg',
    '/images/image3.jpg'
  ]
  showPreview.value = true
}
</script>
```

## 📋 API 参考

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `show` | `boolean` | `false` | 控制预览组件的显示/隐藏 |
| `url` | `string` | - | 文件URL地址 |
| `ossPath` | `string` | - | OSS文件路径 |
| `ossPathName` | `string` | - | OSS文件名称 |
| `urls` | `string[]` | `[]` | 多文件URL数组 |
| `file` | `File` | - | 本地文件对象 |
| `type` | `PreviewType` | `PDF` | 预览类型 |
| `bucket` | `string` | - | OSS存储桶名称 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:show` | `boolean` | 预览状态变化时触发 |

### PreviewType 枚举

```typescript
enum PreviewType {
  PDF = 'pdf',
  IMAGE = 'image',
  IMAGES = 'images',
  XLSX = 'xlsx',
  MULTI = 'multi'
}
```

## 🎨 移动端特性

### 1. 手势操作

```typescript
// 图片手势支持
- 双指缩放：0.3x - 3x 缩放范围
- 单指拖拽：放大状态下支持拖拽
- 触摸反馈：自然的触摸响应
```

### 2. 工具栏功能

```vue
<!-- 顶部工具栏 -->
<div class="mobile-toolbar">
  <!-- 返回按钮 -->
  <n-button @click="closePreview">
    <ArrowBackOutline />
  </n-button>
  
  <!-- 文件信息 -->
  <div class="file-info">
    <span>{{ fileName }}</span>
    <span>({{ currentIndex }}/{{ totalCount }})</span>
  </div>
  
  <!-- 操作按钮 -->
  <n-button @click="downloadFile">
    <DownloadOutline />
  </n-button>
</div>
```

### 3. 图片操作工具栏

```vue
<!-- 图片操作工具栏 -->
<div class="image-toolbar">
  <n-button-group>
    <n-button @click="rotateImage(-90)">逆时针旋转</n-button>
    <n-button @click="rotateImage(90)">顺时针旋转</n-button>
    <n-button @click="zoomImage(0.8)">缩小</n-button>
    <n-button @click="zoomImage(1.2)">放大</n-button>
    <n-button @click="resetImage">重置</n-button>
  </n-button-group>
</div>
```

## 🔧 高级配置

### OSS文件预览

```vue
<Preview
  v-model:show="showPreview"
  :ossPath="ossFilePath"
  :ossPathName="ossFileName"
  :bucket="ossBucket"
  :type="PreviewType.PDF"
/>
```

### 本地文件预览

```vue
<script setup>
const handleFileUpload = (file: File) => {
  previewFile.value = file
  
  // 根据文件类型设置预览类型
  const fileName = file.name.toLowerCase()
  if (fileName.endsWith('.pdf')) {
    previewType.value = PreviewType.PDF
  } else if (fileName.match(/\.(jpg|jpeg|png|gif)$/)) {
    previewType.value = PreviewType.IMAGE
  }
  
  showPreview.value = true
}
</script>
```

## 📱 移动端样式定制

### 主题变量

```css
:root {
  --mobile-toolbar-height: 60px;
  --mobile-safe-area-top: env(safe-area-inset-top);
  --mobile-safe-area-bottom: env(safe-area-inset-bottom);
}
```

### 自定义样式

```vue
<style scoped>
/* 自定义工具栏样式 */
.mobile-toolbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  backdrop-filter: blur(10px);
}

/* 自定义图片容器 */
.image-container {
  background: radial-gradient(circle, #1a1a1a 0%, #000000 100%);
}
</style>
```

## 🐛 常见问题

### Q: 为什么图片无法缩放？
A: 确保图片已完全加载，手势事件会在图片加载完成后自动绑定。

### Q: PDF预览空白怎么办？
A: 检查PDF文件URL是否可访问，确保服务器支持跨域访问。

### Q: 如何禁用某些功能？
A: 可以通过修改组件props或使用CSS隐藏不需要的功能按钮。

## 🔄 版本更新

### v1.0.1
- 🐛 修复TailwindCSS类名错误：`bg-opacity-80` → `bg-black/80`
- 🐛 修复移动端布局边距重合问题
- ✅ 优化安全区域适配

### v1.0.0
- ✅ 基础移动端预览功能
- ✅ 手势操作支持
- ✅ 多文件格式支持
- ✅ 安全区域适配

### 计划中的功能
- 🔄 视频文件预览支持
- 🔄 音频文件预览支持
- 🔄 更多手势操作
- 🔄 离线缓存功能

## 🚨 常见问题修复

### TailwindCSS类名问题
```css
/* ❌ 错误写法 */
.toolbar {
  @apply bg-black bg-opacity-80;
}

/* ✅ 正确写法 */
.toolbar {
  @apply bg-black/80;
}
```

### 移动端布局边距
参考 `src/views/layout/mobile.vue` 的布局设置：
- 顶部工具栏：`padding-top: calc(12px + env(safe-area-inset-top))`
- 内容区域：`margin-top: calc(60px + env(safe-area-inset-top))`
- 底部元素：`bottom: calc(16px + env(safe-area-inset-bottom))`
