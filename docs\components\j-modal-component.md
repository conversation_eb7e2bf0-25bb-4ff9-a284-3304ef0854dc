---
description: 
globs: 
alwaysApply: false
---
# j-modal 组件

## 📝 组件概述
`j-modal` 组件是一个封装了 naive-ui 的弹窗组件，提供了更便捷的使用方式和统一的样式，支持常见的对话框功能，如确认、取消、自定义内容等。

## 🔧 Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| show | Boolean | false | 控制弹窗显示/隐藏 |
| title | String | '提示' | 弹窗标题 |
| width | [String, Number] | '50%' | 弹窗宽度 |
| height | [String, Number] | 'auto' | 弹窗高度 |
| showBtn | Boolean | true | 是否显示底部按钮 |
| confirmText | String | '确定' | 确认按钮文字 |
| cancelText | String | '取消' | 取消按钮文字 |
| loading | Boolean | false | 确认按钮是否显示加载状态 |
| showClose | Boolean | true | 是否显示关闭按钮 |
| preset | String | 'dialog' | 预设样式，可选值：'dialog', 'card' |
| closable | Boolean | true | 是否可以通过点击遮罩层关闭 |
| destroyOnClose | Boolean | false | 关闭时是否销毁内容 |

## 🎯 事件

| 事件名 | 参数 | 说明 |
| --- | --- | --- |
| update:show | (value: Boolean) | 弹窗显示/隐藏状态变更事件 |
| confirm | - | 点击确认按钮事件 |
| cancel | - | 点击取消按钮事件 |
| close | - | 弹窗关闭事件 |

## 📋 插槽

| 插槽名 | 说明 |
| --- | --- |
| default | 默认插槽，弹窗主体内容 |
| header | 自定义弹窗头部 |
| content | 自定义弹窗内容区域 |
| footer | 自定义弹窗底部 |
| action | 自定义底部按钮区域 |

## 🌟 使用示例

### 基础使用
```vue
<template>
  <n-button @click="showModal = true">打开弹窗</n-button>
  
  <j-modal v-model:show="showModal" title="用户信息">
    <p>这是弹窗内容</p>
  </j-modal>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  
  const showModal = ref(false)
</script>
```

### 自定义内容和事件处理
```vue
<template>
  <j-modal 
    v-model:show="showModal" 
    title="编辑用户" 
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :loading="loading"
  >
    <template #content>
      <n-form :model="formValue" label-placement="left">
        <n-form-item label="用户名" path="username">
          <n-input v-model:value="formValue.username" />
        </n-form-item>
        <n-form-item label="邮箱" path="email">
          <n-input v-model:value="formValue.email" />
        </n-form-item>
      </n-form>
    </template>
  </j-modal>
</template>

<script lang="tsx" setup>
  import { ref, reactive } from 'vue'
  
  const showModal = ref(false)
  const loading = ref(false)
  
  const formValue = reactive({
    username: '',
    email: ''
  })
  
  const handleConfirm = async () => {
    loading.value = true
    try {
      // 处理表单提交
      await saveUser(formValue)
      showModal.value = false
    } finally {
      loading.value = false
    }
  }
  
  const handleCancel = () => {
    // 可以在这里进行一些清理工作
    showModal.value = false
  }
</script>
```

### 无按钮的弹窗
```vue
<template>
  <j-modal v-model:show="showSignModal" title="签字确认" width="20%" :show-btn="false">
    <template #content>
      <j-sign @done="signDone" placeholder="请输入个人签章密码" />
    </template>
  </j-modal>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  
  const showSignModal = ref(false)
  
  const signDone = (result) => {
    // 处理签名完成事件
    showSignModal.value = false
  }
</script>
```

## 🔍 组件实现细节

`j-modal` 组件基于 naive-ui 的 `n-modal` 组件封装，增加了更多便捷的属性和事件处理。组件支持通过 `v-model:show` 控制显示和隐藏，内部集成了确认和取消按钮的处理逻辑。

组件默认使用 dialog 预设样式，可以通过 preset 属性切换为 card 样式。对于需要自定义的场景，提供了多个插槽以满足不同的布局需求。

## ⚠️ 注意事项

1. 使用 `v-model:show` 绑定弹窗的显示状态
2. 如果需要在确认按钮上显示加载状态，可以设置 `loading` 属性
3. 对于自定义底部按钮，可以使用 `:show-btn="false"` 隐藏默认按钮，然后使用 footer 或 action 插槽
