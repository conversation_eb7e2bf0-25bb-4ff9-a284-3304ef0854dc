# 资产价值分析可视化功能说明 📊

## 功能概述

为资产价值分析页面添加了强大的数据可视化功能，支持多种图表类型和数据维度的展示，让数据分析更加直观和高效。

## 主要特性 ✨

### 1. 多种图表类型
- **柱状图** 📊：适合对比不同资产类型的数值大小
- **饼图** 🥧：展示各资产类型的占比分布
- **折线图** 📈：显示数据趋势变化

### 2. 多维度数据展示
- **数量维度**：显示各资产类型的数量统计
- **原值维度**：展示资产的原始价值分布
- **净值维度**：显示资产的净值情况
- **本期折旧维度**：展示本期折旧/摊销数据

### 3. 智能数据处理
- **自动排序**：数据按值大小降序排列，突出重点
- **智能格式化**：
  - 数量显示：使用千分位分隔符，单位为"件"
  - 金额显示：自动转换为万元、亿元单位
- **颜色主题**：使用专业的配色方案，提升视觉效果

### 4. 交互式体验
- **实时切换**：图表类型和数据维度可实时切换
- **悬浮提示**：鼠标悬浮显示详细数据信息
- **响应式设计**：图表自适应容器大小

## 使用方法 🚀

### 1. 打开可视化界面
1. 在资产价值分析页面查询数据
2. 点击页面上方的 **"数据可视化"** 按钮
3. 弹窗将显示当前查询结果的可视化图表

### 2. 切换图表类型
在弹窗顶部选择不同的图表类型：
- 选择 **"柱状图"** 进行数值对比
- 选择 **"饼图"** 查看占比分布  
- 选择 **"折线图"** 观察数据趋势

### 3. 切换数据维度
根据分析需要选择不同的数据维度：
- **数量**：分析资产数量分布
- **原值**：分析资产价值构成
- **净值**：分析资产净值情况
- **本期折旧**：分析折旧摊销情况

### 4. 查看统计信息
弹窗底部显示数据汇总统计：
- 总记录数
- 总数量
- 总原值
- 总净值

## 技术实现 🔧

### 核心技术栈
- **ECharts 5.x**：专业的数据可视化图表库
- **Vue 3 Composition API**：现代化的响应式框架
- **NaiveUI**：优雅的UI组件库
- **TypeScript**：类型安全的开发体验

### 数据来源
- 数据来源：`crudRef.value.originData`
- 实时同步：与表格数据保持同步
- 自动更新：查询条件变化时自动更新图表

### 性能优化
- **按需渲染**：只有在打开弹窗时才初始化图表
- **内存管理**：弹窗关闭时自动销毁图表实例
- **响应式更新**：图表配置变化时智能更新

## 使用场景 💡

### 1. 资产结构分析
使用饼图查看不同资产类型的占比分布，快速了解资产结构。

### 2. 价值对比分析
使用柱状图对比不同资产类型的原值、净值，识别高价值资产。

### 3. 折旧趋势分析
使用折线图观察各资产类型的折旧情况，制定折旧策略。

### 4. 数量统计分析
使用数量维度分析各类资产的数量分布，优化资产配置。

## 注意事项 ⚠️

1. **数据要求**：需要先查询数据才能使用可视化功能
2. **浏览器兼容**：建议使用现代浏览器以获得最佳体验
3. **性能考虑**：大数据量时建议使用分页或筛选功能

## 未来扩展 🔮

- 支持更多图表类型（散点图、雷达图等）
- 添加图表导出功能
- 支持自定义颜色主题
- 增加数据钻取功能
- 支持多维度组合分析

---

*该功能为资产管理系统的重要组成部分，提升了数据分析的效率和用户体验。* 🎯
