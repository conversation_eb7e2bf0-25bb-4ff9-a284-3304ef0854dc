# 移动端响应式主题配置 📱

## 设计原则

### 响应式适配 📏
- **移动端优化**: 提供40px-52px触摸目标尺寸，16px字体避免iOS缩放
- **平板适配**: 提供32px-44px组件高度，13px-16px字体
- **桌面保持**: 提供24px-36px组件高度，12px-15px字体，保持原有体验

### 触摸友好 👆
- **分层尺寸系统**: tiny/small/medium/large四个尺寸等级
- **最小触摸目标**: 移动端最小40px，标准44px（符合 Apple 和 Material Design 规范）
- **合适间距**: 避免误触，提升操作精度
- **现代圆角**: 移动端8px，平板6px，桌面4px

### 保持一致性 🎨
- **颜色系统**: 完全保持PC端默认NaiveUI颜色
- **主题变量**: 继续使用 `var(--j-frame-color)` 系统
- **响应式布局**: 只调整尺寸，不改变视觉风格

## 核心配置

### 设备断点
```typescript
export const BREAKPOINTS = {
  mobile: 768,    // 小于768px为移动端
  tablet: 1024,   // 768px-1024px为平板
  desktop: 1024   // 大于1024px为桌面端
}
```

## 分层尺寸系统

### 移动端配置 (< 768px)
```typescript
const mobileProps = {
  button: {
    heightTiny: '40px',    // 最小触摸目标
    heightSmall: '44px',   // 标准触摸目标  
    heightMedium: '48px',  // 大按钮
    heightLarge: '52px',   // 特大按钮
    fontSizeTiny: '14px',
    fontSizeSmall: '15px',
    fontSizeMedium: '16px',
    fontSizeLarge: '17px',
    paddingTiny: '0 8px',
    paddingSmall: '0 10px',
    paddingMedium: '0 12px',
    paddingLarge: '0 16px',
    borderRadius: '8px',
  },
  input: {
    heightTiny: '40px',
    heightSmall: '44px',
    heightMedium: '48px',
    heightLarge: '52px',
    fontSizeTiny: '14px',
    fontSizeSmall: '15px',
    fontSizeMedium: '16px',
    fontSizeLarge: '17px',
    paddingTiny: '8px',
    paddingSmall: '10px',
    paddingMedium: '12px',
    paddingLarge: '14px',
    borderRadius: '8px',
  }
}
```

### 平板配置 (768px - 1024px)
```typescript
const tabletProps = {
  button: {
    heightTiny: '32px',
    heightSmall: '36px',
    heightMedium: '40px',
    heightLarge: '44px',
    fontSizeTiny: '13px',
    fontSizeSmall: '14px',
    fontSizeMedium: '15px',
    fontSizeLarge: '16px',
    paddingTiny: '0 6px',
    paddingSmall: '0 8px',
    paddingMedium: '0 12px',
    paddingLarge: '0 16px',
    borderRadius: '6px',
  },
  input: {
    heightTiny: '32px',
    heightSmall: '36px',
    heightMedium: '40px',
    heightLarge: '44px',
    fontSizeTiny: '13px',
    fontSizeSmall: '14px',
    fontSizeMedium: '15px',
    fontSizeLarge: '16px',
    paddingTiny: '6px',
    paddingSmall: '8px',
    paddingMedium: '12px',
    paddingLarge: '14px',
    borderRadius: '6px',
  }
}
```

### 桌面配置 (> 1024px)
```typescript
const desktopProps = {
  button: {
    heightTiny: '24px',     // 保持原有尺寸
    heightSmall: '28px',
    heightMedium: '32px',
    heightLarge: '36px',
    fontSizeTiny: '12px',
    fontSizeSmall: '13px',
    fontSizeMedium: '14px',
    fontSizeLarge: '15px',
    paddingTiny: '0 6px',
    paddingSmall: '0 8px',
    paddingMedium: '0 10px',
    paddingLarge: '0 14px',
    borderRadius: '4px',
  },
  input: {
    heightTiny: '24px',
    heightSmall: '28px',
    heightMedium: '32px',
    heightLarge: '36px',
    fontSizeTiny: '12px',
    fontSizeSmall: '13px',
    fontSizeMedium: '14px',
    fontSizeLarge: '15px',
    paddingTiny: '6px',
    paddingSmall: '8px',
    paddingMedium: '10px',
    paddingLarge: '12px',
    borderRadius: '4px',
  }
}
```

## 组件尺寸对比表

| 组件尺寸 | 移动端 | 平板端 | 桌面端 | 用途说明 |
|---------|--------|--------|--------|----------|
| **按钮高度** |  |  |  |  |
| Tiny | 40px | 32px | 24px | 紧凑布局 |
| Small | 44px | 36px | 28px | 常用尺寸 |
| Medium | 48px | 40px | 32px | 默认尺寸 |
| Large | 52px | 44px | 36px | 强调按钮 |
| **按钮字体** |  |  |  |  |
| Tiny | 14px | 13px | 12px | 避免iOS缩放 |
| Small | 15px | 14px | 13px | 清晰可读 |
| Medium | 16px | 15px | 14px | 标准大小 |
| Large | 17px | 16px | 15px | 突出显示 |
| **输入框高度** |  |  |  |  |
| Tiny | 40px | 32px | 24px | 紧凑表单 |
| Small | 44px | 36px | 28px | 常用表单 |
| Medium | 48px | 40px | 32px | 标准表单 |
| Large | 52px | 44px | 36px | 重要输入 |
| **按钮组高度** |  |  |  |  |
| Tiny | 40px | 32px | 24px | 紧凑工具栏 |
| Small | 44px | 36px | 28px | 常用工具栏 |
| Medium | 48px | 40px | 32px | 标准工具栏 |
| Large | 52px | 44px | 36px | 重要操作组 |

## 完整主题配置

```json
{
  "responsiveTheme": {
    "Form": {
      "lineHeight": "动态适配(48px/40px/32px)",
      "blankHeightSmall": "动态适配(44px/36px/28px)",
      "blankHeightMedium": "动态适配(48px/40px/32px)",
      "blankHeightLarge": "动态适配(52px/44px/36px)",
      "labelFontSizeLeftSmall": "动态适配(14px/14px/13px)",
      "labelFontSizeTopSmall": "动态适配(14px/14px/13px)"
    },
    "Input": {
      "heightTiny": "动态适配(40px/32px/24px)",
      "heightSmall": "动态适配(44px/36px/28px)",
      "heightMedium": "动态适配(48px/40px/32px)",
      "heightLarge": "动态适配(52px/44px/36px)",
      "paddingTiny": "动态适配(8px/6px/6px)",
      "paddingSmall": "动态适配(10px/8px/8px)",
      "paddingMedium": "动态适配(12px/12px/10px)",
      "paddingLarge": "动态适配(14px/14px/12px)",
      "fontSizeTiny": "动态适配(14px/13px/12px)",
      "fontSizeSmall": "动态适配(15px/14px/13px)",
      "fontSizeMedium": "动态适配(16px/15px/14px)",
      "fontSizeLarge": "动态适配(17px/16px/15px)",
      "borderRadius": "动态适配(8px/6px/4px)"
    },
    "Button": {
      "heightTiny": "动态适配(40px/32px/24px)",
      "heightSmall": "动态适配(44px/36px/28px)",
      "heightMedium": "动态适配(48px/40px/32px)",
      "heightLarge": "动态适配(52px/44px/36px)",
      "paddingTiny": "动态适配(0 8px/0 6px/0 6px)",
      "paddingSmall": "动态适配(0 10px/0 8px/0 8px)",
      "paddingMedium": "动态适配(0 12px/0 12px/0 10px)",
      "paddingLarge": "动态适配(0 16px/0 16px/0 14px)",
      "fontSizeTiny": "动态适配(14px/13px/12px)",
      "fontSizeSmall": "动态适配(15px/14px/13px)",
      "fontSizeMedium": "动态适配(16px/15px/14px)",
      "fontSizeLarge": "动态适配(17px/16px/15px)",
      "borderRadiusTiny": "动态适配(8px/6px/4px)",
      "borderRadiusSmall": "动态适配(8px/6px/4px)",
      "borderRadiusMedium": "动态适配(8px/6px/4px)",
      "borderRadiusLarge": "动态适配(8px/6px/4px)"
    },
    "ButtonGroup": {
      "heightTiny": "动态适配(40px/32px/24px)",
      "heightSmall": "动态适配(44px/36px/28px)", 
      "heightMedium": "动态适配(48px/40px/32px)",
      "heightLarge": "动态适配(52px/44px/36px)",
      "fontSizeTiny": "动态适配(14px/13px/12px)",
      "fontSizeSmall": "动态适配(15px/14px/13px)",
      "fontSizeMedium": "动态适配(16px/15px/14px)",
      "fontSizeLarge": "动态适配(17px/16px/15px)",
      "borderRadius": "动态适配(8px/6px/4px)"
    },
    "DataTable": {
      "fontSizeSmall": "14px",
      "fontSizeMedium": "14px",
      "fontSizeLarge": "14px",
      "thPaddingSmall": "动态适配(12px 8px/10px/8px)",
      "thPaddingMedium": "动态适配(12px 8px/10px/8px)",
      "thPaddingLarge": "动态适配(12px 8px/10px/8px)",
      "tdPaddingSmall": "动态适配(12px 8px/10px/8px)",
      "tdPaddingMedium": "动态适配(12px 8px/10px/8px)",
      "tdPaddingLarge": "动态适配(12px 8px/10px/8px)"
    },
    "InternalSelection": {
      "heightTiny": "动态适配(40px/32px/24px)",
      "heightSmall": "动态适配(44px/36px/28px)",
      "heightMedium": "动态适配(48px/40px/32px)",
      "heightLarge": "动态适配(52px/44px/36px)",
      "fontSizeTiny": "动态适配(14px/13px/12px)",
      "fontSizeSmall": "动态适配(15px/14px/13px)",
      "fontSizeMedium": "动态适配(16px/15px/14px)",
      "fontSizeLarge": "动态适配(17px/16px/15px)",
      "borderRadius": "动态适配(8px/6px/4px)"
    },
    "Menu": {
      "保持原有颜色配置": "var(--j-frame-color) 系列变量"
    },
    "Dropdown": {
      "保持原有颜色配置": "var(--j-frame-color) 系列变量"
    },
    "common": {
      "fontSize": "var(--j-font-size-normal)"
    }
  }
}
```

## 实现方式

### 自动设备检测
```typescript
import { getDeviceType, DeviceType } from '@/utils/device'

function getDeviceProps() {
  const deviceType = getDeviceType()
  
  switch (deviceType) {
    case DeviceType.MOBILE:
      return mobileProps    // 40px-52px 触摸优化
    case DeviceType.TABLET:
      return tabletProps    // 32px-44px 平衡体验
    case DeviceType.DESKTOP:
    default:
      return desktopProps   // 24px-36px 保持原有
  }
}
```

### 动态主题更新
```typescript
import { updateThemeOverrides } from '@/types/comps/themeOverrides'

// 窗口大小变化时自动更新主题
const responsiveListener = new ResponsiveListener()
responsiveListener.addListener((deviceType) => {
  const newThemeOverrides = updateThemeOverrides()
  // 应用新的主题配置
})
```

### 使用示例
```vue
<template>
  <!-- 不同尺寸的按钮 -->
  <n-button size="tiny">紧凑按钮</n-button>      <!-- 移动端40px -->
  <n-button size="small">小按钮</n-button>       <!-- 移动端44px -->
  <n-button size="medium">标准按钮</n-button>     <!-- 移动端48px -->
  <n-button size="large">大按钮</n-button>       <!-- 移动端52px -->

  <!-- 不同尺寸的按钮组 -->
  <n-button-group size="tiny">                   <!-- 移动端40px -->
    <n-button>左</n-button>
    <n-button>中</n-button>
    <n-button>右</n-button>
  </n-button-group>
  
  <n-button-group size="small">                  <!-- 移动端44px -->
    <n-button>选项1</n-button>
    <n-button>选项2</n-button>
  </n-button-group>
  
  <n-button-group size="medium">                 <!-- 移动端48px -->
    <n-button>编辑</n-button>
    <n-button>删除</n-button>
  </n-button-group>
  
  <n-button-group size="large">                  <!-- 移动端52px -->
    <n-button>保存</n-button>
    <n-button>取消</n-button>
  </n-button-group>

  <!-- 不同尺寸的输入框 -->
  <n-input size="tiny" placeholder="紧凑输入框" />    <!-- 移动端40px -->
  <n-input size="small" placeholder="小输入框" />     <!-- 移动端44px -->
  <n-input size="medium" placeholder="标准输入框" />   <!-- 移动端48px -->
  <n-input size="large" placeholder="大输入框" />     <!-- 移动端52px -->

  <!-- 不同尺寸的选择器 -->
  <n-select size="tiny" :options="options" />      <!-- 移动端40px -->
  <n-select size="small" :options="options" />     <!-- 移动端44px -->
  <n-select size="medium" :options="options" />    <!-- 移动端48px -->
  <n-select size="large" :options="options" />     <!-- 移动端52px -->
</template>
```

## 设计思路

### 触摸目标优化
- **移动端**: 40px-52px 确保舒适的触摸操作
- **平板端**: 32px-44px 平衡触摸和界面密度
- **桌面端**: 24px-36px 保持原有精细操作体验

### 字体大小策略
- **移动端**: 16px字体避免iOS自动缩放，最小14px保证可读性
- **平板端**: 13px-16px提供清晰阅读体验
- **桌面端**: 12px-15px保持原有界面密度

### 间距设计
- **移动端**: 更大的padding确保触摸舒适度
- **平板端**: 适中的间距平衡美观和实用
- **桌面端**: 紧凑的间距保持信息密度

## 兼容性说明

1. **完全向后兼容**: 原有代码无需修改
2. **自动适配**: 根据屏幕尺寸自动应用相应配置
3. **颜色保持**: 完全保留PC端NaiveUI默认颜色方案
4. **响应式**: 窗口大小变化时自动更新主题配置

通过这套分层尺寸系统，确保组件在不同设备上都有最佳的交互体验，同时保持设计的一致性。 