# 科室月度人员上报系统重构 📊

## 🎯 重构目标

将原有硬编码的科室月度人员上报系统重构为**动态可配置化**的系统，修正算法问题，提升系统的灵活性和可维护性。

## ✨ 核心特性

### 🔧 动态配置化
- **人员类型配置表**：支持动态配置不同类型的人员计算规则
- **灵活的计算方法**：支持固定值、按百分比、按天数比例等多种计算方式
- **可扩展性**：可通过配置表轻松添加新的人员类型

### 📈 正确的计算逻辑
- **正贡献计算**：本月领取100%人员、正常在岗、离任主任(30%)
- **负贡献计算**：各种请假按实际天数比例扣除
- **精确算法**：请假20天(总30天) = 添加(30-20)/30个计算人数

### 🎨 现代化前端
- **Setup语法糖**：使用Vue3 Composition API
- **TSX支持**：组件使用TSX render函数方式
- **响应式设计**：实时计算和数据响应
- **用户友好**：清晰的UI界面和交互

## 🏗️ 系统架构

### 数据库设计

#### 1. 人员类型配置表 (`pms_staff_type_config`)
```sql
-- 存储各种人员类型的配置信息
- type_code: 类型代码 (FULL_TIME, TRAINING, SICK_LEAVE等)
- type_name: 类型名称
- category: 分类 (positive正贡献, negative负贡献)
- calculation_method: 计算方法 (fixed, percentage, ratio_days)
- need_days_input: 是否需要输入天数
- need_percentage_input: 是否需要输入百分比
```

#### 2. 科室月度人员上报表 (`pms_monthly_dept_staff_report`)
```sql
-- 存储实际上报的数据
- department_code: 科室代码
- report_month: 上报月份
- report_type: 上报类型 (0护, 1医, 2技)
- type_code: 人员类型代码
- staff_count: 人员数量
- work_days: 工作天数
- percentage: 百分比
- calculated_count: 计算后人数
```

### 后端架构

```
src/main/java/com/jp/med/pms/modules/pmsCalc/
├── dto/                    # 数据传输对象
│   ├── PmsStaffTypeConfigDto.java
│   └── PmsMonthlyDeptStaffReportDto.java
├── vo/                     # 视图对象
│   └── PmsMonthlyStaffReportVO.java
├── requestBody/            # 请求体
│   └── PmsMonthlyStaffReportRequestBody.java
├── service/                # 业务逻辑层
│   └── PmsMonthlyStaffReportService.java
├── controller/             # 控制器层
│   └── PmsMonthlyStaffReportController.java
└── mapper/                 # 数据访问层
    ├── PmsStaffTypeConfigMapper.java
    └── PmsMonthlyStaffReportMapper.java
```

### 前端架构

```
src/views/modules/pms/PmsMonthlyDeptStaffNumberReport/
├── index-new.vue                    # 主页面组件
├── StaffReportTable.vue            # 表格组件 (TSX)
├── useMonthlyStaffReport.ts         # 业务逻辑Hook
├── index.vue                        # 原有组件(保留)
└── index.tsx                        # 原有配置(保留)
```

## 🚀 技术实现

### 前端技术栈
- **Vue 3** + **Setup语法糖**
- **TypeScript** + **TSX**
- **Naive UI** 组件库
- **Composition API** + **Custom Hooks**

### 后端技术栈
- **Spring Boot** + **MyBatis Plus**
- **PostgreSQL** 数据库
- **RESTful API** 设计

## 💼 业务逻辑

### 计算规则

#### 正贡献人员 ✅
1. **本月领取100%人员**：固定按人数计算
2. **正常在岗**：固定按人数计算  
3. **离任主任**：按30%比例计算

#### 负贡献人员 ➖
1. **请假类型**：按天数比例扣除
   - 公式：`人数 × (实际工作天数 / 总天数)`
   - 示例：1人请假20天，总30天 → 计算人数 = 1 × (30-20)/30 = 0.3333

2. **临时聘用人员**：按百分比和天数双重计算
   - 公式：`人数 × (百分比/100) × (工作天数/总天数)`

### API接口

#### 1. 获取人员类型配置
```http
GET /pms/monthlyStaffReport/getStaffTypeConfigs
```

#### 2. 查询科室上报数据
```http
GET /pms/monthlyStaffReport/queryDeptReport?departmentCode=xxx&reportMonth=2024-11-01&reportType=1
```

#### 3. 提交科室上报数据
```http
POST /pms/monthlyStaffReport/submitReport
```

## 🎯 核心优势

### 1. **可配置化** 🔧
- 不再硬编码人员类型
- 支持动态添加新的计算规则
- 业务人员可通过配置调整

### 2. **算法正确** ✅
- 修正了原有的计算错误
- 支持多种计算方法
- 精确的数值计算

### 3. **用户体验** 🎨
- 直观的正负贡献分类
- 实时计算反馈
- 清晰的操作界面

### 4. **可维护性** 🛠️
- 模块化设计
- 代码复用性高
- 易于扩展和修改

## 📋 使用指南

### 1. 数据库初始化
```sql
-- 执行建表语句
\i pms_monthly_staff_report_schema.sql
```

### 2. 启动后端服务
```bash
# 确保配置正确的数据库连接
# 启动Spring Boot应用
```

### 3. 访问前端页面
```
http://localhost:3000/pms/monthlyStaffReport
```

### 4. 配置人员类型
- 通过数据库配置表添加新的人员类型
- 设置相应的计算方法和参数

## 🔄 迁移指南

### 从老系统迁移
1. **保留原有代码**：`index.vue` 继续可用
2. **逐步切换**：可以并行运行新旧系统
3. **数据迁移**：编写脚本将原有数据转换为新格式

### 配置迁移
```sql
-- 将原有硬编码类型转换为配置数据
INSERT INTO pms_staff_type_config (...) VALUES (...);
```

## 🎉 总结

这次重构实现了：
- ✅ **完全可配置化**的人员类型管理
- ✅ **正确的计算算法**实现
- ✅ **现代化的前端技术栈**
- ✅ **清晰的系统架构**设计
- ✅ **良好的可扩展性**和维护性

通过这次重构，科室月度人员上报系统从硬编码转变为灵活可配置的现代化系统，大大提升了业务适应性和系统可维护性！ 🚀 