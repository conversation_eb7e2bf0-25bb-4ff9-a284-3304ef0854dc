# ECS系统工资凭证模块Drools重构PRD文档

## 1. 项目概述 📋

### 1.1 项目背景
中江县人民医院智慧财务系统(ECS)中的工资凭证模块负责处理医院工资相关的财务凭证生成和审批流程。当前系统存在业务规则硬编码、规则维护困难、扩展性差等问题，需要引入Drools规则引擎进行重构优化。

### 1.2 项目目标
- 🎯 **业务规则外化**：将工资凭证生成的业务规则从代码中抽离，使用Drools规则引擎管理
- 🔧 **提升可维护性**：业务人员可通过配置界面维护规则，无需修改代码
- 📈 **增强扩展性**：支持灵活的规则组合和动态规则调整
- ⚡ **优化性能**：通过规则引擎优化决策执行效率
- 🛡️ **保证准确性**：确保工资凭证生成的准确性和一致性

### 1.3 项目范围
- 工资凭证生成规则引擎化
- 工资类型判断规则配置
- 科目映射规则管理
- 辅助项配置规则
- 凭证生成条件规则
- 规则配置管理界面

## 2. 现状分析 🔍

### 2.1 当前系统架构
```mermaid
graph TD
    A[工资任务] --> B[工资凭证处理]
    B --> C{工资类型判断}
    C -->|salaryType=1| D[工资计提]
    C -->|salaryType=2| E[工资发放+三险两金]
    C -->|salaryType=3| F[企业四险两金]
    C -->|salaryType=4| G[工会经费]
    D --> H[直接生成凭证]
    E --> I[报销审批流程]
    F --> H
    G --> H
    H --> J[ERP凭证生成]
    I --> K[BPM审批]
    K --> J
```

### 2.2 核心数据模型

#### 2.2.1 核心数据模型关系图

```mermaid
erDiagram
    ECS_REIM_SALARY_TASK {
        INTEGER id PK "主键ID"
        INTEGER salary_id "工资任务ID"
        STRING ff_mth "工资发放月份"
        INTEGER num "工资条数"
        DECIMAL should_pay "应发合计汇总"
        DECIMAL reduce_pay "扣款合计汇总"
        DECIMAL real_pay "实发合计汇总"
        STRING salary_type "工资类型(1-4)"
        STRING reim_flag "是否报销标识"
        INTEGER reim_id "报销ID"
        STRING crter "制表人"
        DATETIME crte_time "制表时间"
    }

    ECS_REIM_SALARY_TASK_DETAIL {
        INTEGER id PK "主键ID"
        INTEGER task_id FK "任务ID"
        STRING org_id "科室ID"
        STRING emp_type "人员类型"
        STRING reim_name "报销项目"
        STRING reim_type "报销类型"
        DECIMAL reim_amt "报销金额"
        STRING reim_desc "报销摘要"
        STRING emp_code "员工编号"
        INTEGER emp_count "人数"
    }

    SALARY_RULE_CONFIG {
        INTEGER id PK "主键ID"
        STRING rule_name "规则名称"
        STRING rule_type "规则类型"
        TEXT rule_content "规则内容"
        STRING version "版本号"
        STRING status "状态"
        INTEGER priority "优先级"
        DATETIME create_time "创建时间"
        STRING creator "创建人"
    }

    SALARY_TYPE_RULE {
        INTEGER id PK "主键ID"
        STRING salary_type "工资类型"
        STRING process_type "处理类型"
        STRING template_code "模板代码"
        TEXT description "描述"
        STRING active_flag "激活标识"
    }

    SALARY_SUBJECT_MAPPING {
        INTEGER id PK "主键ID"
        STRING salary_type "工资类型"
        STRING dept_type "科室类型"
        STRING emp_type "人员类型"
        STRING reim_type "报销类型"
        STRING debit_subject "借方科目"
        STRING credit_subject "贷方科目"
        STRING budget_subject "预算科目"
        TEXT auxiliary_config "辅助项配置"
        INTEGER priority "优先级"
    }

    SALARY_SPECIAL_EMPLOYEE {
        INTEGER id PK "主键ID"
        STRING emp_code "员工编号"
        STRING emp_name "员工姓名"
        STRING special_type "特殊类型"
        STRING subject_code "科目代码"
        STRING auxiliary_code "辅助项代码"
        TEXT description "描述"
    }

    ECS_REIM_SALARY_TASK ||--o{ ECS_REIM_SALARY_TASK_DETAIL : "一对多"
    SALARY_TYPE_RULE ||--o{ ECS_REIM_SALARY_TASK : "规则控制"
    SALARY_SUBJECT_MAPPING ||--o{ ECS_REIM_SALARY_TASK_DETAIL : "科目映射"
    SALARY_SPECIAL_EMPLOYEE ||--o{ ECS_REIM_SALARY_TASK_DETAIL : "特殊处理"
```

#### 2.2.2 工资任务表详细结构

> 📋 **表名**: `ecs_reim_salary_task`
> 🎯 **用途**: 存储工资任务的汇总信息，是工资凭证生成的主要数据源

```mermaid
graph TD
    A[🏥 工资任务表<br/>ecs_reim_salary_task] --> B[📊 基础信息]
    A --> C[💰 金额信息]
    A --> D[🔄 流程信息]
    A --> E[📝 审计信息]

    B --> B1[🔑 id<br/>主键ID<br/>Integer]
    B --> B2[🆔 salary_id<br/>工资任务ID<br/>Integer]
    B --> B3[📅 ff_mth<br/>工资发放月份<br/>String]
    B --> B4[📊 num<br/>工资条数<br/>Integer]

    C --> C1[💵 should_pay<br/>应发合计汇总<br/>BigDecimal]
    C --> C2[➖ reduce_pay<br/>扣款合计汇总<br/>BigDecimal]
    C --> C3[💸 real_pay<br/>实发合计汇总<br/>BigDecimal]

    D --> D1[🏷️ salary_type<br/>工资类型<br/>String]
    D --> D2[✅ reim_flag<br/>是否报销标识<br/>String]
    D --> D3[🔗 reim_id<br/>报销ID<br/>Integer]
    D --> D4[📋 type<br/>类型标识<br/>String]

    E --> E1[👤 crter<br/>制表人<br/>String]
    E --> E2[⏰ crte_time<br/>制表时间<br/>DateTime]
    E --> E3[📝 remark<br/>备注<br/>String]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

**📊 字段详细说明表**

| 字段名 | 类型 | 长度 | 必填 | 说明 | 示例值 |
|--------|------|------|------|------|--------|
| `id` | Integer | - | ✅ | 主键ID，自增 | 1001 |
| `salary_id` | Integer | - | ✅ | 工资任务ID，关联HRM系统 | 202401001 |
| `ff_mth` | String | 10 | ✅ | 工资发放月份，格式：YYYY-MM | "2024-01" |
| `num` | Integer | - | ✅ | 工资条数，统计人员数量 | 150 |
| `should_pay` | BigDecimal | 15,2 | ✅ | 应发合计汇总 | 1500000.00 |
| `reduce_pay` | BigDecimal | 15,2 | ✅ | 扣款合计汇总 | 300000.00 |
| `real_pay` | BigDecimal | 15,2 | ✅ | 实发合计汇总 | 1200000.00 |
| `salary_type` | String | 2 | ✅ | **工资类型**：1-工资计提，2-工资发放，3-企业四险两金，4-工会经费 | "1" |
| `reim_flag` | String | 1 | ✅ | 是否报销标识：0-未报销，1-已报销 | "0" |
| `reim_id` | Integer | - | ❌ | 报销ID，关联报销流程 | 3001 |
| `type` | String | 10 | ❌ | 类型标识 | "SALARY" |
| `crter` | String | 50 | ✅ | 制表人 | "张三" |
| `crte_time` | DateTime | - | ✅ | 制表时间 | "2024-01-15 10:30:00" |
| `remark` | String | 500 | ❌ | 备注信息 | "2024年1月工资计提" |

#### 2.2.3 工资任务明细表详细结构

> 📋 **表名**: `ecs_reim_salary_task_detail`
> 🎯 **用途**: 存储工资任务的明细信息，包含具体的工资项目和金额

```mermaid
graph TD
    A[📄 工资任务明细表<br/>ecs_reim_salary_task_detail] --> B[🔗 关联信息]
    A --> C[🏢 组织信息]
    A --> D[👥 员工信息]
    A --> E[💼 报销信息]

    B --> B1[🔑 id<br/>主键ID<br/>Integer]
    B --> B2[🔗 task_id<br/>任务ID<br/>Integer FK]

    C --> C1[🏢 org_id<br/>科室ID<br/>String]
    C --> C2[🏥 org_name<br/>科室名称<br/>String]
    C --> C3[📊 dept_type<br/>科室类型<br/>String]

    D --> D1[👤 emp_code<br/>员工编号<br/>String]
    D --> D2[📝 emp_name<br/>员工姓名<br/>String]
    D --> D3[🏷️ emp_type<br/>人员类型<br/>String]
    D --> D4[📊 emp_count<br/>人数<br/>Integer]

    E --> E1[📋 reim_name<br/>报销项目<br/>String]
    E --> E2[🏷️ reim_type<br/>报销类型<br/>String]
    E --> E3[💰 reim_amt<br/>报销金额<br/>BigDecimal]
    E --> E4[📝 reim_desc<br/>报销摘要<br/>String]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

**📊 字段详细说明表**

| 字段名 | 类型 | 长度 | 必填 | 说明 | 示例值 |
|--------|------|------|------|------|--------|
| `id` | Integer | - | ✅ | 主键ID，自增 | 10001 |
| `task_id` | Integer | - | ✅ | 任务ID，外键关联工资任务表 | 1001 |
| `org_id` | String | 20 | ✅ | 科室ID | "1001" |
| `org_name` | String | 100 | ❌ | 科室名称 | "内科" |
| `dept_type` | String | 10 | ✅ | **科室类型**：1-业务科室，2-管理科室 | "1" |
| `emp_type` | String | 20 | ✅ | **人员类型**：在编、招聘、临聘、借调、返聘 | "在编" |
| `reim_name` | String | 100 | ✅ | 报销项目名称 | "岗位工资" |
| `reim_type` | String | 50 | ✅ | **报销类型**：具体的工资项目类型 | "postSalary" |
| `reim_amt` | BigDecimal | 15,2 | ✅ | 报销金额 | 50000.00 |
| `reim_desc` | String | 200 | ❌ | 报销摘要 | "2024年1月岗位工资" |
| `emp_code` | String | 20 | ❌ | 员工编号（个人扣款时必填） | "EMP001" |
| `emp_name` | String | 50 | ❌ | 员工姓名 | "李四" |
| `emp_count` | Integer | - | ❌ | 人数统计 | 10 |

#### 2.2.4 工资类型分类图

```mermaid
graph LR
    A[💼 工资类型分类] --> B[1️⃣ 工资计提]
    A --> C[2️⃣ 工资发放]
    A --> D[3️⃣ 企业四险两金]
    A --> E[4️⃣ 工会经费]

    B --> B1[📊 直接生成凭证]
    B --> B2[🏢 按科室分类]
    B --> B3[👥 按人员类型]

    C --> C1[🔄 报销审批流程]
    C --> C2[✅ 个人扣款配置检查]
    C --> C3[🏦 银行存款发放]

    D --> D1[📊 直接生成凭证]
    D --> D2[🛡️ 五险一金]
    D --> D3[💼 职业年金]

    E --> E1[📊 直接生成凭证]
    E --> E2[🏛️ 工会费用]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
```

### 2.3 当前业务规则分析

#### 2.3.1 工资类型处理规则
根据系统实际代码分析，工资类型(salaryType)定义如下：
- **salaryType=1**：工资计提 → 直接生成凭证
- **salaryType=2**：工资发放+个人三险两金代扣 → 走报销审批流程
- **salaryType=3**：企业四险两金计提 → 直接生成凭证
- **salaryType=4**：工会经费 → 直接生成凭证

```java
// 当前硬编码的业务逻辑（来自前端代码分析）
switch (row.salaryType) {
    case '1': return '工资计提'           // 直接生成凭证
    case '2': return '工资发放+三险两金'   // 报销审批流程
    case '3': return '企业四险两金'       // 直接生成凭证
    case '4': return '工会经费'          // 直接生成凭证
}

// 个人代扣类型工资凭证特殊处理逻辑
if (row.salaryType == '2') {
    // 判断个人扣款是否都配置了配置项,未配置需先配置
    const indiToConfig = await queryToConfigSalary({id: checkedRowKeys.value[0]})
    if (indiToConfig.code == 200 && indiToConfig.data.length > 0) {
        // 弹出界面进行个人扣款配置
        return "需要配置个人扣款";
    }
}
```

#### 2.3.2 详细业务规则分类

##### 一、工资计提规则(salaryType=1)

**🏥 人员类型分类图**

```mermaid
graph TD
    A[👥 人员类型分类] --> B[📋 在编人员]
    A --> C[🎯 招聘人员]
    A --> D[⏰ 临聘人员]
    A --> E[🔄 借调人员]
    A --> F[🔙 返聘人员]

    B --> B1[在编]
    B --> B2[血防占编]

    C --> C1[编外-医技]
    C --> C2[编外-护理]
    C --> C3[编外-辅助岗位]
    C --> C4[编外-医技-见习]
    C --> C5[编外-护理-见习]
    C --> C6[编外-辅助岗位-见习]

    D --> D1[编外-其他专技]
    D --> D2[编外-后勤]
    D --> D3[编外-其他专技-见习]
    D --> D4[编外-后勤-见习]

    E --> E1[借调]

    F --> F1[返聘]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

**🏢 科室类型与工资项目映射图**

```mermaid
graph LR
    A[🏥 科室类型] --> B[🏥 业务科室<br/>deptType=1]
    A --> C[🏢 管理科室<br/>deptType=2]

    B --> B1[👥 在编/招聘人员]
    B --> B2[⏰ 临聘人员]

    C --> C1[📋 在编人员]
    C --> C2[🎯 招聘人员]
    C --> C3[⏰ 临聘人员]

    B1 --> B1A[💰 岗位工资]
    B1 --> B1B[📊 薪级工资]
    B1 --> B1C[👩‍⚕️ 护士10%]
    B1 --> B1D[🌍 地区附加津贴]
    B1 --> B1E[⏰ 护龄补贴]
    B1 --> B1F[🎯 基础绩效工资]
    B1 --> B1G[➕ 人力临时增加]
    B1 --> B1H[💼 财务临时增加]

    B2 --> B2A[💰 岗位工资]
    B2 --> B2B[➕ 人力临时增加]
    B2 --> B2C[💼 财务临时增加]

    C1 --> C1A[💰 岗位工资]
    C1 --> C1B[📊 薪级工资]
    C1 --> C1C[👩‍⚕️ 护士10%]
    C1 --> C1D[🌍 地区附加津贴]
    C1 --> C1E[⏰ 护龄补贴]
    C1 --> C1F[🎯 基础绩效工资]
    C1 --> C1G[➕ 人力临时增加]
    C1 --> C1H[💼 财务临时增加]

    C2 --> C2A[💰 岗位工资]
    C2 --> C2B[📊 薪级工资]
    C2 --> C2C[👩‍⚕️ 护士10%]
    C2 --> C2D[🌍 地区附加津贴]
    C2 --> C2E[⏰ 护龄补贴]
    C2 --> C2F[🎯 基础绩效工资]
    C2 --> C2G[➕ 人力临时增加]
    C2 --> C2H[💼 财务临时增加]

    C3 --> C3A[💰 岗位工资]
    C3 --> C3B[➕ 人力临时增加]
    C3 --> C3C[💼 财务临时增加]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
```

根据系统常量定义(EcsConst.java)，人员类型分类如下：
- **在编人员**：`["在编","血防占编"]`
- **招聘人员**：`["编外-医技","编外-护理","编外-辅助岗位","编外-医技-见习","编外-护理-见习","编外-辅助岗位-见习"]`
- **临聘人员**：`["编外-其他专技","编外-后勤","编外-其他专技-见习","编外-后勤-见习"]`
- **借调人员**：`["借调"]`
- **返聘人员**：`["返聘"]`

##### 二、企业四险两金计提规则(salaryType=3)

**🛡️ 企业四险两金分类图**

```mermaid
graph TD
    A[🏢 企业四险两金] --> B[🛡️ 社会保险]
    A --> C[🏠 住房保障]
    A --> D[💼 补充保障]
    A --> E[🏛️ 其他费用]

    B --> B1[👴 养老保险]
    B --> B2[🏥 医疗保险]
    B --> B3[💼 失业保险]
    B --> B4[⚠️ 工伤保险]

    C --> C1[🏠 住房公积金]

    D --> D1[💰 职业年金]

    E --> E1[🏛️ 工会经费]

    B1 --> B1A[机关事业单位基本养老保险缴费<br/>pensionInsuranceEntp]
    B1 --> B1B[职工企业基本养老保险缴费<br/>pensionInsuranceEntp2]

    B2 --> B2A[职工基本医疗保险缴费<br/>medicalInsuranceEntp]

    B3 --> B3A[职工失业保险缴费<br/>unemploymentInsuranceEntp]

    B4 --> B4A[职工工伤保险缴费<br/>injrInsuEntp]

    C1 --> C1A[住房公积金<br/>housingFundEntp]

    D1 --> D1A[职业年金缴费<br/>occupationalAnnuityEntp]

    E1 --> E1A[工会经费支出<br/>pubFeeEntp]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
```

**💰 企业缴费项目明细表**

| 保险类型 | 项目名称 | 常量名称 | 科目代码 | 说明 |
|----------|----------|----------|----------|------|
| 🛡️ **社会保险** | | | | |
| 👴 养老保险 | 机关事业单位基本养老保险缴费 | `pensionInsuranceEntp` | ******** | 事业单位人员 |
| 👴 养老保险 | 职工企业基本养老保险缴费 | `pensionInsuranceEntp2` | ******** | 企业职工 |
| 🏥 医疗保险 | 职工基本医疗保险缴费 | `medicalInsuranceEntp` | ******** | 基本医疗保险 |
| 💼 失业保险 | 职工失业保险缴费 | `unemploymentInsuranceEntp` | ******** | 失业保险 |
| ⚠️ 工伤保险 | 职工工伤保险缴费 | `injrInsuEntp` | 22110306 | 工伤保险 |
| 🏠 **住房保障** | | | | |
| 🏠 住房公积金 | 住房公积金 | `housingFundEntp` | 221104 | 住房公积金 |
| 💼 **补充保障** | | | | |
| 💰 职业年金 | 职业年金缴费 | `occupationalAnnuityEntp` | ******** | 职业年金 |
| 🏛️ **其他费用** | | | | |
| 🏛️ 工会经费 | 工会经费支出 | `pubFeeEntp` | 224199 | 工会经费 |

根据系统常量定义，企业缴纳项目包括：
- 机关事业单位基本养老保险缴费 (pensionInsuranceEntp)
- 职工企业基本养老保险缴费 (pensionInsuranceEntp2)
- 职工基本医疗保险缴费 (medicalInsuranceEntp)
- 职工失业保险缴费 (unemploymentInsuranceEntp)
- 职工工伤保险缴费 (injrInsuEntp)
- 住房公积金 (housingFundEntp)
- 职业年金缴费 (occupationalAnnuityEntp)
- 工会经费支出 (pubFeeEntp)

##### 三、工资发放+个人三险两金代扣规则(salaryType=2)

**👤 个人代扣项目分类图**

```mermaid
graph TD
    A[👤 个人代扣项目] --> B[🛡️ 社会保险]
    A --> C[💰 税费]
    A --> D[🏠 生活费用]
    A --> E[⏰ 临时扣款]

    B --> B1[👴 养老保险<br/>pensionInsurance]
    B --> B2[🏥 医疗保险<br/>medicalInsurance]
    B --> B3[💼 失业保险<br/>unemploymentInsurance]
    B --> B4[🏠 住房基金<br/>housingFund]
    B --> B5[💰 职业年金<br/>occupationalAnnuity]

    C --> C1[💰 个人所得税<br/>personalIncomeTaxDeduction]

    D --> D1[🏠 房租费<br/>rent]
    D --> D2[💧 水费<br/>waterCharge]

    E --> E1[👥 人力临时扣款<br/>temporaryReduceSalary]
    E --> E2[💼 财务临时扣款<br/>temporaryReduceSalary2]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#fce4ec
```

**💳 工资发放凭证结构图**

```mermaid
graph LR
    A[💳 工资发放凭证] --> B[📊 借方科目]
    A --> C[📊 贷方科目]

    B --> B1[💰 应付职工薪酬-基本工资<br/>salaryTotalBase]
    B --> B2[🌍 应付职工薪酬-津贴补贴<br/>221102]
    B --> B3[🎯 应付职工薪酬-绩效工资<br/>221103]
    B --> B4[➖ 冲账科目（负数）]
    B --> B5[🔄 特殊往来账处理]

    C --> C1[🛡️ 个人五险一金]
    C --> C2[💰 个人所得税]
    C --> C3[🏠 生活费用扣款]
    C --> C4[⏰ 临时扣款]
    C --> C5[🏦 银行存款<br/>bankDeposit]

    C1 --> C1A[👴 养老保险 ********]
    C1 --> C1B[🏥 医疗保险 ********]
    C1 --> C1C[💼 失业保险 ********]
    C1 --> C1D[🏠 住房公积金 221104]
    C1 --> C1E[💰 职业年金 ********]

    C2 --> C2A[💰 个人所得税 210207]

    C3 --> C3A[🏠 房租费 224199]
    C3 --> C3B[💧 水费 224199]

    C4 --> C4A[👥 人力临时扣款 224199]
    C4 --> C4B[💼 财务临时扣款 224199]

    style A fill:#e3f2fd
    style B fill:#ffebee
    style C fill:#e8f5e8
```

**📊 个人代扣项目明细表**

| 扣款类型 | 项目名称 | 常量名称 | 科目代码 | 说明 |
|----------|----------|----------|----------|------|
| 🛡️ **社会保险** | | | | |
| 👴 养老保险 | 养老保险 | `pensionInsurance` | ******** | 个人缴纳部分 |
| 🏥 医疗保险 | 医疗保险 | `medicalInsurance` | ******** | 个人缴纳部分 |
| 💼 失业保险 | 失业保险 | `unemploymentInsurance` | ******** | 个人缴纳部分 |
| 🏠 住房基金 | 住房基金 | `housingFund` | 221104 | 个人缴纳部分 |
| 💰 职业年金 | 职业年金 | `occupationalAnnuity` | ******** | 个人缴纳部分 |
| 💰 **税费** | | | | |
| 💰 个人所得税 | 个人所得税 | `personalIncomeTaxDeduction` | 210207 | 代扣代缴 |
| 🏠 **生活费用** | | | | |
| 🏠 房租费 | 房租费 | `rent` | 224199 | 住房租金 |
| 💧 水费 | 水费 | `waterCharge` | 224199 | 水电费用 |
| ⏰ **临时扣款** | | | | |
| 👥 人力临时扣款 | 人力临时扣款 | `temporaryReduceSalary` | 224199 | 人力部门扣款 |
| 💼 财务临时扣款 | 财务临时扣款 | `temporaryReduceSalary2` | 224199 | 财务部门扣款 |

根据系统常量定义，个人代扣项目包括：
- 养老保险 (pensionInsurance)
- 医疗保险 (medicalInsurance)
- 失业保险 (unemploymentInsurance)
- 住房基金 (housingFund)
- 职业年金 (occupationalAnnuity)
- 个人所得税 (personalIncomeTaxDeduction)
- 房租费 (rent)
- 水费 (waterCharge)
- 人力临时扣款 (temporaryReduceSalary)
- 财务临时扣款 (temporaryReduceSalary2)

#### 2.3.3 科目映射规则
- **部门类型判断**：业务科室 vs 管理科室
- **人员类型判断**：在编、招聘、临聘
- **工资项目分类**：基本工资、津贴补贴、绩效工资
- **借贷方向**：根据业务类型自动判断
- **辅助项配置**：部门、项目、资金性质

#### 2.3.4 特殊业务处理规则
根据系统实际代码分析，特殊人员处理规则如下：

**特殊往来账处理人员**：
- 维修班人员：邹毅、田晓辉、刘黎、刘亨强、陈洪浩、吕勇、苟鹏、古代勇、林贤培、张丽、邓钟
- 总务科特殊人员：吴思琪
- 住院收费室特殊人员：罗亚婕
- 处理方式：借方科目使用"其他应收款-其他121803[往来单位4414]"

**单采血浆站特殊处理**：
- 适用范围：血浆站全体人员
- 处理方式：借方科目使用"其他应收款-其他121803[往来单位7004]"

**个人扣款配置检查**：
- 工资发放类型(salaryType=2)需要检查个人扣款配置
- 未配置的员工需要先进行个人扣款配置才能生成凭证

**预算科目映射**：
- 预算科目：7201010301[事业支出-医院支出其他资金支出-基本支出]
- 适用于所有工资类型的预算分录生成

### 2.4 现有业务代码路径分析

#### 2.4.1 核心业务代码位置
基于系统代码分析，当前工资凭证相关的业务逻辑分布在以下文件中：

**后端代码路径**：
```
med-ecs/src/main/java/com/jp/med/ecs/modules/reimMgt/
├── service/
│   ├── read/impl/
│   │   └── EcsReimSalaryTaskReadServiceImpl.java     # 工资任务查询服务
│   └── write/impl/
│       └── EcsReimSalaryTaskWriteServiceImpl.java    # 工资任务写入服务
├── controller/
│   └── EcsReimSalaryTaskController.java              # 工资任务控制器
└── constant/
    └── EcsConst.java                                 # 常量定义（人员类型、工资项目等）

med-common/src/main/java/com/jp/med/common/
├── dto/ecs/
│   ├── EcsReimSalaryTask.java                        # 工资任务DTO
│   └── EcsReimSalaryTaskDetail.java                  # 工资任务明细DTO
└── constant/
    ├── MedConst.java                                 # 医疗系统常量
    └── ReimTypeConst.java                            # 报销类型常量
```

**前端代码路径**：
```
src/views/modules/
├── ecs/reimMgt/salaryReim/index.vue                  # 工资报销管理页面
├── ecs/reimNew/salaryReim/index.vue                  # 新版工资报销页面
└── erp/
    ├── vcrGen/vcrPane/components/salaryVcr.vue       # 工资凭证生成组件
    └── config/salaryConfig/index.vue                 # 工资凭证科目映射配置

src/api/erp/vcr/vcrGen/vcrGen.ts                      # 凭证生成API接口
```

#### 2.4.2 关键业务逻辑代码片段

**工资类型判断逻辑**（位于前端组件）：
```javascript
// src/views/modules/ecs/reimNew/salaryReim/index.vue:330-337
switch (row.salaryType) {
    case '1': return '工资计提'           // 直接生成凭证
    case '2': return '工资发放+三险两金'   // 报销审批流程
    case '3': return '企业四险两金'       // 直接生成凭证
    case '4': return '工会经费'          // 直接生成凭证
}

// src/views/modules/erp/vcrGen/vcrPane/components/salaryVcr.vue:427-435
if (row.salaryType == '2') {
    // 个人代扣类型工资凭证，判断个人扣款是否都配置了配置项
    const indiToConfig = await queryToConfigSalary({id: checkedRowKeys.value[0]})
    if (indiToConfig.code == 200 && indiToConfig.data.length > 0) {
        // 弹出界面进行个人扣款配置
        indiToConfigModal.value = true
        return
    }
}
```

**人员类型常量定义**（位于EcsConst.java）：
```java
// med-ecs/src/main/java/com/jp/med/ecs/modules/reimMgt/constant/EcsConst.java:271-284
//在编
String[] ESTAB_STR_ARR = new String[]{"在编","血防占编"};

//招聘
String[] HIRE_STR_ARR = new String[]{"编外-医技","编外-护理","编外-辅助岗位",
                                    "编外-医技-见习","编外-护理-见习","编外-辅助岗位-见习"};

//临聘
String[] TEMP_HIRE_STR_ARR = new String[]{"编外-其他专技","编外-后勤",
                                         "编外-其他专技-见习","编外-后勤-见习"};

//借调
String[] SECONDMENT_STR_ARR = new String[]{"借调"};

//返聘
String[] REHIRE_STR_ARR = new String[]{"返聘"};
```

**工资项目常量定义**（位于EcsConst.java）：
```java
// med-ecs/src/main/java/com/jp/med/ecs/modules/reimMgt/constant/EcsConst.java:184-267
//岗位工资
String POST_SALARY = "postSalary";
// 薪级工资
String SAL_GRADE_SALARY = "salGradeSalary";
// 护士 10%
String NURSE_SALARY = "nurseSalary";
// 地区附加津贴
String AREA_SALARY = "areaSalary";
// 护龄津贴
String AGE_SALARY = "ageSalary";
// 基础性绩效
String BASIC_PERF = "basicPerf";
// 人力临时增加
String TEMPORARY_ADD_SALARY = "temporaryAddSalary";
// 财务临时增加
String TEMPORARY_ADD_SALARY2 = "temporaryAddSalary2";

// 养老保险
String PENSION_INSURANCE = "pensionInsurance";
// 医疗保险
String MEDICAL_INSURANCE = "medicalInsurance";
// 失业保险
String UNEMPLOYMENT_INSURANCE = "unemploymentInsurance";
// 住房基金
String HOUSING_FUND = "housingFund";
// 职业年金
String OCCUPATION_ANNUITY = "occupationalAnnuity";
//个人所得税
String PERSON_TAX = "personalIncomeTaxDeduction";
```

### 2.5 存在问题
1. **规则硬编码**：业务规则直接写在Java代码和前端组件中，修改需要重新部署
2. **维护困难**：业务人员无法直接修改规则，依赖开发人员
3. **扩展性差**：新增工资类型或规则变更需要修改多处代码
4. **测试复杂**：规则变更影响面广，测试成本高
5. **一致性风险**：多处相似逻辑容易出现不一致
6. **代码分散**：业务逻辑分散在前后端多个文件中，难以统一管理

## 3. 解决方案设计 🎨

### 3.1 Drools规则引擎架构
```mermaid
graph TB
    A[工资凭证请求] --> B[规则引擎入口]
    B --> C[事实对象构建]
    C --> D[Drools规则引擎]
    D --> E[规则执行]
    E --> F[结果收集]
    F --> G[凭证生成决策]
    
    H[规则配置管理] --> I[规则文件生成]
    I --> J[规则热加载]
    J --> D
    
    K[规则管理界面] --> H
```

### 3.2 核心组件设计

#### 3.2.1 规则引擎服务(SalaryVoucherRuleService)
```java
@Service
public class SalaryVoucherRuleService {
    
    /**
     * 执行工资凭证规则
     */
    public SalaryVoucherDecision executeRules(SalaryTaskFact fact);
    
    /**
     * 重载规则
     */
    public void reloadRules();
}
```

#### 3.2.2 事实对象(Fact Objects)
```java
// 工资任务事实
public class SalaryTaskFact {
    private String salaryType;      // 工资类型：1-工资计提，2-工资发放，3-企业四险两金，4-工会经费
    private BigDecimal shouldPay;   // 应发金额
    private BigDecimal reducePay;   // 扣款金额
    private BigDecimal realPay;     // 实发金额
    private String ffMth;           // 发放月份
    private Integer num;            // 工资条数
    private String bchno;           // 批次号
    private List<SalaryDetailFact> details; // 明细
    private ProcessType processType; // 处理类型
    private String voucherTemplate; // 凭证模板
    private List<VoucherEntry> voucherEntries; // 凭证分录
    private List<String> validationErrors; // 验证错误
}

// 工资明细事实
public class SalaryDetailFact {
    private String orgId;           // 科室ID
    private String orgName;         // 科室名称
    private String deptType;        // 部门类型：业务科室、管理科室
    private String reimType;        // 报销类型：岗位工资、薪级工资、护士10%等
    private BigDecimal reimAmt;     // 报销金额
    private String empCode;         // 员工编号
    private String empName;         // 员工姓名
    private String empType;         // 人员类型：在编、招聘、临聘
    private Integer empCount;       // 人数
    private String fundType;        // 资金类型
    private String subjectCode;     // 会计科目代码
    private String budgetSubject;   // 预算科目代码
}

// 工资上下文事实
public class SalaryContextFact {
    private String hospitalCode;    // 医院代码
    private String currentMonth;    // 当前月份
    private String currentYear;     // 当前年度
    private Map<String, String> deptMapping; // 部门映射
    private Map<String, String> subjectMapping; // 科目映射
    private List<String> specialEmployees; // 特殊员工列表
    private Map<String, Object> systemConfig; // 系统配置
}

// 个人扣款配置事实
public class PersonalDeductionConfig {
    private String empCode;         // 员工编号
    private String deductionType;   // 扣款类型
    private BigDecimal amount;      // 扣款金额
    private String status;          // 状态
}
```

#### 3.2.3 决策结果(Decision Objects)
```java
// 凭证生成决策
public class SalaryVoucherDecision {
    private ProcessType processType;    // 处理类型：直接生成/报销流程
    private String voucherTemplate;     // 凭证模板
    private List<VoucherEntry> entries; // 凭证分录
    private List<BudgetEntry> budgetEntries; // 预算分录
    private List<String> validationErrors; // 验证错误
    private Map<String, Object> params; // 附加参数
    private boolean success;            // 执行是否成功
    private String message;             // 执行消息
}

// 凭证分录
public class VoucherEntry {
    private String subjectCode;         // 会计科目代码
    private String subjectName;         // 会计科目名称
    private BigDecimal debitAmount;     // 借方金额
    private BigDecimal creditAmount;    // 贷方金额
    private String direction;           // 借贷方向：DEBIT/CREDIT
    private Map<String, String> auxiliaryItems; // 辅助项
    private String description;         // 摘要
}

// 预算分录
public class BudgetEntry {
    private String budgetSubject;       // 预算科目代码
    private String budgetSubjectName;   // 预算科目名称
    private BigDecimal amount;          // 金额
    private String description;         // 说明
}

// 处理类型枚举
public enum ProcessType {
    DIRECT_VOUCHER("直接生成凭证"),
    REIMBURSEMENT_FLOW("报销审批流程");

    private String description;

    ProcessType(String description) {
        this.description = description;
    }
}

// 凭证模板枚举
public enum VoucherTemplate {
    SALARY_ACCRUAL("工资计提"),
    SALARY_PAYMENT("工资发放"),
    ENTERPRISE_INSURANCE("企业四险两金"),
    UNION_FEE("工会经费");

    private String description;

    VoucherTemplate(String description) {
        this.description = description;
    }
}
```

### 3.3 规则分类设计

#### 3.3.1 工资类型判断规则

**🔄 工资类型判断规则流程图**

```mermaid
flowchart TD
    A[📋 工资任务输入] --> B{🏷️ 工资类型判断}

    B -->|salaryType=1| C[📊 R001_工资计提类型判断]
    B -->|salaryType=2| D[💳 R002_工资发放类型判断]
    B -->|salaryType=3| E[🛡️ R003_企业四险两金类型判断]
    B -->|salaryType=4| F[🏛️ R004_工会经费类型判断]
    B -->|其他值| G[❌ R005_未知工资类型处理]

    C --> C1[✅ 直接生成凭证<br/>DIRECT_VOUCHER]
    C --> C2[📋 工资计提模板<br/>SALARY_ACCRUAL]

    D --> D1[🔄 报销审批流程<br/>REIMBURSEMENT_FLOW]
    D --> D2[💳 工资发放模板<br/>SALARY_PAYMENT]

    E --> E1[✅ 直接生成凭证<br/>DIRECT_VOUCHER]
    E --> E2[🛡️ 企业保险模板<br/>ENTERPRISE_INSURANCE]

    F --> F1[✅ 直接生成凭证<br/>DIRECT_VOUCHER]
    F --> F2[🏛️ 工会经费模板<br/>UNION_FEE]

    G --> G1[❌ 验证错误<br/>ValidationError]

    C1 --> H[📄 生成凭证]
    D1 --> I[🔄 启动审批流程]
    E1 --> H
    F1 --> H
    G1 --> J[❌ 终止处理]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#ffebee
    style H fill:#e8f5e8
    style I fill:#fff3e0
    style J fill:#ffebee
```

**📋 规则详细说明表**

| 规则编号 | 规则名称 | 优先级 | 条件 | 动作 | 结果 |
|----------|----------|--------|------|------|------|
| R001 | 工资计提类型判断 | 100 | salaryType == "1" | 设置直接生成凭证 | 工资计提凭证 |
| R002 | 工资发放类型判断 | 100 | salaryType == "2" | 设置报销审批流程 | 工资发放审批 |
| R003 | 企业四险两金类型判断 | 100 | salaryType == "3" | 设置直接生成凭证 | 企业保险凭证 |
| R004 | 工会经费类型判断 | 100 | salaryType == "4" | 设置直接生成凭证 | 工会经费凭证 |
| R005 | 未知工资类型处理 | 50 | salaryType not in ("1","2","3","4") | 添加验证错误 | 错误提示 |

**📝 Drools规则实现**

将工资类型判断细分为具体的业务规则：

```drools
// 规则组：工资类型处理规则
rule "R001_工资计提类型判断"
    salience 100
when
    $task: SalaryTaskFact(salaryType == "1")
then
    $task.setProcessType(ProcessType.DIRECT_VOUCHER);
    $task.setVoucherTemplate("SALARY_ACCRUAL");
    $task.setRuleApplied("R001");
    System.out.println("应用规则R001：工资计提直接生成凭证");
end

rule "R002_工资发放类型判断"
    salience 100
when
    $task: SalaryTaskFact(salaryType == "2")
then
    $task.setProcessType(ProcessType.REIMBURSEMENT_FLOW);
    $task.setFlowTemplate("SALARY_PAYMENT");
    $task.setRuleApplied("R002");
    System.out.println("应用规则R002：工资发放走报销审批流程");
end

rule "R003_企业四险两金类型判断"
    salience 100
when
    $task: SalaryTaskFact(salaryType == "3")
then
    $task.setProcessType(ProcessType.DIRECT_VOUCHER);
    $task.setVoucherTemplate("ENTERPRISE_INSURANCE");
    $task.setRuleApplied("R003");
    System.out.println("应用规则R003：企业四险两金直接生成凭证");
end

rule "R004_工会经费类型判断"
    salience 100
when
    $task: SalaryTaskFact(salaryType == "4")
then
    $task.setProcessType(ProcessType.DIRECT_VOUCHER);
    $task.setVoucherTemplate("UNION_FEE");
    $task.setRuleApplied("R004");
    System.out.println("应用规则R004：工会经费直接生成凭证");
end

rule "R005_未知工资类型处理"
    salience 50
when
    $task: SalaryTaskFact(salaryType not in ("1", "2", "3", "4"))
then
    $task.addValidationError("未知的工资类型：" + $task.getSalaryType());
    $task.setRuleApplied("R005");
    System.out.println("应用规则R005：未知工资类型错误处理");
end
```

#### 3.3.2 工资计提科目映射规则
将工资计提科目映射细分为每个具体的工资项目规则：

```drools
**🏥 业务科室工资计提规则流程图**

```mermaid
flowchart TD
    A[📋 业务科室工资计提<br/>salaryType=1 & deptType=1] --> B{👥 人员类型判断}

    B -->|在编/招聘人员| C[🎯 在编招聘人员规则组]
    B -->|临聘人员| D[⏰ 临聘人员规则组]

    C --> C1[💰 R101_岗位工资计提]
    C --> C2[📊 R103_薪级工资计提]
    C --> C3[👩‍⚕️ R104_护士10%计提]
    C --> C4[🌍 R105_地区附加津贴计提]
    C --> C5[⏰ R106_护龄补贴计提]
    C --> C6[🎯 R107_基础绩效工资计提]
    C --> C7[➕ R108_人力临时增加计提]
    C --> C8[💼 R109_财务临时增加计提]

    D --> D1[💰 R102_临聘岗位工资计提]
    D --> D2[➕ 人力临时增加计提]
    D --> D3[💼 财务临时增加计提]

    C1 --> E[📄 生成凭证分录]
    C2 --> E
    C3 --> E
    C4 --> E
    C5 --> E
    C6 --> E
    C7 --> E
    C8 --> E
    D1 --> E
    D2 --> E
    D3 --> E

    E --> F[💰 借方：**********<br/>业务活动费用-其他经费-人员经费]
    E --> G[💳 贷方：221101/221102/221103<br/>应付职工薪酬]

    F --> H[📋 辅助项配置<br/>部门+项目]
    G --> I[✅ 凭证生成完成]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#ffebee
    style G fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#e8f5e8
```

**📊 业务科室工资计提规则映射表**

| 规则编号 | 工资项目 | 人员类型 | 借方科目 | 贷方科目 | 辅助项 |
|----------|----------|----------|----------|----------|--------|
| R101 | 岗位工资 | 在编/招聘 | ********** | 221101 | 部门+项目 |
| R102 | 岗位工资 | 临聘 | ********** | 221101 | 部门+项目 |
| R103 | 薪级工资 | 在编/招聘 | ********** | 221101 | 部门+项目 |
| R104 | 护士10% | 在编/招聘 | ********** | 221101 | 部门+项目 |
| R105 | 地区附加津贴 | 全部 | ********** | 221102 | 部门+项目 |
| R106 | 护龄补贴 | 全部 | ********** | 221102 | 部门+项目 |
| R107 | 基础绩效工资 | 全部 | ********** | 221103 | 部门+项目 |
| R108 | 人力临时增加 | 全部 | ********** | 221101 | 部门+项目 |
| R109 | 财务临时增加 | 全部 | ********** | 221101 | 部门+项目 |

**📝 Drools规则实现**

```drools
// 规则组：业务科室工资计提规则
rule "R101_业务科室在编招聘岗位工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "岗位工资",
        deptType == "1", // 业务科室
        empType in ("在编", "血防占编", "编外-医技", "编外-护理", "编外-辅助岗位",
                   "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********"); // 业务活动费用-其他经费-人员经费
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("岗位工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "岗位工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101"); // 应付职工薪酬-基本工资
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("岗位工资计提-" + $detail.getOrgName());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R101");
end

rule "R102_业务科室临聘岗位工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "岗位工资",
        deptType == "1", // 业务科室
        empType in ("编外-其他专技", "编外-后勤", "编外-其他专技-见习", "编外-后勤-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("临聘岗位工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "临聘岗位工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("临聘岗位工资计提-" + $detail.getOrgName());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R102");
end

rule "R103_业务科室薪级工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "薪级工资",
        deptType == "1",
        empType in ("在编", "血防占编", "编外-医技", "编外-护理", "编外-辅助岗位",
                   "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("薪级工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "薪级工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R103");
end

rule "R104_业务科室护士10%计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "护士10%",
        deptType == "1",
        empType in ("在编", "血防占编", "编外-医技", "编外-护理", "编外-辅助岗位",
                   "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("护士10%计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "护士津贴"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R104");
end

rule "R105_业务科室地区附加津贴计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "地区附加津贴",
        deptType == "1"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("地区附加津贴计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "地区津贴"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221102"); // 应付职工薪酬-国家统一规定的津贴补贴
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R105");
end

rule "R106_业务科室护龄补贴计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "护龄补贴",
        deptType == "1"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("护龄补贴计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "护龄津贴"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221102");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R106");
end

rule "R107_业务科室基础绩效工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "基础绩效工资",
        deptType == "1"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("基础绩效工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "绩效工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221103"); // 应付职工薪酬-规范津贴补贴
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R107");
end

rule "R108_业务科室人力临时增加计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "人力临时增加",
        deptType == "1"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("人力临时增加计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "临时工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R108");
end

rule "R109_业务科室财务临时增加计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "财务临时增加",
        deptType == "1"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("财务临时增加计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "财务调整"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R109");
end
```

#### 3.3.3 管理科室工资计提规则

**🏢 管理科室工资计提规则流程图**

```mermaid
flowchart TD
    A[🏢 管理科室工资计提<br/>salaryType=1 & deptType=2] --> B{👥 人员类型判断}

    B -->|在编人员| C[📋 在编人员规则组]
    B -->|招聘人员| D[🎯 招聘人员规则组]
    B -->|临聘人员| E[⏰ 临聘人员规则组]

    C --> C1[💰 R201_在编岗位工资计提]
    C --> C2[📊 R204_薪级工资计提]
    C --> C3[🌍 R205_地区附加津贴计提]

    D --> D1[💰 R202_招聘岗位工资计提]
    D --> D2[📊 招聘薪级工资计提]
    D --> D3[🌍 招聘地区附加津贴计提]

    E --> E1[💰 R203_临聘岗位工资计提]
    E --> E2[➕ 临聘人力临时增加]
    E --> E3[💼 临聘财务临时增加]

    C1 --> F[📄 生成凭证分录]
    C2 --> F
    C3 --> F
    D1 --> F
    D2 --> F
    D3 --> F
    E1 --> F
    E2 --> F
    E3 --> F

    F --> G[💰 借方：5201030199<br/>单位管理费用-其他经费-人员经费]
    F --> H[💳 贷方：221101/221102/221103<br/>应付职工薪酬]

    G --> I[📋 辅助项配置<br/>部门+项目]
    H --> J[✅ 凭证生成完成]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#ffebee
    style H fill:#e8f5e8
    style I fill:#fff3e0
    style J fill:#e8f5e8
```

**📊 管理科室工资计提规则映射表**

| 规则编号 | 工资项目 | 人员类型 | 借方科目 | 贷方科目 | 辅助项 |
|----------|----------|----------|----------|----------|--------|
| R201 | 岗位工资 | 在编 | 5201030199 | 221101 | 部门+项目 |
| R202 | 岗位工资 | 招聘 | 5201030199 | 221101 | 部门+项目 |
| R203 | 岗位工资 | 临聘 | 5201030199 | 221101 | 部门+项目 |
| R204 | 薪级工资 | 全部 | 5201030199 | 221101 | 部门+项目 |
| R205 | 地区附加津贴 | 全部 | 5201030199 | 221102 | 部门+项目 |

**📝 Drools规则实现**

```drools
// 规则组：管理科室工资计提规则
rule "R201_管理科室在编岗位工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "岗位工资",
        deptType == "2", // 管理科室
        empType in ("在编", "血防占编")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("5201030199"); // 单位管理费用-其他经费-人员经费
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("管理科室岗位工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "岗位工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R201");
end

rule "R202_管理科室招聘岗位工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "岗位工资",
        deptType == "2",
        empType in ("编外-医技", "编外-护理", "编外-辅助岗位",
                   "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("5201030199");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("管理科室招聘岗位工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "招聘岗位工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R202");
end

rule "R203_管理科室临聘岗位工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "岗位工资",
        deptType == "2",
        empType in ("编外-其他专技", "编外-后勤", "编外-其他专技-见习", "编外-后勤-见习")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("5201030199");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("管理科室临聘岗位工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "临聘岗位工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R203");
end

// 管理科室其他工资项目规则（薪级工资、护士10%、津贴等）
rule "R204_管理科室薪级工资计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "薪级工资",
        deptType == "2"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("5201030199");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("管理科室薪级工资计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "薪级工资"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221101");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R204");
end

rule "R205_管理科室地区附加津贴计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(
        reimType == "地区附加津贴",
        deptType == "2"
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("5201030199");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("管理科室地区附加津贴计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "地区津贴"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221102");
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R205");
end

#### 3.3.4 工资发放科目映射规则

**💳 工资发放科目映射规则流程图**

```mermaid
flowchart TD
    A[💳 工资发放处理<br/>salaryType=2] --> B{🔄 处理类型判断}

    B -->|冲销处理| C[📊 工资冲销规则组]
    B -->|个人代扣| D[👤 个人代扣规则组]
    B -->|实发处理| E[🏦 银行存款规则]

    C --> C1[💰 R301_基本工资冲销]
    C --> C2[🌍 R302_津贴补贴冲销]
    C --> C3[🎯 R303_绩效工资冲销]

    D --> D1[👴 R311_个人养老保险代扣]
    D --> D2[🏥 R312_个人医疗保险代扣]
    D --> D3[💼 R313_个人失业保险代扣]
    D --> D4[🏠 R314_个人住房公积金代扣]
    D --> D5[💰 R315_个人职业年金代扣]
    D --> D6[💰 R316_个人所得税代扣]
    D --> D7[🏛️ R317_工会会费代扣]
    D --> D8[🏠 R318_房租费代扣]
    D --> D9[💧 R319_水费代扣]
    D --> D10[👥 R320_人力临时扣款]
    D --> D11[💼 R321_财务临时扣款]

    E --> E1[🏦 R330_银行存款实发工资]

    C1 --> F[📄 生成凭证分录]
    C2 --> F
    C3 --> F
    D1 --> F
    D2 --> F
    D3 --> F
    D4 --> F
    D5 --> F
    D6 --> F
    D7 --> F
    D8 --> F
    D9 --> F
    D10 --> F
    D11 --> F
    E1 --> F

    F --> G[💰 借方：221101/221102/221103<br/>应付职工薪酬冲销]
    F --> H[💳 贷方：********-22110306/221104/210207/224199<br/>个人代扣项目]
    F --> I[🏦 贷方：100201<br/>银行存款]

    G --> J[✅ 凭证生成完成]
    H --> J
    I --> J

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#ffebee
    style H fill:#e8f5e8
    style I fill:#f1f8e9
    style J fill:#e8f5e8
```

**📊 工资发放规则映射表**

| 规则类型 | 规则编号 | 项目名称 | 借方科目 | 贷方科目 | 说明 |
|----------|----------|----------|----------|----------|------|
| **冲销规则** | R301 | 基本工资冲销 | 221101 | ********** | 负数冲销 |
| | R302 | 津贴补贴冲销 | 221102 | ********** | 负数冲销 |
| | R303 | 绩效工资冲销 | 221103 | ********** | 负数冲销 |
| **个人代扣** | R311 | 个人养老保险 | - | ******** | 个人缴纳部分 |
| | R312 | 个人医疗保险 | - | ******** | 个人缴纳部分 |
| | R313 | 个人失业保险 | - | ******** | 个人缴纳部分 |
| | R314 | 个人住房公积金 | - | 221104 | 个人缴纳部分 |
| | R315 | 个人职业年金 | - | ******** | 个人缴纳部分 |
| | R316 | 个人所得税 | - | 210207 | 代扣代缴 |
| | R317 | 工会会费 | - | 224199 | 个人缴纳 |
| | R318 | 房租费 | - | 224199 | 生活费用 |
| | R319 | 水费 | - | 224199 | 生活费用 |
| | R320 | 人力临时扣款 | - | 224199 | 临时扣款 |
| | R321 | 财务临时扣款 | - | 224199 | 临时扣款 |
| **实发处理** | R330 | 银行存款实发 | - | 100201 | 实际发放 |

**📝 Drools规则实现**

将工资发放科目映射细分为每个具体的代扣代缴项目：

```drools
// 规则组：工资发放冲销规则
rule "R301_基本工资冲销"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "基本工资冲销") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("221101"); // 应付职工薪酬-基本工资
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("基本工资冲销-" + $detail.getOrgName());

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("**********"); // 负数冲销
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("基本工资冲销-" + $detail.getOrgName());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R301");
end

rule "R302_津贴补贴冲销"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "津贴补贴冲销") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("221102"); // 应付职工薪酬-国家统一规定的津贴补贴
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("津贴补贴冲销-" + $detail.getOrgName());

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("**********"); // 负数冲销
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("津贴补贴冲销-" + $detail.getOrgName());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R302");
end

rule "R303_绩效工资冲销"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "绩效工资冲销") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("221103"); // 应付职工薪酬-规范津贴补贴
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("绩效工资冲销-" + $detail.getOrgName());

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("**********"); // 负数冲销
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("绩效工资冲销-" + $detail.getOrgName());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R303");
end

// 规则组：个人代扣项目规则
rule "R311_个人养老保险代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "养老保险") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-养老保险-个人缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人养老保险代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R311");
end

rule "R312_个人医疗保险代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "医疗保险") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-医疗保险-个人缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人医疗保险代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R312");
end

rule "R313_个人失业保险代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "失业保险") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-失业保险-个人缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人失业保险代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R313");
end

rule "R314_个人住房公积金代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "住房公积金") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221104"); // 应付职工薪酬-住房公积金-个人缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人住房公积金代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R314");
end

rule "R315_个人职业年金代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "职业年金") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-职业年金-个人缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人职业年金代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R315");
end

rule "R316_个人所得税代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "个人所得税") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("210207"); // 应交税费-个人所得税
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("个人所得税代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R316");
end

rule "R317_工会会费代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "工会会费") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-工会会费
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("工会会费代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R317");
end

rule "R318_房租费代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "房租费") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-房租费
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("房租费代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R318");
end

rule "R319_水费代扣"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "水费") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-水费
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("水费代扣-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R319");
end

rule "R320_人力临时扣款"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "人力临时扣款") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-临时扣款
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("人力临时扣款-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R320");
end

rule "R321_财务临时扣款"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(reimType == "财务临时扣款") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-临时扣款
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("财务临时扣款-" + $detail.getEmpName());
    creditEntry.setAuxiliaryItems(buildAuxItems("员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R321");
end

rule "R330_银行存款实发工资"
    salience 70
when
    $task: SalaryTaskFact(salaryType == "2")
    $realPay: BigDecimal() from $task.realPay
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("100201"); // 银行存款-自有存款-中国银行
    creditEntry.setCreditAmount($realPay);
    creditEntry.setDescription("实发工资-银行存款");
    creditEntry.setAuxiliaryItems(buildAuxItems("资金性质", "自有资金"));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R330");
end
```

#### 3.3.4 企业四险两金计提规则

**🛡️ 企业四险两金计提规则流程图**

```mermaid
flowchart TD
    A[🏢 企业四险两金计提<br/>salaryType=3] --> B{🛡️ 保险类型判断}

    B -->|养老保险| C[👴 R401_企业养老保险计提]
    B -->|医疗保险| D[🏥 R402_企业医疗保险计提]
    B -->|失业保险| E[💼 R403_企业失业保险计提]
    B -->|工伤保险| F[⚠️ R404_企业工伤保险计提]
    B -->|住房公积金| G[🏠 R405_企业住房公积金计提]
    B -->|职业年金| H[💰 R406_企业职业年金计提]

    C --> C1[📄 生成凭证分录]
    D --> D1[📄 生成凭证分录]
    E --> E1[📄 生成凭证分录]
    F --> F1[📄 生成凭证分录]
    G --> G1[📄 生成凭证分录]
    H --> H1[📄 生成凭证分录]

    C1 --> I[💰 借方：**********<br/>业务活动费用-其他经费-人员经费]
    D1 --> I
    E1 --> I
    F1 --> I
    G1 --> I
    H1 --> I

    C1 --> J[💳 贷方：********<br/>应付职工薪酬-养老保险-企业缴纳]
    D1 --> K[💳 贷方：********<br/>应付职工薪酬-医疗保险-企业缴纳]
    E1 --> L[💳 贷方：********<br/>应付职工薪酬-失业保险-企业缴纳]
    F1 --> M[💳 贷方：22110306<br/>应付职工薪酬-工伤保险-企业缴纳]
    G1 --> N[💳 贷方：221104<br/>应付职工薪酬-住房公积金-企业缴纳]
    H1 --> O[💳 贷方：********<br/>应付职工薪酬-职业年金-企业缴纳]

    I --> P[📋 辅助项配置<br/>部门+项目]
    J --> Q[✅ 凭证生成完成]
    K --> Q
    L --> Q
    M --> Q
    N --> Q
    O --> Q

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style H fill:#fff8e1
    style I fill:#ffebee
    style P fill:#fff3e0
    style Q fill:#e8f5e8
```

**📊 企业四险两金规则映射表**

| 规则编号 | 保险项目 | 常量名称 | 借方科目 | 贷方科目 | 说明 |
|----------|----------|----------|----------|----------|------|
| R401 | 企业养老保险 | pensionInsuranceEntp | ********** | ******** | 机关事业单位基本养老保险 |
| R402 | 企业医疗保险 | medicalInsuranceEntp | ********** | ******** | 职工基本医疗保险 |
| R403 | 企业失业保险 | unemploymentInsuranceEntp | ********** | ******** | 职工失业保险 |
| R404 | 企业工伤保险 | injrInsuEntp | ********** | 22110306 | 职工工伤保险 |
| R405 | 企业住房公积金 | housingFundEntp | ********** | 221104 | 住房公积金 |
| R406 | 企业职业年金 | occupationalAnnuityEntp | ********** | ******** | 职业年金 |

**📝 Drools规则实现**

将企业缴纳项目细分为每个具体的保险和公积金规则：

```drools
// 规则组：企业四险两金计提规则
rule "R401_企业养老保险计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "机关事业单位基本养老保险缴费") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********"); // 业务活动费用-其他经费-人员经费
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业养老保险计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "养老保险"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-养老保险-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R401");
end

rule "R402_企业医疗保险计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "职工基本医疗保险缴费") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业医疗保险计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "医疗保险"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-医疗保险-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R402");
end

rule "R403_企业失业保险计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "职工失业保险缴费") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业失业保险计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "失业保险"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-失业保险-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R403");
end

rule "R404_企业工伤保险计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "职工工伤保险缴费") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业工伤保险计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "工伤保险"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("22110306"); // 应付职工薪酬-社会保险费-工伤保险-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R404");
end

rule "R405_企业住房公积金计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "住房公积金") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业住房公积金计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "住房公积金"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("221104"); // 应付职工薪酬-住房公积金-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R405");
end

rule "R406_企业职业年金计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact(reimType == "职业年金缴费") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("企业职业年金计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "职业年金"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("********"); // 应付职工薪酬-社会保险费-职业年金-企业缴纳
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R406");
end
```

#### 3.3.5 工会经费计提规则

**🏛️ 工会经费计提规则流程图**

```mermaid
flowchart TD
    A[🏛️ 工会经费计提<br/>salaryType=4] --> B[📋 工会经费支出识别]

    B --> C[💰 R501_工会经费计提]

    C --> D[📄 生成凭证分录]

    D --> E[💰 借方：**********<br/>业务活动费用-其他经费-人员经费]
    D --> F[💳 贷方：224199<br/>其他应付款-工会经费]

    E --> G[📋 辅助项配置<br/>部门+项目]
    F --> H[✅ 凭证生成完成]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#ffebee
    style F fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#e8f5e8
```

**📊 工会经费计提规则映射表**

| 规则编号 | 项目名称 | 常量名称 | 借方科目 | 贷方科目 | 说明 |
|----------|----------|----------|----------|----------|------|
| R501 | 工会经费计提 | pubFeeEntp | ********** | 224199 | 工会经费支出 |

**📝 Drools规则实现**

```drools
// 规则组：工会经费计提规则
rule "R501_工会经费计提"
    salience 90
when
    $task: SalaryTaskFact(salaryType == "4")
    $detail: SalaryDetailFact(reimType == "工会经费支出") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("**********"); // 业务活动费用-其他经费-人员经费
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("工会经费计提-" + $detail.getOrgName());
    debitEntry.setAuxiliaryItems(buildAuxItems("部门", $detail.getOrgId(), "项目", "工会经费"));

    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("224199"); // 其他应付款-工会经费
    creditEntry.setCreditAmount($detail.getReimAmt());

    $task.addVoucherEntry(debitEntry);
    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R501");
end
```

#### 3.3.6 特殊业务处理规则

**🔧 特殊人员处理规则流程图**

```mermaid
flowchart TD
    A[👥 特殊人员识别<br/>salaryType=2] --> B{🔍 员工身份判断}

    B -->|维修班人员| C[🔧 R601_维修班特殊往来账处理]
    B -->|总务科人员| D[🏢 R602_总务科特殊往来账处理]
    B -->|收费室人员| E[💳 R603_收费室特殊往来账处理]
    B -->|血浆站人员| F[🩸 R604_单采血浆站特殊处理]
    B -->|特定员工| G[👤 R605_吴军兵特殊实发处理]
    B -->|普通员工| H[✅ 正常处理流程]

    C --> C1[📄 生成特殊凭证分录]
    D --> D1[📄 生成特殊凭证分录]
    E --> E1[📄 生成特殊凭证分录]
    F --> F1[📄 生成特殊凭证分录]
    G --> G1[📄 生成特殊凭证分录]

    C1 --> I[💰 借方：121803<br/>其他应收款-其他]
    D1 --> I
    E1 --> I
    F1 --> J[💰 借方：121803<br/>其他应收款-其他]
    G1 --> K[💳 贷方：121803<br/>其他应收款-其他]

    I --> L[🏷️ 辅助项：往来单位4414<br/>员工编号]
    J --> M[🏷️ 辅助项：往来单位7004<br/>员工编号]
    K --> N[🏷️ 辅助项：往来单位3077<br/>员工编号]

    L --> O[✅ 特殊处理完成]
    M --> O
    N --> O
    H --> P[✅ 正常流程继续]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style H fill:#fff8e1
    style I fill:#ffebee
    style J fill:#ffebee
    style K fill:#e8f5e8
    style O fill:#e8f5e8
    style P fill:#e8f5e8
```

**👥 特殊人员规则映射表**

| 规则编号 | 特殊人员类型 | 人员名单 | 科目代码 | 往来单位 | 处理方式 |
|----------|-------------|----------|----------|----------|----------|
| R601 | 维修班人员 | 邹毅、田晓辉、刘黎等11人 | 121803 | 4414 | 借方特殊往来账 |
| R602 | 总务科人员 | 吴思琪 | 121803 | 4414 | 借方特殊往来账 |
| R603 | 收费室人员 | 罗亚婕 | 121803 | 4414 | 借方特殊往来账 |
| R604 | 血浆站人员 | 血浆站全体人员 | 121803 | 7004 | 借方特殊往来账 |
| R605 | 特定员工 | 吴军兵 | 121803 | 3077 | 贷方特殊实发 |

**📝 Drools规则实现**

将特殊人员和特殊情况处理细分为具体规则：

```drools
// 规则组：特殊人员处理规则
rule "R601_维修班特殊往来账处理"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(
        empCode in ("邹毅", "田晓辉", "刘黎", "刘亨强", "陈洪浩",
                   "吕勇", "苟鹏", "古代勇", "林贤培", "张丽", "邓钟")
    ) from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("121803"); // 其他应收款-其他
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("维修班特殊往来账-" + $detail.getEmpName());
    debitEntry.setAuxiliaryItems(buildAuxItems("往来单位", "4414", "员工", $detail.getEmpCode()));

    $task.addVoucherEntry(debitEntry);
    $task.setRuleApplied("R601");
    $task.setSpecialProcessing(true);
end

rule "R602_总务科特殊往来账处理"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(empCode == "吴思琪") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("121803");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("总务科特殊往来账-" + $detail.getEmpName());
    debitEntry.setAuxiliaryItems(buildAuxItems("往来单位", "4414", "员工", $detail.getEmpCode()));

    $task.addVoucherEntry(debitEntry);
    $task.setRuleApplied("R602");
    $task.setSpecialProcessing(true);
end

rule "R603_收费室特殊往来账处理"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(empCode == "罗亚婕") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("121803");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("收费室特殊往来账-" + $detail.getEmpName());
    debitEntry.setAuxiliaryItems(buildAuxItems("往来单位", "4414", "员工", $detail.getEmpCode()));

    $task.addVoucherEntry(debitEntry);
    $task.setRuleApplied("R603");
    $task.setSpecialProcessing(true);
end

rule "R604_单采血浆站特殊处理"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(orgId == "单采血浆站") from $task.details
then
    VoucherEntry debitEntry = new VoucherEntry();
    debitEntry.setSubjectCode("121803");
    debitEntry.setDebitAmount($detail.getReimAmt());
    debitEntry.setDescription("血浆站特殊处理-" + $detail.getEmpName());
    debitEntry.setAuxiliaryItems(buildAuxItems("往来单位", "7004", "员工", $detail.getEmpCode()));

    $task.addVoucherEntry(debitEntry);
    $task.setRuleApplied("R604");
    $task.setSpecialProcessing(true);
end

rule "R605_吴军兵特殊实发处理"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(empCode == "吴军兵") from $task.details
then
    VoucherEntry creditEntry = new VoucherEntry();
    creditEntry.setSubjectCode("121803"); // 其他应收款
    creditEntry.setCreditAmount($detail.getReimAmt());
    creditEntry.setDescription("吴军兵特殊实发处理");
    creditEntry.setAuxiliaryItems(buildAuxItems("往来单位", "3077", "员工", $detail.getEmpCode()));

    $task.addVoucherEntry(creditEntry);
    $task.setRuleApplied("R605");
    $task.setSpecialProcessing(true);
end
```

#### 3.3.7 条件判断和验证规则

**✅ 数据验证规则流程图**

```mermaid
flowchart TD
    A[📋 数据验证开始] --> B[🔍 R701_工资任务基础数据验证]
    B --> C[💰 R702_工资金额数据验证]
    C --> D[📄 R703_工资明细数据验证]
    D --> E[👤 R704_个人扣款配置检查]
    E --> F[🔧 R705_特殊人员身份验证]
    F --> G[🏢 R706_科室类型验证]
    G --> H[👥 R707_人员类型验证]

    B -->|验证失败| I[❌ 基础数据错误]
    C -->|验证失败| J[❌ 金额数据错误]
    D -->|验证失败| K[❌ 明细数据错误]
    E -->|验证失败| L[❌ 配置缺失错误]
    F -->|验证失败| M[❌ 特殊人员配置错误]
    G -->|验证失败| N[❌ 科室类型错误]
    H -->|验证失败| O[❌ 人员类型错误]

    H -->|全部验证通过| P[✅ 数据验证通过]

    I --> Q[🛑 终止处理]
    J --> Q
    K --> Q
    L --> Q
    M --> Q
    N --> Q
    O --> Q

    P --> R[🔄 继续业务规则处理]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style H fill:#fff8e1
    style P fill:#e8f5e8
    style Q fill:#ffebee
    style R fill:#e8f5e8
```

**📊 数据验证规则详细表**

| 规则编号 | 验证项目 | 验证条件 | 错误信息 | 优先级 |
|----------|----------|----------|----------|--------|
| R701 | 基础数据验证 | salaryId、ffMth、num不能为空 | 工资任务基础数据不完整 | 100 |
| R702 | 金额数据验证 | shouldPay、reducePay、realPay不能为负 | 工资金额数据异常 | 100 |
| R703 | 明细数据验证 | orgId、reimType、reimAmt不能为空 | 工资明细数据不完整 | 100 |
| R704 | 个人扣款配置检查 | 个人扣款项目必须有配置 | 个人扣款未配置 | 95 |
| R705 | 特殊人员身份验证 | 特殊人员必须有配置记录 | 特殊人员配置缺失 | 95 |
| R706 | 科室类型验证 | deptType必须为1或2 | 科室类型错误 | 95 |
| R707 | 人员类型验证 | empType必须在允许范围内 | 人员类型错误 | 95 |

**📝 Drools规则实现**

将条件判断和验证逻辑细分为具体的检查规则：

```drools
// 规则组：数据验证规则
rule "R701_工资任务基础数据验证"
    salience 100
when
    $task: SalaryTaskFact(
        salaryId == null ||
        ffMth == null || ffMth == "" ||
        num == null || num <= 0
    )
then
    $task.addValidationError("工资任务基础数据不完整：工资ID、发放月份、工资条数不能为空");
    $task.setRuleApplied("R701");
end

rule "R702_工资金额数据验证"
    salience 100
when
    $task: SalaryTaskFact(
        shouldPay == null || shouldPay.compareTo(BigDecimal.ZERO) < 0 ||
        reducePay == null || reducePay.compareTo(BigDecimal.ZERO) < 0 ||
        realPay == null || realPay.compareTo(BigDecimal.ZERO) < 0
    )
then
    $task.addValidationError("工资金额数据异常：应发、扣款、实发金额不能为负数");
    $task.setRuleApplied("R702");
end

rule "R703_工资明细数据验证"
    salience 100
when
    $task: SalaryTaskFact()
    $detail: SalaryDetailFact(
        orgId == null || orgId == "" ||
        reimType == null || reimType == "" ||
        reimAmt == null || reimAmt.compareTo(BigDecimal.ZERO) <= 0
    ) from $task.details
then
    $task.addValidationError("工资明细数据不完整：科室ID、报销类型、报销金额不能为空或负数");
    $task.setRuleApplied("R703");
end

rule "R704_个人扣款配置检查"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(
        empCode != null && empCode != "",
        reimType in ("养老保险", "医疗保险", "失业保险", "住房公积金", "职业年金",
                    "个人所得税", "房租费", "水费", "人力临时扣款", "财务临时扣款")
    ) from $task.details
    not PersonalDeductionConfig(empCode == $detail.empCode, deductionType == $detail.reimType)
then
    $task.addValidationError("个人扣款未配置: 员工[" + $detail.getEmpCode() + "]的[" + $detail.getReimType() + "]");
    $task.setRuleApplied("R704");
end

rule "R705_特殊人员身份验证"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(
        empCode in ("邹毅", "田晓辉", "刘黎", "刘亨强", "陈洪浩", "吕勇", "苟鹏",
                   "古代勇", "林贤培", "张丽", "邓钟", "吴思琪", "罗亚婕", "吴军兵")
    ) from $task.details
    not SpecialEmployeeConfig(empCode == $detail.empCode, activeFlag == "1")
then
    $task.addValidationError("特殊人员配置缺失: 员工[" + $detail.getEmpCode() + "]需要特殊处理配置");
    $task.setRuleApplied("R705");
end

rule "R706_科室类型验证"
    salience 95
when
    $task: SalaryTaskFact()
    $detail: SalaryDetailFact(
        deptType not in ("1", "2")
    ) from $task.details
then
    $task.addValidationError("科室类型错误: 科室[" + $detail.getOrgName() + "]类型必须为1(业务科室)或2(管理科室)");
    $task.setRuleApplied("R706");
end

rule "R707_人员类型验证"
    salience 95
when
    $task: SalaryTaskFact()
    $detail: SalaryDetailFact(
        empType not in ("在编", "血防占编", "编外-医技", "编外-护理", "编外-辅助岗位",
                       "编外-医技-见习", "编外-护理-见习", "编外-辅助岗位-见习",
                       "编外-其他专技", "编外-后勤", "编外-其他专技-见习", "编外-后勤-见习",
                       "借调", "返聘")
    ) from $task.details
then
    $task.addValidationError("人员类型错误: 员工[" + $detail.getEmpCode() + "]类型[" + $detail.getEmpType() + "]不在允许范围内");
    $task.setRuleApplied("R707");
end
```

#### 3.3.8 预算科目配置规则

**💰 预算科目配置规则流程图**

```mermaid
flowchart TD
    A[💰 预算科目配置] --> B{🏷️ 工资类型判断}

    B -->|salaryType=1| C[📊 R801_工资计提预算科目配置]
    B -->|salaryType=2| D[💳 R802_工资发放预算科目配置]
    B -->|salaryType=3| E[🛡️ R803_企业四险两金预算科目配置]
    B -->|salaryType=4| F[🏛️ R804_工会经费预算科目配置]

    C --> G[📄 生成预算分录]
    D --> G
    E --> G
    F --> G

    G --> H[💰 预算科目：7201010301<br/>事业支出-医院支出其他资金支出-基本支出]

    H --> I[📋 预算分录配置]
    I --> J[✅ 预算配置完成]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#e1f5fe
    style F fill:#fce4ec
    style G fill:#f1f8e9
    style H fill:#ffebee
    style I fill:#fff3e0
    style J fill:#e8f5e8
```

**📊 预算科目配置规则映射表**

| 规则编号 | 工资类型 | 预算科目 | 预算科目名称 | 说明 |
|----------|----------|----------|-------------|------|
| R801 | 工资计提 | 7201010301 | 事业支出-基本支出-人员经费 | 工资计提预算 |
| R802 | 工资发放 | 7201010301 | 事业支出-基本支出-人员经费 | 工资发放预算 |
| R803 | 企业四险两金 | 7201010301 | 事业支出-基本支出-社会保障缴费 | 企业保险预算 |
| R804 | 工会经费 | 7201010301 | 事业支出-基本支出-工会经费 | 工会经费预算 |

#### 3.3.9 业务流程控制规则

**🔄 业务流程控制规则流程图**

```mermaid
flowchart TD
    A[🔄 业务流程控制] --> B[🔍 R901_工资发放流程前置检查]

    B --> C{❌ 验证错误存在?}
    C -->|是| D[❌ 流程终止<br/>VALIDATION_FAILED]
    C -->|否| E[✅ R902_个人扣款配置完整性检查]

    E --> F{⚙️ 配置完整?}
    F -->|否| G[⚙️ 需要配置<br/>CONFIG_REQUIRED]
    F -->|是| H[✅ R903_凭证生成条件检查]

    H --> I{📄 条件满足?}
    I -->|是| J[✅ 可以生成凭证<br/>readyForVoucher = true]
    I -->|否| K[❌ 条件不满足]

    D --> L[🛑 处理终止]
    G --> M[⚙️ 等待配置]
    J --> N[📄 继续凭证生成]
    K --> L

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#fff8e1
    style D fill:#ffebee
    style E fill:#e8f5e8
    style F fill:#fff8e1
    style G fill:#fff3e0
    style H fill:#e8f5e8
    style I fill:#fff8e1
    style J fill:#e8f5e8
    style K fill:#ffebee
    style L fill:#ffebee
    style M fill:#fff3e0
    style N fill:#e8f5e8
```

**📊 业务流程控制规则映射表**

| 规则编号 | 规则名称 | 检查内容 | 成功结果 | 失败结果 |
|----------|----------|----------|----------|----------|
| R901 | 工资发放流程前置检查 | 验证错误列表 | 继续流程 | VALIDATION_FAILED |
| R902 | 个人扣款配置完整性检查 | 员工配置完整性 | 继续流程 | CONFIG_REQUIRED |
| R903 | 凭证生成条件检查 | 所有条件满足 | readyForVoucher=true | 条件不满足 |

**📝 Drools规则实现**

```drools
// 规则组：预算科目配置规则
rule "R801_工资计提预算科目配置"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact() from $task.details
then
    BudgetEntry budgetEntry = new BudgetEntry();
    budgetEntry.setBudgetSubject("7201010301"); // 事业支出-医院支出其他资金支出-基本支出
    budgetEntry.setBudgetSubjectName("事业支出-基本支出-人员经费");
    budgetEntry.setAmount($detail.getReimAmt());
    budgetEntry.setDescription("工资计提预算-" + $detail.getReimType() + "-" + $detail.getOrgName());

    $task.addBudgetEntry(budgetEntry);
    $task.setRuleApplied("R801");
end

rule "R802_工资发放预算科目配置"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact() from $task.details
then
    BudgetEntry budgetEntry = new BudgetEntry();
    budgetEntry.setBudgetSubject("7201010301");
    budgetEntry.setBudgetSubjectName("事业支出-基本支出-人员经费");
    budgetEntry.setAmount($detail.getReimAmt());
    budgetEntry.setDescription("工资发放预算-" + $detail.getReimType() + "-" + $detail.getOrgName());

    $task.addBudgetEntry(budgetEntry);
    $task.setRuleApplied("R802");
end

rule "R803_企业四险两金预算科目配置"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "3")
    $detail: SalaryDetailFact() from $task.details
then
    BudgetEntry budgetEntry = new BudgetEntry();
    budgetEntry.setBudgetSubject("7201010301");
    budgetEntry.setBudgetSubjectName("事业支出-基本支出-社会保障缴费");
    budgetEntry.setAmount($detail.getReimAmt());
    budgetEntry.setDescription("企业保险预算-" + $detail.getReimType() + "-" + $detail.getOrgName());

    $task.addBudgetEntry(budgetEntry);
    $task.setRuleApplied("R803");
end

rule "R804_工会经费预算科目配置"
    salience 80
when
    $task: SalaryTaskFact(salaryType == "4")
    $detail: SalaryDetailFact() from $task.details
then
    BudgetEntry budgetEntry = new BudgetEntry();
    budgetEntry.setBudgetSubject("7201010301");
    budgetEntry.setBudgetSubjectName("事业支出-基本支出-工会经费");
    budgetEntry.setAmount($detail.getReimAmt());
    budgetEntry.setDescription("工会经费预算-" + $detail.getOrgName());

    $task.addBudgetEntry(budgetEntry);
    $task.setRuleApplied("R804");
end

// 规则组：业务流程控制规则
rule "R901_工资发放流程前置检查"
    salience 85
when
    $task: SalaryTaskFact(salaryType == "2")
    $validationErrors: List(size > 0) from $task.validationErrors
then
    $task.setProcessType(ProcessType.VALIDATION_FAILED);
    $task.addValidationError("工资发放流程前置检查失败，请先解决数据验证问题");
    $task.setRuleApplied("R901");
end

rule "R902_个人扣款配置完整性检查"
    salience 85
when
    $task: SalaryTaskFact(salaryType == "2", processType != ProcessType.VALIDATION_FAILED)
    $empCodes: List() from collect(SalaryDetailFact(empCode != null && empCode != "") from $task.details)
    $configuredEmpCodes: List() from collect(PersonalDeductionConfig(activeFlag == "1").empCode)
    eval($empCodes.size() > $configuredEmpCodes.size())
then
    $task.addValidationError("存在未配置个人扣款的员工，请先完成个人扣款配置");
    $task.setProcessType(ProcessType.CONFIG_REQUIRED);
    $task.setRuleApplied("R902");
end

rule "R903_凭证生成条件检查"
    salience 75
when
    $task: SalaryTaskFact(
        processType != ProcessType.VALIDATION_FAILED &&
        processType != ProcessType.CONFIG_REQUIRED
    )
    $validationErrors: List(size == 0) from $task.validationErrors
    $voucherEntries: List(size > 0) from $task.voucherEntries
then
    $task.setReadyForVoucher(true);
    $task.setRuleApplied("R903");
    System.out.println("凭证生成条件检查通过，可以生成凭证");
end
```

### 3.4 规则优先级和执行顺序

#### 3.4.1 规则优先级设计
基于业务逻辑的重要性和依赖关系，规则优先级设计如下：

| 优先级 | 规则类型 | 规则编号范围 | 说明 |
|--------|----------|-------------|------|
| 100 | 数据验证规则 | R701-R707 | 最高优先级，确保数据完整性 |
| 95 | 特殊业务处理规则 | R601-R605, R704-R707 | 特殊人员和特殊情况处理 |
| 90 | 科目映射规则 | R101-R109, R201-R205, R401-R406, R501 | 核心业务规则，科目映射 |
| 85 | 流程控制规则 | R901-R902 | 业务流程前置检查 |
| 80 | 预算科目配置规则 | R801-R804 | 预算科目配置 |
| 75 | 条件检查规则 | R903 | 最终条件检查 |
| 70 | 汇总计算规则 | R330 | 金额汇总和计算 |
| 50 | 异常处理规则 | R005 | 异常情况处理 |

#### 3.4.2 规则执行流程
```mermaid
graph TD
    A[开始执行规则] --> B[数据验证规则 100]
    B --> C{验证通过?}
    C -->|否| D[返回验证错误]
    C -->|是| E[特殊业务处理规则 95]
    E --> F[科目映射规则 90]
    F --> G[流程控制规则 85]
    G --> H{需要配置?}
    H -->|是| I[返回配置要求]
    H -->|否| J[预算科目配置规则 80]
    J --> K[条件检查规则 75]
    K --> L[汇总计算规则 70]
    L --> M[返回执行结果]
```

#### 3.4.3 规则依赖关系
- **数据验证规则**：无依赖，最先执行
- **特殊业务处理规则**：依赖数据验证通过
- **科目映射规则**：依赖特殊业务处理完成
- **流程控制规则**：依赖科目映射完成
- **预算科目配置规则**：依赖流程控制检查通过
- **条件检查规则**：依赖所有业务规则执行完成
- **汇总计算规则**：依赖条件检查通过

### 3.5 代码迁移对照表

#### 3.5.1 硬编码业务逻辑迁移对照

| 原代码位置 | 硬编码逻辑 | 对应Drools规则 | 迁移说明 |
|------------|------------|----------------|----------|
| `salaryVcr.vue:330-337` | 工资类型判断switch语句 | R001-R005 | 工资类型处理规则 |
| `salaryVcr.vue:427-435` | 个人扣款配置检查 | R704, R902 | 个人扣款验证规则 |
| `EcsConst.java:271-284` | 人员类型常量数组 | R707 | 人员类型验证规则 |
| `EcsConst.java:184-267` | 工资项目常量定义 | R101-R501 | 工资项目科目映射规则 |
| `salaryConfig/index.vue:205-214` | 工资类别选项 | R001-R004 | 工资类型配置规则 |
| `salaryConfig/index.vue:216-237` | 员工类别选项 | R707 | 人员类型验证规则 |
| `salaryConfig/index.vue:238-247` | 科室类别选项 | R706 | 科室类型验证规则 |

#### 3.5.2 业务逻辑重构对照

**原始硬编码逻辑**：
```javascript
// 原代码：src/views/modules/ecs/reimNew/salaryReim/index.vue
switch (row.salaryType) {
    case '1': return '工资计提'
    case '2': return '工资发放+三险两金'
    case '3': return '企业四险两金'
    case '4': return '工会经费'
}
```

**重构后Drools规则**：
```drools
rule "R001_工资计提类型判断"
    salience 100
when
    $task: SalaryTaskFact(salaryType == "1")
then
    $task.setProcessType(ProcessType.DIRECT_VOUCHER);
    $task.setVoucherTemplate("SALARY_ACCRUAL");
end
```

**原始硬编码逻辑**：
```javascript
// 原代码：src/views/modules/erp/vcrGen/vcrPane/components/salaryVcr.vue
if (row.salaryType == '2') {
    const indiToConfig = await queryToConfigSalary({id: checkedRowKeys.value[0]})
    if (indiToConfig.code == 200 && indiToConfig.data.length > 0) {
        indiToConfigModal.value = true
        return
    }
}
```

**重构后Drools规则**：
```drools
rule "R704_个人扣款配置检查"
    salience 95
when
    $task: SalaryTaskFact(salaryType == "2")
    $detail: SalaryDetailFact(empCode != null) from $task.details
    not PersonalDeductionConfig(empCode == $detail.empCode)
then
    $task.addValidationError("个人扣款未配置: " + $detail.getEmpCode());
end
```

#### 3.5.3 常量定义迁移对照

**原始常量定义**：
```java
// 原代码：med-ecs/src/main/java/com/jp/med/ecs/modules/reimMgt/constant/EcsConst.java
String[] ESTAB_STR_ARR = new String[]{"在编","血防占编"};
String[] HIRE_STR_ARR = new String[]{"编外-医技","编外-护理","编外-辅助岗位",...};
```

**重构后规则配置**：
```sql
-- 规则配置表数据
INSERT INTO salary_rule_config (rule_name, rule_type, rule_content) VALUES
('人员类型验证', 'VALIDATION', 'empType in ("在编", "血防占编", "编外-医技", ...)');
```

#### 3.5.4 接口调用迁移对照

**原始接口调用**：
```javascript
// 原代码：src/api/erp/vcr/vcrGen/vcrGen.ts
export function queryToConfigSalary(param: Object) {
    return request({
        url: 'erp/erpVcrDetail/queryToCfgTempReduce',
        method: RequestType.POST,
        data: param,
    })
}
```

**重构后规则引擎调用**：
```java
// 新代码：SalaryVoucherRuleService.java
public SalaryVoucherDecision executeRules(SalaryTaskRequest request) {
    SalaryTaskFact fact = buildSalaryTaskFact(request);
    KieSession kieSession = kieContainer.newKieSession();
    kieSession.insert(fact);
    kieSession.fireAllRules();
    return buildDecision(fact);
}
```

## 4. 功能需求 📝

### 4.1 规则引擎核心功能

#### 4.1.1 规则执行引擎
- **功能描述**：基于Drools的规则执行引擎
- **输入**：工资任务数据、明细数据
- **输出**：凭证生成决策、验证结果
- **性能要求**：单次规则执行时间 < 500ms

#### 4.1.2 规则热加载
- **功能描述**：支持规则动态加载，无需重启服务
- **触发方式**：规则配置变更后自动触发
- **回滚机制**：规则加载失败时自动回滚到上一版本

#### 4.1.3 规则版本管理
- **功能描述**：维护规则版本历史，支持版本回退
- **版本策略**：语义化版本号(major.minor.patch)
- **存储方式**：数据库+文件系统双重存储

### 4.2 规则配置管理

#### 4.2.1 工资类型规则配置
- **配置项**：工资类型、处理方式、模板选择
- **界面要求**：表格形式，支持增删改查
- **验证规则**：工资类型唯一性、处理方式有效性

#### 4.2.2 科目映射规则配置  
- **配置项**：报销类型、会计科目、借贷方向、金额计算公式
- **界面要求**：树形结构展示科目层级
- **公式支持**：支持简单的数学表达式

#### 4.2.3 辅助项规则配置
- **配置项**：辅助项类型、取值规则、默认值
- **支持类型**：部门、项目、资金性质、现金流量
- **取值规则**：固定值、动态取值、计算取值

### 4.3 规则管理界面

#### 4.3.1 规则列表页面
- **功能**：展示所有规则，支持搜索、筛选、排序
- **操作**：新增、编辑、删除、启用/禁用、测试
- **状态显示**：规则状态、最后修改时间、修改人

#### 4.3.2 规则编辑器
- **功能**：可视化规则编辑器，支持拖拽操作
- **语法检查**：实时语法验证和错误提示
- **预览功能**：规则预览和测试执行

#### 4.3.3 规则测试工具
- **功能**：规则单元测试和集成测试
- **测试数据**：支持导入测试数据或手动输入
- **结果对比**：期望结果与实际结果对比

## 5. 技术实现方案 🛠️

### 5.1 技术选型
基于当前系统架构，技术选型如下：
- **规则引擎**：Drools 7.74.1.Final
- **后端框架**：Spring Boot 2.7.x
- **数据库**：PostgreSQL 12+
- **前端框架**：Vue 3 + Naive UI
- **构建工具**：Maven 3.8+
- **JDK版本**：JDK 8+
- **缓存**：Redis（规则缓存）
- **工作流**：BPM流程引擎（报销审批）

### 5.2 数据库设计
```sql
-- 规则配置表
CREATE TABLE salary_rule_config (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    rule_content TEXT NOT NULL,
    version VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    priority INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator VARCHAR(50),
    updater VARCHAR(50)
);

-- 规则版本历史表
CREATE TABLE salary_rule_version (
    id SERIAL PRIMARY KEY,
    rule_id INTEGER REFERENCES salary_rule_config(id),
    version VARCHAR(20) NOT NULL,
    rule_content TEXT NOT NULL,
    change_log TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator VARCHAR(50)
);

-- 工资类型规则配置表
CREATE TABLE salary_type_rule (
    id SERIAL PRIMARY KEY,
    salary_type VARCHAR(10) NOT NULL,
    process_type VARCHAR(50) NOT NULL,
    template_code VARCHAR(100),
    description TEXT,
    active_flag VARCHAR(1) DEFAULT '1',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 科目映射规则表
CREATE TABLE salary_subject_mapping (
    id SERIAL PRIMARY KEY,
    salary_type VARCHAR(10) NOT NULL,
    dept_type VARCHAR(20) NOT NULL,
    emp_type VARCHAR(20) NOT NULL,
    reim_type VARCHAR(50) NOT NULL,
    debit_subject VARCHAR(20),
    credit_subject VARCHAR(20),
    budget_subject VARCHAR(20),
    auxiliary_config TEXT,
    priority INTEGER DEFAULT 0,
    active_flag VARCHAR(1) DEFAULT '1',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 特殊人员配置表
CREATE TABLE salary_special_employee (
    id SERIAL PRIMARY KEY,
    emp_code VARCHAR(50) NOT NULL,
    emp_name VARCHAR(100),
    special_type VARCHAR(50) NOT NULL,
    subject_code VARCHAR(20),
    auxiliary_code VARCHAR(50),
    description TEXT,
    active_flag VARCHAR(1) DEFAULT '1'
);

-- 辅助项配置表
CREATE TABLE salary_auxiliary_config (
    id SERIAL PRIMARY KEY,
    aux_type VARCHAR(50) NOT NULL,
    aux_code VARCHAR(50) NOT NULL,
    aux_name VARCHAR(100) NOT NULL,
    mapping_rule TEXT,
    active_flag VARCHAR(1) DEFAULT '1'
);
```

### 5.3 科目映射规则详细配置

#### 5.3.1 工资计提科目映射表
基于系统实际配置，工资计提科目映射规则如下：

| 部门类型 | 人员类型 | 工资项目 | 借方科目 | 贷方科目 | 辅助项配置 | 说明 |
|----------|----------|----------|----------|----------|------------|------|
| 业务科室(1) | 在编/招聘 | 岗位工资 | ********** | 221101 | 部门+项目 | 业务活动费用-其他经费-人员经费 |
| 业务科室(1) | 在编/招聘 | 薪级工资 | ********** | 221101 | 部门+项目 | 基本工资科目 |
| 业务科室(1) | 在编/招聘 | 护士10% | ********** | 221101 | 部门+项目 | 护士津贴 |
| 业务科室(1) | 在编/招聘 | 地区附加津贴 | ********** | 221102 | 部门+项目 | 国家统一规定津贴补贴 |
| 业务科室(1) | 在编/招聘 | 护龄补贴 | ********** | 221102 | 部门+项目 | 国家统一规定津贴补贴 |
| 业务科室(1) | 在编/招聘 | 基础绩效工资 | ********** | 221103 | 部门+项目 | 规范津贴补贴 |
| 业务科室(1) | 在编/招聘 | 人力临时增加 | ********** | 221101 | 部门+项目 | 临时工资调整 |
| 业务科室(1) | 在编/招聘 | 财务临时增加 | ********** | 221101 | 部门+项目 | 财务调整 |
| 业务科室(1) | 临聘 | 岗位工资 | ********** | 221101 | 部门+项目 | 临聘人员基本工资 |
| 管理科室(2) | 在编 | 岗位工资 | 5201030199 | 221101 | 部门+项目 | 单位管理费用-其他经费-人员经费 |
| 管理科室(2) | 在编 | 薪级工资 | 5201030199 | 221101 | 部门+项目 | 管理科室基本工资 |
| 管理科室(2) | 招聘 | 岗位工资 | 5201030199 | 221101 | 部门+项目 | 招聘人员工资 |
| 管理科室(2) | 临聘 | 岗位工资 | 5201030199 | 221101 | 部门+项目 | 临聘人员工资 |

#### 5.3.2 工资发放科目映射表
| 业务类型 | 借方科目 | 贷方科目 | 说明 |
|----------|----------|----------|------|
| 基本工资冲销 | 221101 | **********(负数) | 冲销计提的基本工资 |
| 津贴补贴冲销 | 221102 | **********(负数) | 冲销计提的津贴补贴 |
| 绩效工资冲销 | 221103 | **********(负数) | 冲销计提的绩效工资 |
| 个人养老保险 | - | ******** | 个人缴纳养老保险 |
| 个人医疗保险 | - | ******** | 个人缴纳医疗保险 |
| 个人失业保险 | - | ******** | 个人缴纳失业保险 |
| 个人住房公积金 | - | 221104 | 个人缴纳住房公积金 |
| 个人职业年金 | - | ******** | 个人缴纳职业年金 |
| 工会会费 | - | 其他应付款 | 工会会费代扣 |
| 个人所得税 | - | 210207 | 个人所得税代扣 |
| 实发工资 | - | 100201 | 银行存款实发 |

#### 5.3.3 特殊人员处理配置
| 特殊类型 | 人员列表 | 科目代码 | 辅助项 | 说明 |
|----------|----------|----------|--------|------|
| 维修班 | 邹毅,田晓辉,刘黎等11人 | 121803 | 往来单位4414 | 特殊往来账处理 |
| 总务科特殊 | 吴思琪 | 121803 | 往来单位4414 | 特殊往来账处理 |
| 收费室特殊 | 罗亚婕 | 121803 | 往来单位4414 | 特殊往来账处理 |
| 单采血浆站 | 血浆站全体人员 | 121803 | 往来单位7004 | 血浆站特殊处理 |
| 吴军兵特殊 | 吴军兵 | - | 往来单位3077 | 特殊实发处理 |

### 5.4 核心代码结构
基于现有ECS模块结构，规则引擎代码组织如下：
```
med-ecs/src/main/java/com/jp/med/ecs/modules/
├── salary/                              # 新增工资规则模块
│   ├── rule/
│   │   ├── engine/
│   │   │   ├── SalaryRuleEngine.java           # 规则引擎核心
│   │   │   ├── RuleExecutor.java               # 规则执行器
│   │   │   └── RuleLoader.java                 # 规则加载器
│   │   ├── fact/
│   │   │   ├── SalaryTaskFact.java             # 工资任务事实
│   │   │   ├── SalaryDetailFact.java           # 工资明细事实
│   │   │   └── SalaryContextFact.java          # 工资上下文事实
│   │   ├── decision/
│   │   │   ├── SalaryVoucherDecision.java      # 凭证决策结果
│   │   │   ├── ProcessType.java                # 处理类型枚举
│   │   │   └── VoucherEntry.java               # 凭证分录
│   │   └── config/
│   │       ├── RuleConfig.java                 # 规则配置
│   │       └── DroolsConfig.java               # Drools配置
│   ├── service/
│   │   ├── SalaryVoucherRuleService.java       # 工资凭证规则服务
│   │   ├── RuleConfigService.java              # 规则配置服务
│   │   └── RuleManagementService.java          # 规则管理服务
│   └── controller/
│       ├── SalaryRuleController.java           # 规则执行控制器
│       └── RuleManagementController.java       # 规则管理控制器
└── reimMgt/                             # 现有报销管理模块
    ├── service/
    │   └── write/impl/
    │       └── EcsReimSalaryTaskWriteServiceImpl.java  # 集成规则引擎
    └── controller/
        └── EcsReimSalaryTaskController.java            # 增加规则调用

# 前端代码结构
src/views/modules/ecs/
├── salary/                              # 新增工资规则管理页面
│   ├── rule-management/
│   │   ├── index.vue                           # 规则管理主页面
│   │   ├── components/
│   │   │   ├── RuleEditor.vue                  # 规则编辑器
│   │   │   ├── RuleTestTool.vue                # 规则测试工具
│   │   │   └── RuleVersionHistory.vue          # 规则版本历史
│   │   └── hooks/
│   │       └── useRuleManagement.ts            # 规则管理逻辑
│   └── config/
│       ├── salary-type-config.vue              # 工资类型配置
│       └── subject-mapping-config.vue          # 科目映射配置
└── reimMgt/                             # 现有报销管理模块
    └── salaryReim/
        └── index.vue                           # 集成规则引擎调用
```

### 5.4 Drools配置
```java
@Configuration
@EnableConfigurationProperties
public class DroolsConfig {

    @Bean
    public KieContainer kieContainer() {
        KieServices kieServices = KieServices.Factory.get();
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem();

        // 加载规则文件
        loadRulesFromDatabase(kieFileSystem);

        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();

        KieModule kieModule = kieBuilder.getKieModule();
        return kieServices.newKieContainer(kieModule.getReleaseId());
    }

    private void loadRulesFromDatabase(KieFileSystem kieFileSystem) {
        // 从数据库加载规则配置
        List<SalaryRuleConfig> rules = ruleConfigService.getActiveRules();
        for (SalaryRuleConfig rule : rules) {
            String rulePath = "src/main/resources/rules/" + rule.getRuleName() + ".drl";
            kieFileSystem.write(rulePath, rule.getRuleContent());
        }
    }
}
```

## 6. 业务流程设计 🔄

### 6.1 工资凭证生成流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant C as Controller
    participant S as SalaryVoucherService
    participant R as RuleEngine
    participant E as ERPService
    participant B as BPMService

    U->>C: 提交工资凭证生成请求
    C->>S: 调用工资凭证服务
    S->>S: 构建事实对象
    S->>R: 执行规则引擎
    R->>R: 规则匹配和执行
    R->>S: 返回决策结果

    alt 直接生成凭证
        S->>E: 调用ERP生成凭证
        E->>S: 返回凭证信息
    else 报销审批流程
        S->>B: 启动BPM流程
        B->>S: 返回流程实例ID
    end

    S->>C: 返回处理结果
    C->>U: 返回响应
```

### 6.2 规则配置管理流程
```mermaid
graph TD
    A[规则配置请求] --> B[规则验证]
    B --> C{验证通过?}
    C -->|否| D[返回错误信息]
    C -->|是| E[保存规则配置]
    E --> F[生成规则文件]
    F --> G[规则热加载]
    G --> H{加载成功?}
    H -->|否| I[回滚到上一版本]
    H -->|是| J[更新规则状态]
    J --> K[返回成功结果]
    I --> L[返回回滚信息]
```

## 7. 接口设计 🔌

### 7.1 规则执行接口
```java
@RestController
@RequestMapping("/api/salary/rule")
public class SalaryRuleController {

    /**
     * 执行工资凭证规则
     */
    @PostMapping("/execute")
    public CommonResult<SalaryVoucherDecision> executeRules(
            @RequestBody SalaryTaskRequest request) {
        SalaryVoucherDecision decision = salaryVoucherRuleService.executeRules(request);
        return CommonResult.success(decision);
    }

    /**
     * 验证规则
     */
    @PostMapping("/validate")
    public CommonResult<ValidationResult> validateRules(
            @RequestBody SalaryTaskRequest request) {
        ValidationResult result = salaryVoucherRuleService.validateRules(request);
        return CommonResult.success(result);
    }
}
```

### 7.2 规则管理接口
```java
@RestController
@RequestMapping("/api/salary/rule-config")
public class RuleManagementController {

    /**
     * 查询规则列表
     */
    @PostMapping("/list")
    public CommonResult<PageResult<SalaryRuleConfig>> getRuleList(
            @RequestBody RuleQueryRequest request) {
        PageResult<SalaryRuleConfig> result = ruleConfigService.queryRules(request);
        return CommonResult.success(result);
    }

    /**
     * 保存规则
     */
    @PostMapping("/save")
    public CommonResult<Void> saveRule(@RequestBody SalaryRuleConfig rule) {
        ruleConfigService.saveRule(rule);
        return CommonResult.success();
    }

    /**
     * 测试规则
     */
    @PostMapping("/test")
    public CommonResult<RuleTestResult> testRule(@RequestBody RuleTestRequest request) {
        RuleTestResult result = ruleConfigService.testRule(request);
        return CommonResult.success(result);
    }
}
```

## 8. 前端界面设计 🎨

### 8.1 规则管理主页面
- **页面路径**：`/ecs/salary/rule-management`
- **页面组件**：基于`j-crud`组件
- **主要功能**：
  - 规则列表展示
  - 规则搜索和筛选
  - 规则新增、编辑、删除
  - 规则启用/禁用
  - 规则测试

### 8.2 规则编辑器
- **组件名称**：`SalaryRuleEditor.vue`
- **功能特性**：
  - 代码高亮显示
  - 语法错误提示
  - 自动补全功能
  - 规则模板选择
  - 实时预览

### 8.3 规则测试工具
- **组件名称**：`RuleTestTool.vue`
- **测试功能**：
  - 测试数据输入
  - 规则执行结果展示
  - 执行日志查看
  - 性能指标统计

## 9. 部署方案 🚀

### 9.1 环境要求
- **JDK版本**：JDK 8+
- **Spring Boot版本**：2.7.x
- **Drools版本**：7.74.1.Final
- **数据库**：PostgreSQL 12+

### 9.2 配置文件
```yaml
# application.yml
drools:
  rules:
    path: classpath:rules/
    auto-reload: true
    reload-interval: 30000
  cache:
    enabled: true
    max-size: 1000
    expire-after-write: 3600
```

### 9.3 部署步骤
1. **数据库初始化**：执行DDL脚本创建规则相关表
2. **规则文件部署**：将初始规则文件部署到指定目录
3. **应用启动**：启动Spring Boot应用
4. **规则加载验证**：验证规则引擎正常工作
5. **功能测试**：执行端到端功能测试

## 10. 测试方案 🧪

### 10.1 单元测试
- **规则引擎测试**：测试各种规则场景
- **服务层测试**：测试业务逻辑正确性
- **数据访问测试**：测试数据库操作

### 10.2 集成测试
- **端到端测试**：完整业务流程测试
- **性能测试**：规则执行性能测试
- **并发测试**：多用户并发场景测试

### 10.3 测试用例
```java
@Test
public void testSalaryType1Rule() {
    // 测试工资计提规则
    SalaryTaskFact fact = new SalaryTaskFact();
    fact.setSalaryType("1");

    SalaryVoucherDecision decision = ruleEngine.execute(fact);

    assertEquals(ProcessType.DIRECT_VOUCHER, decision.getProcessType());
    assertEquals("SALARY_ACCRUAL", decision.getVoucherTemplate());
}
```

## 11. 风险评估与应对 ⚠️

### 11.1 技术风险
- **规则复杂度风险**：规则过于复杂可能影响性能
  - **应对措施**：规则优化和性能监控
- **规则冲突风险**：多个规则可能产生冲突
  - **应对措施**：规则优先级管理和冲突检测

### 11.2 业务风险
- **规则理解偏差**：业务规则理解不准确
  - **应对措施**：充分的业务调研和用户确认
- **数据迁移风险**：现有数据迁移可能出现问题
  - **应对措施**：详细的迁移方案和回滚计划

## 12. 项目计划 📅

### 12.1 开发阶段
| 阶段 | 任务 | 工期 | 负责人 |
|------|------|------|--------|
| 需求分析 | 业务调研、需求确认 | 1周 | 产品经理 |
| 技术设计 | 架构设计、接口设计 | 1周 | 架构师 |
| 后端开发 | 规则引擎、服务开发 | 3周 | 后端开发 |
| 前端开发 | 界面开发、集成测试 | 2周 | 前端开发 |
| 测试验收 | 功能测试、性能测试 | 1周 | 测试工程师 |

### 12.2 里程碑
- **M1**：需求确认完成
- **M2**：技术方案评审通过
- **M3**：核心功能开发完成
- **M4**：系统测试通过
- **M5**：生产环境部署完成

## 13. 实施建议 💡

### 13.1 分阶段实施策略
**第一阶段：基础框架搭建**
- 搭建Drools规则引擎基础框架
- 实现核心事实对象和决策对象
- 完成规则配置数据库设计

**第二阶段：核心规则迁移**
- 迁移工资类型判断规则
- 实现基础科目映射规则
- 完成规则热加载功能

**第三阶段：复杂规则实现**
- 实现特殊人员处理规则
- 完成个人扣款配置检查
- 实现规则冲突检测

**第四阶段：管理界面开发**
- 开发规则管理界面
- 实现规则测试工具
- 完成规则版本管理

### 13.2 数据迁移策略
- 现有硬编码规则提取为配置数据
- 建立规则配置与代码的映射关系
- 制定规则验证和回滚机制

### 13.3 培训计划
- 开发人员Drools技术培训
- 业务人员规则配置培训
- 系统管理员运维培训

## 14. 附录 📚

### 14.1 相关技术文档
- [Drools官方文档]
- [Spring Boot集成Drools指南]
- [ECS系统架构文档]

### 14.2 业务规则清单
基于系统实际分析，完整的业务规则清单：

**工资类型规则**：
- salaryType=1：工资计提 → 直接生成凭证
- salaryType=2：工资发放+三险两金 → 报销审批流程
- salaryType=3：企业四险两金 → 直接生成凭证
- salaryType=4：工会经费 → 直接生成凭证

**人员类型规则**：
- 在编人员：`["在编","血防占编"]`
- 招聘人员：`["编外-医技","编外-护理","编外-辅助岗位","编外-医技-见习","编外-护理-见习","编外-辅助岗位-见习"]`
- 临聘人员：`["编外-其他专技","编外-后勤","编外-其他专技-见习","编外-后勤-见习"]`
- 借调人员：`["借调"]`
- 返聘人员：`["返聘"]`

**科室类型规则**：
- 业务科室(deptType=1)：临床医技医辅
- 管理科室(deptType=2)：行政

### 14.3 系统集成点
- **HRM系统**：获取员工信息和工资数据
- **ERP系统**：生成财务凭证
- **BPM系统**：工资报销审批流程
- **OSS系统**：文件存储和管理

## 15. 总结 📊

本PRD文档基于中江县人民医院智慧财务系统(ECS)的实际业务需求和技术架构，详细描述了工资凭证模块的Drools重构方案。通过深入分析现有系统代码和业务规则，制定了完整的重构计划。

**重构价值**：
1. **业务规则外化**：将硬编码的业务逻辑转换为可配置的规则
2. **提升可维护性**：业务人员可直接维护规则，减少开发依赖
3. **增强扩展性**：支持灵活的规则组合和动态调整
4. **优化性能**：通过规则引擎优化决策执行效率
5. **保证准确性**：统一的规则管理确保业务逻辑一致性

**技术优势**：
- 基于成熟的Drools规则引擎
- 与现有Spring Boot架构无缝集成
- 支持规则热加载和版本管理
- 提供完整的规则测试和验证机制

重构后的系统将为医院财务管理提供更加灵活、高效、可靠的技术支撑，显著提升工资凭证处理的自动化水平和业务响应能力。
