# Tailwind CSS 集成文档

## 📋 概述

本文档说明了如何在 Vue 3 + Vite 项目中集成 Tailwind CSS，以及相关的配置和使用方法。

## 🚀 安装步骤

### 1. 安装依赖

```bash
pnpm add -D tailwindcss postcss autoprefixer
```

### 2. 创建配置文件

#### tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
```

#### postcss.config.js
```javascript
module.exports = () => {
    return {
        plugins: {
            tailwindcss: {},
            autoprefixer: {}
        }
    };
};
```

### 3. 创建样式文件

在 `src/assets/css/tailwind.css` 中添加：

```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

### 4. 在主入口文件中导入

在 `src/main.ts` 中添加：

```typescript
// 引入 Tailwind CSS
import './assets/css/tailwind.css'
```

## 🎨 使用示例

### 基础样式
```vue
<template>
  <div class="container mx-auto px-4">
    <h1 class="text-3xl font-bold text-blue-600">标题</h1>
    <p class="text-gray-600 mt-4">这是一段文本</p>
  </div>
</template>
```

### 响应式设计
```vue
<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <div class="bg-white p-6 rounded-lg shadow-md">卡片内容</div>
  </div>
</template>
```

### 按钮样式
```vue
<template>
  <button class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-300">
    点击按钮
  </button>
</template>
```

## 🔧 高级配置

### 自定义主题

在 `tailwind.config.js` 中扩展主题：

```javascript
export default {
  // ... 其他配置
  theme: {
    extend: {
      colors: {
        'brand': {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a',
        }
      },
      fontFamily: {
        'custom': ['YourCustomFont', 'sans-serif'],
      },
      spacing: {
        '72': '18rem',
        '84': '21rem',
      }
    },
  },
}
```

### 添加插件

```bash
pnpm add -D @tailwindcss/forms @tailwindcss/typography
```

```javascript
export default {
  // ... 其他配置
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
```

## 🎯 最佳实践

### 1. 组件化样式
```vue
<template>
  <button :class="buttonClasses">
    <slot />
  </button>
</template>

<script setup lang="ts">
interface Props {
  variant?: 'primary' | 'secondary' | 'danger'
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md'
})

const buttonClasses = computed(() => {
  const base = 'font-medium rounded-lg transition duration-300 focus:outline-none focus:ring-2'
  
  const variants = {
    primary: 'bg-blue-500 hover:bg-blue-600 text-white focus:ring-blue-300',
    secondary: 'bg-gray-500 hover:bg-gray-600 text-white focus:ring-gray-300',
    danger: 'bg-red-500 hover:bg-red-600 text-white focus:ring-red-300'
  }
  
  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  }
  
  return [base, variants[props.variant], sizes[props.size]].join(' ')
})
</script>
```

### 2. 使用 @apply 指令

在组件的 `<style>` 部分：

```vue
<style scoped>
.custom-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
}
</style>
```

### 3. 条件样式
```vue
<template>
  <div :class="[
    'base-styles',
    {
      'bg-green-100 text-green-800': status === 'success',
      'bg-red-100 text-red-800': status === 'error',
      'bg-yellow-100 text-yellow-800': status === 'warning'
    }
  ]">
    状态信息
  </div>
</template>
```

## 🧪 测试验证

访问测试页面 `/test-tailwind` 来验证 Tailwind CSS 是否正常工作。

## 📝 注意事项

1. **与现有样式共存**：Tailwind CSS 可以与现有的 Less/Sass 样式共存
2. **生产环境优化**：Tailwind CSS 会自动移除未使用的样式，保持最终包体积最小
3. **IDE 支持**：建议安装 Tailwind CSS IntelliSense 插件以获得更好的开发体验
4. **样式冲突**：注意 Tailwind 的 base 样式可能会重置一些默认样式

## 🔗 相关链接

- [Tailwind CSS 官方文档](https://tailwindcss.com/docs)
- [Tailwind CSS with Vue.js](https://tailwindcss.com/docs/guides/vite#vue)
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss) 