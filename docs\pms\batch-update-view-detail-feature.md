# 指标详情查看权限批量管理功能实现说明

## 📋 功能概述

为指标关联模板批量管理页面添加批量开启/关闭指标详情查看功能，提升用户操作效率。

## 🎯 实现目标

- ✅ 前端API接口已添加
- ✅ 前端UI组件已更新
- ⏳ 后端服务接口需要添加
- ⏳ 后端服务实现需要添加
- ⏳ 后端控制器需要添加

## 🔧 技术实现

### 前端部分

#### 1. API接口更新
文件：`src/api/pms/clac/AwardCoefficientConfigWeb.ts`

```typescript
/**
 * 批量更新指标详情查看权限
 * @param param 
 */
export function batchUpdateAllowViewItemDetail(param: Object) {
  return request({
    url: 'pms/pmsAwardCoefficientConfig/batchUpdateAllowViewItemDetail',
    method: RequestType.PUT,
    data: param,
  })
}
```

#### 2. 组件功能更新
文件：`src/views/modules/pms/pmsCalc/pmsAwardCalcDeptTemplateConfig/components/ItemTemplateRelationModal.vue`

**新增功能函数：**
- `handleBatchEnableViewDetail()` - 批量开启权限
- `handleBatchDisableViewDetail()` - 批量关闭权限

**新增UI按钮：**
```vue
<NButton type="success" @click="handleBatchEnableViewDetail">批量开启详情查看</NButton>
<NButton type="warning" @click="handleBatchDisableViewDetail">批量关闭详情查看</NButton>
```

### 后端部分（待实现）

#### 1. 服务接口
文件：`med-pms/src/main/java/com/jp/med/pms/modules/pmsCalc/service/write/PmsAwardCoefficientConfigWriteService.java`

需要添加方法：
```java
/**
 * 批量更新指标详情查看权限
 * @param dto 指标与模板关联DTO，包含itemCode、templateIds和allowViewItemDetail
 */
void batchUpdateAllowViewItemDetail(PmsItemTemplateRelationDto dto);
```

#### 2. 服务实现
文件：`med-pms/src/main/java/com/jp/med/pms/modules/pmsCalcTemplate/service/write/impl/PmsAwardCoefficientConfigWriteServiceImpl.java`

需要添加实现：
```java
@Override
@Transactional(rollbackFor = Exception.class)
public void batchUpdateAllowViewItemDetail(PmsItemTemplateRelationDto dto) {
    if (dto.getTemplateIds() == null || dto.getTemplateIds().isEmpty() || dto.getItemCode() == null) {
        return;
    }

    var updateWrapper = new UpdateWrapper<PmsAwardCoefficientConfigDto>()
            .eq("item_code", dto.getItemCode())
            .in("template_id", dto.getTemplateIds())
            .set("allow_view_item_detail", dto.getAllowViewItemDetail());
    update(updateWrapper);
}
```

#### 3. 控制器接口
文件：`med-pms/src/main/java/com/jp/med/pms/modules/pmsCalcTemplate/controller/PmsAwardCoefficientConfigController.java`

需要添加接口：
```java
/**
 * 批量更新指标详情查看权限
 */
@ApiOperation("批量更新指标详情查看权限")
@PutMapping("/batchUpdateAllowViewItemDetail")
public CommonResult<?> batchUpdateAllowViewItemDetail(@RequestBody PmsItemTemplateRelationDto dto) {
    pmsAwardCoefficientConfigWriteService.batchUpdateAllowViewItemDetail(dto);
    return CommonResult.success();
}
```

#### 4. DTO更新
需要确保`PmsItemTemplateRelationDto`包含`allowViewItemDetail`字段：
```java
/**
 * 是否允许查看指标详情 ('0'/'1')
 */
private String allowViewItemDetail;
```

## 📊 数据流程

1. **用户操作** → 选择表格行 → 点击批量开启/关闭按钮
2. **前端验证** → 检查是否选中行 → 调用API
3. **后端处理** → 验证参数 → 批量更新数据库
4. **结果反馈** → 返回操作结果 → 刷新页面数据

## 🔄 API参数格式

```typescript
{
  itemCode: string,           // 指标编码
  templateIds: number[],      // 模板ID数组
  allowViewItemDetail: '0' | '1'  // '1'开启，'0'关闭
}
```

## ✨ 用户体验优化

- 🎨 使用不同颜色的按钮区分开启/关闭操作
- ⚠️ 操作前验证是否选中行
- 🎉 操作成功后显示友好的提示信息
- 🔄 自动刷新数据确保界面同步

## 🚀 下一步工作

1. 完成后端服务接口和实现
2. 添加后端控制器接口
3. 测试功能完整性
4. 优化用户交互体验

## 📝 注意事项

- 批量操作需要事务支持，确保数据一致性
- 前端需要处理并发操作的loading状态
- 错误处理要友好，给用户明确的反馈信息
