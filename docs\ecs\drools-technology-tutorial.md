# ECS工资凭证模块Drools重构技术教程 📚

> 本文档为ECS系统工资凭证模块Drools重构项目的技术学习指南，涵盖了项目中需要掌握的核心技术栈。

## 📋 目录

- [1. Drools规则引擎基础](#1-drools规则引擎基础)
- [2. Spring Boot集成Drools](#2-spring-boot集成drools)
- [3. 规则文件管理](#3-规则文件管理)
- [4. 事实对象设计](#4-事实对象设计)
- [5. 规则语法详解](#5-规则语法详解)
- [6. 规则测试与调试](#6-规则测试与调试)
- [7. 规则热加载](#7-规则热加载)
- [8. 性能优化](#8-性能优化)
- [9. 最佳实践](#9-最佳实践)
- [10. 常见问题解决](#10-常见问题解决)

---

## 1. Drools规则引擎基础 🎯

### 1.1 什么是Drools

Drools是一个开源的业务规则管理系统（BRMS），它提供了：
- **规则引擎**：执行业务规则的核心引擎
- **规则语言**：DRL（Drools Rule Language）
- **决策表**：Excel格式的规则定义
- **规则流**：复杂业务流程的规则编排

### 1.2 核心概念

```mermaid
graph TD
    A[📋 Knowledge Base<br/>知识库] --> B[📄 Rules<br/>规则]
    A --> C[📊 Facts<br/>事实]
    A --> D[🔄 Session<br/>会话]

    B --> B1[🏷️ Rule Name<br/>规则名称]
    B --> B2[🔍 When Condition<br/>条件部分]
    B --> B3[⚡ Then Action<br/>动作部分]

    C --> C1[📦 Working Memory<br/>工作内存]
    C --> C2[🎯 Pattern Matching<br/>模式匹配]

    D --> D1[🔥 Fire Rules<br/>触发规则]
    D --> D2[📤 Results<br/>执行结果]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
```

### 1.3 工作原理

**Drools执行流程**：
1. **加载规则**：从DRL文件或数据库加载规则到知识库
2. **创建会话**：创建KieSession工作会话
3. **插入事实**：将业务数据作为事实对象插入工作内存
4. **模式匹配**：规则引擎匹配事实与规则条件
5. **执行规则**：触发匹配的规则执行动作
6. **获取结果**：从事实对象中获取处理结果

## 2. Spring Boot集成Drools 🚀

### 2.1 依赖配置

在`pom.xml`中添加Drools依赖：

```xml
<dependencies>
    <!-- Drools核心依赖 -->
    <dependency>
        <groupId>org.drools</groupId>
        <artifactId>drools-core</artifactId>
        <version>7.74.1.Final</version>
    </dependency>

    <!-- Drools编译器 -->
    <dependency>
        <groupId>org.drools</groupId>
        <artifactId>drools-compiler</artifactId>
        <version>7.74.1.Final</version>
    </dependency>

    <!-- KIE API -->
    <dependency>
        <groupId>org.kie</groupId>
        <artifactId>kie-api</artifactId>
        <version>7.74.1.Final</version>
    </dependency>

    <!-- KIE Spring集成 -->
    <dependency>
        <groupId>org.kie</groupId>
        <artifactId>kie-spring</artifactId>
        <version>7.74.1.Final</version>
    </dependency>
</dependencies>
```

### 2.2 配置类

创建Drools配置类：

```java
@Configuration
@EnableConfigurationProperties(DroolsProperties.class)
public class DroolsConfig {

    @Autowired
    private DroolsProperties droolsProperties;

    /**
     * KieContainer Bean配置
     */
    @Bean
    public KieContainer kieContainer() {
        KieServices kieServices = KieServices.Factory.get();
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem();

        // 加载规则文件
        loadRuleFiles(kieFileSystem);

        // 构建知识库
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();

        // 检查构建结果
        Results results = kieBuilder.getResults();
        if (results.hasMessages(Message.Level.ERROR)) {
            throw new RuntimeException("规则文件构建失败: " + results.getMessages());
        }

        return kieServices.newKieContainer(kieBuilder.getKieModule().getReleaseId());
    }

    /**
     * 加载规则文件
     */
    private void loadRuleFiles(KieFileSystem kieFileSystem) {
        try {
            // 从classpath加载规则文件
            Resource[] resources = ResourcePatternUtils
                .getResourcePatternResolver(new DefaultResourceLoader())
                .getResources("classpath*:rules/**/*.drl");

            for (Resource resource : resources) {
                kieFileSystem.write(ResourceFactory.newClassPathResource(
                    resource.getFilename(), "UTF-8"));
            }
        } catch (IOException e) {
            throw new RuntimeException("加载规则文件失败", e);
        }
    }
}
```

### 2.3 配置属性

创建配置属性类：

```java
@ConfigurationProperties(prefix = "drools")
@Data
public class DroolsProperties {

    /**
     * 规则文件路径
     */
    private String rulesPath = "classpath*:rules/**/*.drl";

    /**
     * 是否启用规则缓存
     */
    private boolean cacheEnabled = true;

    /**
     * 规则热加载间隔（秒）
     */
    private int reloadInterval = 60;

    /**
     * 是否启用调试模式
     */
    private boolean debugEnabled = false;
}
```

## 3. 规则文件管理 📁

### 3.1 规则文件结构

推荐的规则文件组织结构：

```
src/main/resources/rules/
├── salary/                          # 工资相关规则
│   ├── salary-type-rules.drl        # 工资类型判断规则
│   ├── salary-accrual-rules.drl     # 工资计提规则
│   ├── salary-payment-rules.drl     # 工资发放规则
│   ├── insurance-rules.drl          # 保险规则
│   └── validation-rules.drl         # 验证规则
├── common/                          # 通用规则
│   ├── validation-common.drl        # 通用验证规则
│   └── utility-rules.drl            # 工具规则
└── config/                          # 配置规则
    └── system-config-rules.drl      # 系统配置规则
```

### 3.2 规则文件模板

标准的DRL文件模板：

```drools
package com.jp.med.ecs.rules.salary

// 导入必要的类
import com.jp.med.ecs.modules.salary.rule.fact.SalaryTaskFact
import com.jp.med.ecs.modules.salary.rule.fact.SalaryDetailFact
import com.jp.med.ecs.modules.salary.rule.decision.VoucherEntry
import com.jp.med.ecs.modules.salary.rule.decision.ProcessType
import java.math.BigDecimal

// 全局变量定义
global org.slf4j.Logger logger

// 规则定义
rule "规则名称"
    // 规则属性
    salience 100                    // 优先级
    no-loop true                    // 防止无限循环
    agenda-group "salary-group"     // 规则组

when
    // 条件部分
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact(reimType == "岗位工资") from $task.details

then
    // 动作部分
    logger.info("执行规则: {}", drools.getRule().getName());

    // 业务逻辑处理
    VoucherEntry entry = new VoucherEntry();
    entry.setSubjectCode("5101030199");
    entry.setDebitAmount($detail.getReimAmt());

    $task.addVoucherEntry(entry);
    $task.setRuleApplied(drools.getRule().getName());
end
```

## 4. 事实对象设计 📦

### 4.1 事实对象原则

事实对象是规则引擎的数据载体，设计时需要遵循：
- **简单性**：避免复杂的继承关系
- **可变性**：支持规则执行过程中的状态变更
- **序列化**：支持分布式环境下的对象传输

### 4.2 核心事实对象

**工资任务事实对象**：

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalaryTaskFact implements Serializable {

    // 基础信息
    private Integer salaryId;           // 工资任务ID
    private String ffMth;               // 发放月份
    private Integer num;                // 工资条数
    private String salaryType;          // 工资类型

    // 金额信息
    private BigDecimal shouldPay;       // 应发合计
    private BigDecimal reducePay;       // 扣款合计
    private BigDecimal realPay;         // 实发合计

    // 处理状态
    private ProcessType processType;    // 处理类型
    private String voucherTemplate;     // 凭证模板
    private String flowTemplate;        // 流程模板
    private boolean readyForVoucher;    // 是否可生成凭证
    private boolean specialProcessing;  // 是否特殊处理

    // 明细数据
    private List<SalaryDetailFact> details = new ArrayList<>();

    // 处理结果
    private List<VoucherEntry> voucherEntries = new ArrayList<>();
    private List<BudgetEntry> budgetEntries = new ArrayList<>();
    private List<String> validationErrors = new ArrayList<>();
    private List<String> appliedRules = new ArrayList<>();

    // 业务方法
    public void addVoucherEntry(VoucherEntry entry) {
        this.voucherEntries.add(entry);
    }

    public void addValidationError(String error) {
        this.validationErrors.add(error);
    }

    public void setRuleApplied(String ruleName) {
        this.appliedRules.add(ruleName);
    }

    public boolean hasValidationErrors() {
        return !validationErrors.isEmpty();
    }
}
```

**工资明细事实对象**：

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalaryDetailFact implements Serializable {

    // 组织信息
    private String orgId;               // 科室ID
    private String orgName;             // 科室名称
    private String deptType;            // 科室类型：1-业务科室，2-管理科室

    // 员工信息
    private String empCode;             // 员工编号
    private String empName;             // 员工姓名
    private String empType;             // 人员类型
    private Integer empCount;           // 人数

    // 报销信息
    private String reimName;            // 报销项目名称
    private String reimType;            // 报销类型
    private BigDecimal reimAmt;         // 报销金额
    private String reimDesc;            // 报销摘要

    // 业务方法
    public boolean isBusinessDept() {
        return "1".equals(deptType);
    }

    public boolean isManagementDept() {
        return "2".equals(deptType);
    }

    public boolean isEstablishedEmployee() {
        return Arrays.asList("在编", "血防占编").contains(empType);
    }

    public boolean isContractEmployee() {
        return empType != null && empType.startsWith("编外-");
    }
}
```

### 4.3 决策结果对象

**凭证分录对象**：

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VoucherEntry implements Serializable {

    private String subjectCode;         // 科目代码
    private String subjectName;         // 科目名称
    private BigDecimal debitAmount;     // 借方金额
    private BigDecimal creditAmount;    // 贷方金额
    private String description;         // 摘要
    private Map<String, String> auxiliaryItems = new HashMap<>(); // 辅助项

    // 工具方法
    public void setAuxiliaryItems(String... keyValues) {
        for (int i = 0; i < keyValues.length; i += 2) {
            if (i + 1 < keyValues.length) {
                auxiliaryItems.put(keyValues[i], keyValues[i + 1]);
            }
        }
    }

    public boolean isDebitEntry() {
        return debitAmount != null && debitAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    public boolean isCreditEntry() {
        return creditAmount != null && creditAmount.compareTo(BigDecimal.ZERO) > 0;
    }
}
```

## 5. 规则语法详解 📝

### 5.1 基本语法结构

```drools
rule "规则名称"
    // 规则属性（可选）
    salience 100
    no-loop true

when
    // 条件部分（LHS - Left Hand Side）
    $fact: FactClass(property == "value")

then
    // 动作部分（RHS - Right Hand Side）
    $fact.setResult("processed");
    update($fact);
end
```

### 5.2 条件语法

**基本条件匹配**：

```drools
// 简单属性匹配
$task: SalaryTaskFact(salaryType == "1")

// 数值比较
$task: SalaryTaskFact(shouldPay > 1000.00)

// 字符串匹配
$detail: SalaryDetailFact(empType matches "编外-.*")

// 集合包含
$detail: SalaryDetailFact(empType in ("在编", "血防占编"))

// 空值检查
$detail: SalaryDetailFact(empCode != null && empCode != "")

// 复合条件
$task: SalaryTaskFact(
    salaryType == "1" &&
    shouldPay > 0 &&
    num > 0
)
```

**集合操作**：

```drools
// 从集合中获取元素
$detail: SalaryDetailFact(reimType == "岗位工资") from $task.details

// 集合累积
$totalAmount: BigDecimal() from accumulate(
    SalaryDetailFact($amt: reimAmt) from $task.details,
    sum($amt)
)

// 集合过滤
$businessDetails: List() from collect(
    SalaryDetailFact(deptType == "1") from $task.details
)

// 存在性检查
exists SalaryDetailFact(reimType == "岗位工资") from $task.details

// 不存在检查
not SalaryDetailFact(empCode == "EMP001") from $task.details
```

### 5.3 动作语法

**基本操作**：

```drools
then
    // 修改事实对象
    $task.setProcessType(ProcessType.DIRECT_VOUCHER);

    // 创建新对象
    VoucherEntry entry = new VoucherEntry();
    entry.setSubjectCode("5101030199");
    entry.setDebitAmount($detail.getReimAmt());

    // 添加到集合
    $task.addVoucherEntry(entry);

    // 更新工作内存
    update($task);

    // 插入新事实
    insert(entry);

    // 删除事实
    retract($detail);

    // 日志输出
    System.out.println("规则执行: " + drools.getRule().getName());
end
```

### 5.4 高级语法特性

**规则继承**：

```drools
rule "基础工资计提规则"
when
    $task: SalaryTaskFact(salaryType == "1")
    $detail: SalaryDetailFact() from $task.details
then
    // 基础处理逻辑
end

rule "业务科室工资计提规则" extends "基础工资计提规则"
when
    $detail: SalaryDetailFact(deptType == "1")
then
    // 业务科室特殊处理
end
```

**条件函数**：

```drools
// 自定义函数
function boolean isSpecialEmployee(String empCode) {
    return Arrays.asList("邹毅", "田晓辉", "刘黎").contains(empCode);
}

rule "特殊员工处理"
when
    $detail: SalaryDetailFact(isSpecialEmployee(empCode))
then
    // 特殊员工处理逻辑
end
```

## 6. 规则测试与调试 🔍

### 6.1 单元测试框架

创建规则测试基类：

```java
@SpringBootTest
@TestPropertySource(properties = {
    "drools.debug-enabled=true",
    "logging.level.org.drools=DEBUG"
})
public abstract class DroolsTestBase {

    @Autowired
    protected KieContainer kieContainer;

    protected KieSession createKieSession() {
        KieSession kieSession = kieContainer.newKieSession();

        // 添加调试监听器
        kieSession.addEventListener(new DebugAgendaEventListener());
        kieSession.addEventListener(new DebugRuleRuntimeEventListener());

        return kieSession;
    }

    protected SalaryTaskFact createTestSalaryTask(String salaryType) {
        SalaryTaskFact task = new SalaryTaskFact();
        task.setSalaryId(1001);
        task.setSalaryType(salaryType);
        task.setFfMth("2024-01");
        task.setNum(10);
        task.setShouldPay(new BigDecimal("100000.00"));
        task.setReducePay(new BigDecimal("20000.00"));
        task.setRealPay(new BigDecimal("80000.00"));
        return task;
    }

    protected SalaryDetailFact createTestSalaryDetail(String reimType, String deptType, String empType) {
        SalaryDetailFact detail = new SalaryDetailFact();
        detail.setOrgId("1001");
        detail.setOrgName("内科");
        detail.setDeptType(deptType);
        detail.setEmpType(empType);
        detail.setReimType(reimType);
        detail.setReimAmt(new BigDecimal("5000.00"));
        return detail;
    }
}
```

### 6.2 规则测试用例

**工资类型判断规则测试**：

```java
@Test
public class SalaryTypeRulesTest extends DroolsTestBase {

    @Test
    public void testSalaryAccrualTypeRule() {
        // 准备测试数据
        SalaryTaskFact task = createTestSalaryTask("1");

        // 创建会话并插入事实
        KieSession kieSession = createKieSession();
        kieSession.insert(task);

        // 执行规则
        int firedRules = kieSession.fireAllRules();

        // 验证结果
        assertThat(firedRules).isGreaterThan(0);
        assertThat(task.getProcessType()).isEqualTo(ProcessType.DIRECT_VOUCHER);
        assertThat(task.getVoucherTemplate()).isEqualTo("SALARY_ACCRUAL");
        assertThat(task.getAppliedRules()).contains("R001_工资计提类型判断");

        kieSession.dispose();
    }

    @Test
    public void testSalaryPaymentTypeRule() {
        // 准备测试数据
        SalaryTaskFact task = createTestSalaryTask("2");

        // 创建会话并插入事实
        KieSession kieSession = createKieSession();
        kieSession.insert(task);

        // 执行规则
        int firedRules = kieSession.fireAllRules();

        // 验证结果
        assertThat(firedRules).isGreaterThan(0);
        assertThat(task.getProcessType()).isEqualTo(ProcessType.REIMBURSEMENT_FLOW);
        assertThat(task.getFlowTemplate()).isEqualTo("SALARY_PAYMENT");
        assertThat(task.getAppliedRules()).contains("R002_工资发放类型判断");

        kieSession.dispose();
    }

    @Test
    public void testInvalidSalaryTypeRule() {
        // 准备测试数据
        SalaryTaskFact task = createTestSalaryTask("9");

        // 创建会话并插入事实
        KieSession kieSession = createKieSession();
        kieSession.insert(task);

        // 执行规则
        int firedRules = kieSession.fireAllRules();

        // 验证结果
        assertThat(firedRules).isGreaterThan(0);
        assertThat(task.getValidationErrors()).isNotEmpty();
        assertThat(task.getValidationErrors().get(0)).contains("未知的工资类型");
        assertThat(task.getAppliedRules()).contains("R005_未知工资类型处理");

        kieSession.dispose();
    }
}
```

### 6.3 调试技巧

**启用规则调试**：

```java
@Component
public class DroolsDebugListener implements AgendaEventListener {

    private static final Logger logger = LoggerFactory.getLogger(DroolsDebugListener.class);

    @Override
    public void matchCreated(MatchCreatedEvent event) {
        logger.debug("规则匹配创建: {}", event.getMatch().getRule().getName());
    }

    @Override
    public void beforeMatchFired(BeforeMatchFiredEvent event) {
        logger.debug("规则开始执行: {}", event.getMatch().getRule().getName());

        // 打印事实对象状态
        for (FactHandle factHandle : event.getMatch().getFactHandles()) {
            Object fact = ((DefaultKieSession) event.getKieRuntime()).getObject(factHandle);
            logger.debug("事实对象: {}", fact);
        }
    }

    @Override
    public void afterMatchFired(AfterMatchFiredEvent event) {
        logger.debug("规则执行完成: {}", event.getMatch().getRule().getName());
    }
}
```

**规则执行跟踪**：

```java
@Service
public class RuleExecutionTracker {

    private final ThreadLocal<List<String>> executionTrace = new ThreadLocal<>();

    public void startTrace() {
        executionTrace.set(new ArrayList<>());
    }

    public void addTrace(String ruleName, String message) {
        List<String> trace = executionTrace.get();
        if (trace != null) {
            trace.add(String.format("[%s] %s: %s",
                LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_TIME),
                ruleName, message));
        }
    }

    public List<String> getTrace() {
        return executionTrace.get();
    }

    public void clearTrace() {
        executionTrace.remove();
    }
}
```

## 7. 规则热加载 🔄

### 7.1 规则热加载实现

```java
@Service
public class RuleHotReloadService {

    private static final Logger logger = LoggerFactory.getLogger(RuleHotReloadService.class);

    @Autowired
    private DroolsProperties droolsProperties;

    private KieContainer kieContainer;
    private final Map<String, Long> ruleFileTimestamps = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        this.kieContainer = buildKieContainer();
        if (droolsProperties.getReloadInterval() > 0) {
            startHotReloadScheduler();
        }
    }

    /**
     * 启动热加载调度器
     */
    @Scheduled(fixedDelayString = "${drools.reload-interval:60}000")
    public void checkAndReloadRules() {
        try {
            if (hasRuleFilesChanged()) {
                logger.info("检测到规则文件变更，开始热加载...");
                reloadRules();
                logger.info("规则热加载完成");
            }
        } catch (Exception e) {
            logger.error("规则热加载失败", e);
        }
    }

    /**
     * 检查规则文件是否变更
     */
    private boolean hasRuleFilesChanged() {
        try {
            Resource[] resources = ResourcePatternUtils
                .getResourcePatternResolver(new DefaultResourceLoader())
                .getResources(droolsProperties.getRulesPath());

            for (Resource resource : resources) {
                String filename = resource.getFilename();
                long lastModified = resource.lastModified();

                Long cachedTimestamp = ruleFileTimestamps.get(filename);
                if (cachedTimestamp == null || lastModified > cachedTimestamp) {
                    ruleFileTimestamps.put(filename, lastModified);
                    return true;
                }
            }
            return false;
        } catch (IOException e) {
            logger.error("检查规则文件变更失败", e);
            return false;
        }
    }

    /**
     * 重新加载规则
     */
    public synchronized void reloadRules() {
        KieContainer newContainer = buildKieContainer();
        KieContainer oldContainer = this.kieContainer;

        this.kieContainer = newContainer;

        // 清理旧容器
        if (oldContainer != null) {
            oldContainer.dispose();
        }

        // 发布规则重载事件
        ApplicationContextHolder.publishEvent(new RuleReloadedEvent(this));
    }

    /**
     * 构建KieContainer
     */
    private KieContainer buildKieContainer() {
        KieServices kieServices = KieServices.Factory.get();
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem();

        // 加载规则文件
        loadRuleFiles(kieFileSystem);

        // 构建知识库
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();

        // 检查构建结果
        Results results = kieBuilder.getResults();
        if (results.hasMessages(Message.Level.ERROR)) {
            throw new RuntimeException("规则构建失败: " + results.getMessages());
        }

        return kieServices.newKieContainer(kieBuilder.getKieModule().getReleaseId());
    }

    public KieContainer getKieContainer() {
        return kieContainer;
    }
}
```

### 7.2 规则版本管理

```java
@Entity
@Table(name = "salary_rule_version")
@Data
public class SalaryRuleVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "version_number")
    private String versionNumber;

    @Column(name = "rule_content", columnDefinition = "TEXT")
    private String ruleContent;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private RuleStatus status;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "activated_time")
    private LocalDateTime activatedTime;

    @Column(name = "description")
    private String description;
}

@Service
public class RuleVersionService {

    @Autowired
    private SalaryRuleVersionRepository ruleVersionRepository;

    @Autowired
    private RuleHotReloadService ruleHotReloadService;

    /**
     * 发布新版本规则
     */
    @Transactional
    public void publishRuleVersion(String ruleContent, String description, String createdBy) {
        // 验证规则语法
        validateRuleContent(ruleContent);

        // 生成版本号
        String versionNumber = generateVersionNumber();

        // 保存规则版本
        SalaryRuleVersion ruleVersion = new SalaryRuleVersion();
        ruleVersion.setVersionNumber(versionNumber);
        ruleVersion.setRuleContent(ruleContent);
        ruleVersion.setStatus(RuleStatus.DRAFT);
        ruleVersion.setCreatedBy(createdBy);
        ruleVersion.setCreatedTime(LocalDateTime.now());
        ruleVersion.setDescription(description);

        ruleVersionRepository.save(ruleVersion);
    }

    /**
     * 激活规则版本
     */
    @Transactional
    public void activateRuleVersion(Long versionId) {
        SalaryRuleVersion ruleVersion = ruleVersionRepository.findById(versionId)
            .orElseThrow(() -> new RuntimeException("规则版本不存在"));

        // 停用当前激活的版本
        ruleVersionRepository.deactivateAllVersions();

        // 激活新版本
        ruleVersion.setStatus(RuleStatus.ACTIVE);
        ruleVersion.setActivatedTime(LocalDateTime.now());
        ruleVersionRepository.save(ruleVersion);

        // 写入规则文件
        writeRuleToFile(ruleVersion.getRuleContent());

        // 触发热加载
        ruleHotReloadService.reloadRules();
    }
}
```

## 8. 性能优化 ⚡

### 8.1 规则优化策略

**规则优先级设置**：

```drools
// 高优先级规则先执行
rule "数据验证规则"
    salience 100
when
    $task: SalaryTaskFact(salaryId == null)
then
    $task.addValidationError("工资ID不能为空");
end

// 低优先级规则后执行
rule "汇总计算规则"
    salience 10
when
    $task: SalaryTaskFact(validationErrors.size() == 0)
then
    // 汇总计算逻辑
end
```

**规则分组执行**：

```java
@Service
public class OptimizedRuleExecutor {

    public SalaryVoucherDecision executeRules(SalaryTaskFact task) {
        KieSession kieSession = kieContainer.newKieSession();

        try {
            // 插入事实对象
            kieSession.insert(task);

            // 分阶段执行规则
            executeValidationRules(kieSession);
            executeBusinessRules(kieSession);
            executeCalculationRules(kieSession);

            return buildDecision(task);
        } finally {
            kieSession.dispose();
        }
    }

    private void executeValidationRules(KieSession kieSession) {
        kieSession.getAgenda().getAgendaGroup("validation").setFocus();
        kieSession.fireAllRules();
    }

    private void executeBusinessRules(KieSession kieSession) {
        kieSession.getAgenda().getAgendaGroup("business").setFocus();
        kieSession.fireAllRules();
    }

    private void executeCalculationRules(KieSession kieSession) {
        kieSession.getAgenda().getAgendaGroup("calculation").setFocus();
        kieSession.fireAllRules();
    }
}
```

### 8.2 内存优化

**事实对象池化**：

```java
@Component
public class FactObjectPool {

    private final ObjectPool<SalaryTaskFact> taskFactPool;
    private final ObjectPool<SalaryDetailFact> detailFactPool;

    public FactObjectPool() {
        this.taskFactPool = new GenericObjectPool<>(new SalaryTaskFactFactory());
        this.detailFactPool = new GenericObjectPool<>(new SalaryDetailFactFactory());
    }

    public SalaryTaskFact borrowTaskFact() {
        try {
            return taskFactPool.borrowObject();
        } catch (Exception e) {
            return new SalaryTaskFact();
        }
    }

    public void returnTaskFact(SalaryTaskFact fact) {
        try {
            // 清理对象状态
            fact.reset();
            taskFactPool.returnObject(fact);
        } catch (Exception e) {
            // 忽略归还失败
        }
    }
}
```

### 8.3 缓存策略

**规则结果缓存**：

```java
@Service
public class RuleResultCache {

    private final Cache<String, SalaryVoucherDecision> cache;

    public RuleResultCache() {
        this.cache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofMinutes(30))
            .recordStats()
            .build();
    }

    public SalaryVoucherDecision getOrExecute(SalaryTaskFact task,
                                             Supplier<SalaryVoucherDecision> executor) {
        String cacheKey = generateCacheKey(task);
        return cache.get(cacheKey, key -> executor.get());
    }

    private String cacheKey(SalaryTaskFact task) {
        return String.format("%s_%s_%s_%s",
            task.getSalaryType(),
            task.getFfMth(),
            task.getDetails().size(),
            task.getDetails().stream()
                .mapToInt(d -> d.hashCode())
                .sum());
    }
}
```

## 9. 最佳实践 🏆

### 9.1 规则设计原则

1. **单一职责**：每个规则只处理一个业务逻辑
2. **原子性**：规则操作要么全部成功，要么全部失败
3. **幂等性**：多次执行同一规则应产生相同结果
4. **可测试性**：每个规则都应该有对应的单元测试

### 9.2 规则命名规范

```drools
// 规则命名格式：R{编号}_{业务描述}
rule "R001_工资计提类型判断"
rule "R101_业务科室岗位工资计提"
rule "R701_工资任务基础数据验证"

// 规则分组命名
agenda-group "validation"    // 验证规则组
agenda-group "business"      // 业务规则组
agenda-group "calculation"   // 计算规则组
```

### 9.3 错误处理策略

```java
@Component
public class RuleErrorHandler {

    private static final Logger logger = LoggerFactory.getLogger(RuleErrorHandler.class);

    @EventListener
    public void handleRuleError(RuleExecutionErrorEvent event) {
        logger.error("规则执行错误: {}", event.getErrorMessage(), event.getException());

        // 记录错误详情
        RuleExecutionLog errorLog = new RuleExecutionLog();
        errorLog.setRuleName(event.getRuleName());
        errorLog.setErrorMessage(event.getErrorMessage());
        errorLog.setStackTrace(ExceptionUtils.getStackTrace(event.getException()));
        errorLog.setOccurredTime(LocalDateTime.now());

        // 保存错误日志
        ruleExecutionLogRepository.save(errorLog);

        // 发送告警通知
        alertService.sendRuleErrorAlert(errorLog);
    }
}
```

## 10. 常见问题解决 ❓

### 10.1 规则不执行问题

**问题**：规则定义正确但不执行
**解决方案**：
1. 检查事实对象是否正确插入工作内存
2. 验证规则条件是否匹配事实对象属性
3. 确认规则优先级设置是否合理
4. 检查是否有no-loop等属性阻止执行

### 10.2 内存泄漏问题

**问题**：长时间运行后内存占用过高
**解决方案**：
1. 及时dispose KieSession
2. 避免在规则中创建大量临时对象
3. 使用对象池复用事实对象
4. 定期清理规则缓存

### 10.3 性能问题

**问题**：规则执行速度慢
**解决方案**：
1. 优化规则条件，避免复杂计算
2. 合理设置规则优先级
3. 使用规则分组减少匹配范围
4. 启用规则结果缓存

### 10.4 规则冲突问题

**问题**：多个规则同时匹配导致冲突
**解决方案**：
1. 设置互斥规则组
2. 使用salience控制执行顺序
3. 添加更精确的条件判断
4. 使用activation-group属性

```drools
rule "规则A"
    salience 100
    activation-group "mutual-exclusive-group"
when
    // 条件A
then
    // 动作A
end

rule "规则B"
    salience 90
    activation-group "mutual-exclusive-group"
when
    // 条件B
then
    // 动作B
end
```

---

## 📚 参考资源

- [Drools官方文档](https://docs.drools.org/)
- [Drools GitHub仓库](https://github.com/kiegroup/drools)
- [Spring Boot集成Drools示例](https://spring.io/guides/)
- [规则引擎最佳实践](https://www.redhat.com/en/technologies/jboss-middleware/business-rules)

---

## 🎯 学习路径建议

1. **基础阶段**（1-2周）
   - 学习Drools基本概念和语法
   - 完成简单规则编写练习
   - 掌握Spring Boot集成方法

2. **进阶阶段**（2-3周）
   - 深入学习规则语法和高级特性
   - 实践复杂业务规则设计
   - 掌握规则测试和调试技巧

3. **高级阶段**（3-4周）
   - 学习规则热加载和版本管理
   - 掌握性能优化技巧
   - 实践大型项目规则架构设计

4. **实战阶段**（4-6周）
   - 参与ECS工资凭证模块重构
   - 编写实际业务规则
   - 进行系统集成测试

通过系统学习和实践，您将能够熟练掌握Drools规则引擎技术，为ECS系统工资凭证模块的重构提供强有力的技术支撑！ 🚀
```