<template>
  <j-container>
    <template #content>
      <div class="app-menu-demo">
        <!-- 演示说明 -->
        <div class="demo-header mb-6">
          <h1 class="text-2xl font-bold text-gray-900 mb-4">App风格菜单演示</h1>
          <div class="demo-description">
            <n-alert type="info" class="mb-4">
              <template #icon>
                <n-icon><InformationCircleOutline /></n-icon>
              </template>
              <div>
                <strong>功能说明：</strong>
                <ul class="mt-2 space-y-1 text-sm">
                  <li>• 桌面端：网格卡片布局，类似应用商店</li>
                  <li>• 移动端：手机App风格，支持大卡片（2x2）展示</li>
                  <li>• 一级菜单作为卡片标题，二级菜单作为卡片内容</li>
                  <li>• 二级菜单较多时使用大卡片形式，可点击展开</li>
                  <li>• 支持搜索功能和警告徽章显示</li>
                </ul>
              </div>
            </n-alert>
          </div>
        </div>

        <!-- 设备切换演示 -->
        <div class="device-switcher mb-6">
          <n-card>
            <template #header>
              <div class="flex items-center gap-2">
                <n-icon size="20"><DevicesOutline /></n-icon>
                <span>设备模式切换</span>
              </div>
            </template>

            <div class="flex items-center gap-4">
              <n-button-group>
                <n-button :type="demoMode === 'desktop' ? 'primary' : 'default'" @click="demoMode = 'desktop'">
                  <template #icon>
                    <n-icon><DesktopOutline /></n-icon>
                  </template>
                  桌面端
                </n-button>
                <n-button :type="demoMode === 'mobile' ? 'primary' : 'default'" @click="demoMode = 'mobile'">
                  <template #icon>
                    <n-icon><PhonePortraitOutline /></n-icon>
                  </template>
                  移动端
                </n-button>
              </n-button-group>

              <n-divider vertical />

              <div class="demo-info">
                <span class="text-sm text-gray-600">
                  当前模式：<strong>{{ demoMode === 'desktop' ? '桌面端' : '移动端' }}</strong>
                </span>
              </div>
            </div>
          </n-card>
        </div>

        <!-- 演示区域 -->
        <div class="demo-container">
          <n-card>
            <template #header>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <n-icon size="20"><GridOutline /></n-icon>
                  <span>{{ demoMode === 'desktop' ? '桌面端菜单' : '移动端菜单' }}</span>
                </div>
                <n-tag :type="demoMode === 'desktop' ? 'info' : 'success'">
                  {{ demoMode === 'desktop' ? 'Desktop' : 'Mobile' }}
                </n-tag>
              </div>
            </template>

            <!-- 桌面端演示 -->
            <div v-if="demoMode === 'desktop'" class="desktop-demo">
              <AppMenu />
            </div>

            <!-- 移动端演示 -->
            <div v-else class="mobile-demo">
              <div class="mobile-frame">
                <div class="mobile-screen">
                  <AppMenuMob />
                </div>
              </div>
            </div>
          </n-card>
        </div>

        <!-- 功能特性说明 -->
        <div class="features-section mt-6">
          <n-card>
            <template #header>
              <div class="flex items-center gap-2">
                <n-icon size="20"><StarOutline /></n-icon>
                <span>功能特性</span>
              </div>
            </template>

            <n-grid :cols="2" :x-gap="24" :y-gap="16">
              <n-grid-item>
                <div class="feature-item">
                  <div class="feature-icon">
                    <n-icon size="32" color="#3b82f6"><GridOutline /></n-icon>
                  </div>
                  <div class="feature-content">
                    <h3 class="feature-title">响应式布局</h3>
                    <p class="feature-desc"> 根据设备类型自动切换布局风格，桌面端使用网格布局，移动端使用App风格 </p>
                  </div>
                </div>
              </n-grid-item>

              <n-grid-item>
                <div class="feature-item">
                  <div class="feature-icon">
                    <n-icon size="32" color="#10b981"><CubeOutline /></n-icon>
                  </div>
                  <div class="feature-content">
                    <h3 class="feature-title">智能卡片</h3>
                    <p class="feature-desc"> 二级菜单较多时自动使用大卡片（2x2）展示，支持预览子功能 </p>
                  </div>
                </div>
              </n-grid-item>

              <n-grid-item>
                <div class="feature-item">
                  <div class="feature-icon">
                    <n-icon size="32" color="#f59e0b"><SearchOutline /></n-icon>
                  </div>
                  <div class="feature-content">
                    <h3 class="feature-title">搜索功能</h3>
                    <p class="feature-desc"> 支持实时搜索菜单功能，快速定位所需功能模块 </p>
                  </div>
                </div>
              </n-grid-item>

              <n-grid-item>
                <div class="feature-item">
                  <div class="feature-icon">
                    <n-icon size="32" color="#ef4444"><NotificationsOutline /></n-icon>
                  </div>
                  <div class="feature-content">
                    <h3 class="feature-title">警告徽章</h3>
                    <p class="feature-desc"> 支持显示待办数量和警告信息，及时提醒用户处理 </p>
                  </div>
                </div>
              </n-grid-item>
            </n-grid>
          </n-card>
        </div>

        <!-- 技术实现说明 -->
        <div class="tech-section mt-6">
          <n-card>
            <template #header>
              <div class="flex items-center gap-2">
                <n-icon size="20"><CodeSlashOutline /></n-icon>
                <span>技术实现</span>
              </div>
            </template>

            <div class="tech-content">
              <n-tabs type="line" animated>
                <n-tab-pane name="structure" tab="文件结构">
                  <div class="code-block">
                    <pre><code>src/views/modules/home/
├── app-menu.vue          # 桌面端App菜单
├── app-menu-mob.vue      # 移动端App菜单
└── index.vue             # 原有工作台（保持兼容）

路由自动加载规则：
- 移动端：优先加载 app-menu-mob.vue
- 桌面端：加载 app-menu.vue
- 回退：如果没有找到，使用 index.vue</code></pre>
                  </div>
                </n-tab-pane>

                <n-tab-pane name="features" tab="核心特性">
                  <div class="feature-list">
                    <div class="feature-point">
                      <strong>智能组件加载：</strong>
                      根据设备类型自动选择合适的组件
                    </div>
                    <div class="feature-point">
                      <strong>菜单数据复用：</strong>
                      使用现有的sideBar菜单数据结构
                    </div>
                    <div class="feature-point">
                      <strong>响应式设计：</strong>
                      支持不同屏幕尺寸的自适应布局
                    </div>
                    <div class="feature-point">
                      <strong>触摸优化：</strong>
                      移动端使用触摸友好的交互设计
                    </div>
                  </div>
                </n-tab-pane>

                <n-tab-pane name="todo" tab="开发计划">
                  <div class="todo-list">
                    <div class="todo-item completed">
                      <n-icon color="#10b981"><CheckmarkCircleOutline /></n-icon>
                      <span>基础App菜单布局</span>
                    </div>
                    <div class="todo-item completed">
                      <n-icon color="#10b981"><CheckmarkCircleOutline /></n-icon>
                      <span>移动端专用组件</span>
                    </div>
                    <div class="todo-item completed">
                      <n-icon color="#10b981"><CheckmarkCircleOutline /></n-icon>
                      <span>大卡片展示功能</span>
                    </div>
                    <div class="todo-item pending">
                      <n-icon color="#f59e0b"><TimeOutline /></n-icon>
                      <span>三级菜单支持</span>
                    </div>
                    <div class="todo-item pending">
                      <n-icon color="#f59e0b"><TimeOutline /></n-icon>
                      <span>菜单个性化配置</span>
                    </div>
                    <div class="todo-item pending">
                      <n-icon color="#f59e0b"><TimeOutline /></n-icon>
                      <span>使用频率统计</span>
                    </div>
                  </div>
                </n-tab-pane>
              </n-tabs>
            </div>
          </n-card>
        </div>
      </div>
    </template>
  </j-container>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    NCard,
    NAlert,
    NIcon,
    NButton,
    NButtonGroup,
    NDivider,
    NTag,
    NGrid,
    NGridItem,
    NTabs,
    NTabPane,
  } from 'naive-ui'
  import {
    InformationCircleOutline,
    DevicesOutline,
    DesktopOutline,
    PhonePortraitOutline,
    GridOutline,
    StarOutline,
    CubeOutline,
    SearchOutline,
    NotificationsOutline,
    CodeSlashOutline,
    CheckmarkCircleOutline,
    TimeOutline,
  } from '@vicons/ionicons5'
  import AppMenu from './app-menu.vue'
  import AppMenuMob from './app-menu-mob.vue'

  // 演示模式
  const demoMode = ref<'desktop' | 'mobile'>('desktop')
</script>

<style scoped lang="scss">
  @reference "tailwindcss";
  .app-menu-demo {
    @apply max-w-7xl mx-auto;
  }

  .demo-description ul {
    @apply list-none pl-0;
  }

  .demo-description li {
    @apply text-gray-600;
  }

  .demo-container {
    @apply min-h-96;
  }

  .desktop-demo {
    @apply w-full;
  }

  .mobile-demo {
    @apply flex justify-center;
  }

  .mobile-frame {
    @apply bg-gray-900 rounded-3xl p-2 shadow-2xl;
    width: 375px;
  }

  .mobile-screen {
    @apply bg-white rounded-2xl overflow-hidden;
    height: 667px;
    overflow-y: auto;
  }

  .feature-item {
    @apply flex items-start gap-4 p-4 bg-gray-50 rounded-lg;
  }

  .feature-icon {
    @apply flex-shrink-0;
  }

  .feature-content {
    @apply flex-1;
  }

  .feature-title {
    @apply text-base font-semibold text-gray-900 mb-2;
  }

  .feature-desc {
    @apply text-sm text-gray-600 leading-relaxed;
  }

  .code-block {
    @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto;
  }

  .code-block code {
    @apply text-sm font-mono;
  }

  .feature-list {
    @apply space-y-3;
  }

  .feature-point {
    @apply text-sm text-gray-700 leading-relaxed;
  }

  .todo-list {
    @apply space-y-3;
  }

  .todo-item {
    @apply flex items-center gap-3 text-sm;
  }

  .todo-item.completed {
    @apply text-green-700;
  }

  .todo-item.pending {
    @apply text-amber-700;
  }
</style>
