# BPM流程图移动端放大查看功能 📱

本文档介绍了为 `ProcessInstanceBpmnViewer.vue` 组件实现的移动端流程图放大横屏显示功能。

## 🎯 功能概览

### 核心特性
- ✅ **移动端检测** - 自动检测设备类型，仅在移动端显示放大按钮
- ✅ **全屏弹窗** - 点击放大按钮后全屏显示流程图
- ✅ **纵向优化** - 流程图在移动端纵向显示，适合手机长边
- ✅ **触摸友好** - 44px最小触摸目标，适合手指操作
- ✅ **状态同步** - 弹窗中的流程图与原图数据完全同步

### 用户体验
- 🔍 **置顶放大按钮** - 在流程图卡片标题栏右侧显示
- 📱 **全屏沉浸** - 弹窗占满整个屏幕，无边框干扰
- 🎛️ **缩放控制** - 保留原有的缩放控制功能
- 🏷️ **图例显示** - 保持流程图图例的完整显示

## 🔧 技术实现

### 1. 移动端检测
```typescript
// 使用项目统一的移动端检测机制
const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')
```

### 2. 放大按钮实现
```vue
<!-- 仅在移动端显示的放大按钮 -->
<el-button 
  v-if="isMobileDevice" 
  type="primary" 
  size="small" 
  @click="dialogVisible = true"
>
  <svg><!-- 放大镜图标 --></svg>
  放大查看
</el-button>
```

### 3. 全屏弹窗
```vue
<!-- 移动端全屏流程图弹窗 -->
<el-dialog
  v-if="isMobileDevice"
  v-model="dialogVisible"
  title="流程图"
  :fullscreen="true"
  :show-close="true"
  :close-on-click-modal="false"
  class="mobile-process-dialog"
>
  <div class="mobile-process-container">
    <MyProcessViewer
      key="mobile-designer"
      :activityData="activityList"
      :prefix="bpmnControlForm.prefix"
      :processInstanceData="processInstance"
      :taskData="tasks"
      :value="bpmnXml"
      v-bind="bpmnControlForm"
      @nextTasks="handleNextTasks"
    />
  </div>
</el-dialog>
```

### 4. 移动端样式优化
```css
.mobile-process-dialog {
  /* 全屏显示 */
  :deep(.el-dialog) {
    margin: 0;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }
  
  :deep(.el-dialog__body) {
    padding: 0;
    height: calc(100vh - 60px);
    overflow: hidden;
  }
}

.mobile-process-container {
  /* 流程图容器优化 */
  :deep(.my-process-designer__canvas) {
    height: 100% !important;
    min-height: calc(100vh - 120px);
  }
  
  /* 缩放控制按钮优化 */
  .zoom-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    
    .zoom-btn {
      min-height: 44px; /* 触摸友好 */
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}
```

## 📱 移动端体验优化

### 1. 布局适配
- **标题栏布局**: 使用 `justify-content: space-between` 让放大按钮右对齐
- **按钮尺寸**: 44px最小高度，符合移动端触摸标准
- **全屏显示**: 弹窗占满整个屏幕，无边距干扰

### 2. 交互优化
- **触摸友好**: 所有按钮都有足够的触摸区域
- **视觉反馈**: 按钮状态变化有明显的视觉提示
- **关闭便捷**: 支持点击关闭按钮或返回键关闭弹窗

### 3. 性能优化
- **条件渲染**: 弹窗仅在移动端渲染，减少桌面端资源消耗
- **组件复用**: 使用相同的 `MyProcessViewer` 组件，确保功能一致性
- **数据同步**: 弹窗中的流程图与原图共享相同的数据源

## 🎨 设计特色

### 1. 图标设计
- 使用放大镜图标，直观表达放大功能
- 图标颜色根据弹窗状态动态变化
- 图标与文字组合，提升可理解性

### 2. 布局设计
- 放大按钮位于标题栏右侧，不干扰主要内容
- 全屏弹窗无边框，最大化显示区域
- 缩放控制按钮固定在右下角，便于操作

### 3. 响应式设计
- 仅在移动端显示放大功能
- 桌面端保持原有布局不变
- 适配不同移动设备尺寸

## 🔍 使用场景

### 1. 复杂流程图查看
当流程图节点较多、连线复杂时，移动端小屏幕难以清晰显示，此时可以使用放大功能全屏查看。

### 2. 流程细节审查
在审批过程中需要仔细查看流程图的具体节点和状态时，全屏显示提供更好的视觉体验。

### 3. 横屏查看优化
利用手机的长边进行纵向显示，因为大多数流程图都是水平布局的，纵向显示能更好地展示完整流程。

## 📋 技术要点

### 1. 设备检测集成
- 使用项目统一的 `isMobileDevice` 注入机制
- 响应式检测，支持设备旋转和窗口大小变化
- 类型安全的 TypeScript 实现

### 2. 组件状态管理
- 使用 `ref` 管理弹窗显示状态
- 事件处理函数保持类型安全
- 数据流清晰，易于维护

### 3. 样式架构
- 使用 `:deep()` 选择器深度定制组件样式
- 媒体查询适配不同屏幕尺寸
- CSS变量和主题色集成

## 🚀 扩展可能

### 1. 手势支持
- 可以添加双击放大功能
- 支持捏合缩放手势
- 添加拖拽平移功能

### 2. 旋转适配
- 检测设备方向变化
- 横屏时优化布局
- 自动调整流程图显示方向

### 3. 分享功能
- 添加流程图截图功能
- 支持导出为图片
- 集成分享到其他应用

## 📚 相关文件

- `src/views/modules/bpm/processInstance/detail/ProcessInstanceBpmnViewer.vue` - 主组件文件
- `src/utils/device.ts` - 设备检测工具
- `src/App.vue` - 移动端检测注入点
- `src/components/common/bpm/bpmnProcessDesigner/package/designer/ProcessViewer.vue` - 流程图查看器组件
