# EmpInfo组件移动端优化

## 概述 📱

对 `src/views/modules/hrm/hrmEmp/comps/empInfo.vue` 组件进行移动端适配优化，主要解决移动端显示问题并隐藏不必要的右侧导航。

## 主要修改内容 ✨

### 1. 移动端检测
- 添加了 `isMobileDevice` 计算属性，基于屏幕宽度（≤768px）判断是否为移动设备
- 使用 Vue 3 的 `computed` API 实现响应式检测

### 2. 右侧导航隐藏
- 在移动端隐藏 `right-bar` 组件，避免占用宝贵的屏幕空间
- 使用 `v-if="!isMobileDevice"` 条件渲染

### 3. 布局响应式优化
- 容器添加 `mobile-layout` 类名用于移动端样式控制
- 内容区域添加 `mobile-content` 类名，移动端占满全宽
- 卡片组件添加 `mobile-card` 类名用于移动端样式优化

### 4. 基本信息表单优化
- 头像区域的表单字段：移动端每行显示2个字段（span=12），桌面端每行3个字段（span=8）
- 其他基本信息字段：移动端每行显示2个字段（span=12），桌面端每行4个字段（span=6）

### 5. 移动端样式
```less
// 移动端布局
.mobile-layout {
  flex-direction: column;
  
  .mobile-content {
    width: 100% !important;
    padding: 8px !important;
  }
  
  .mobile-card {
    margin-bottom: 8px !important;
    
    .n-card__content {
      padding: 12px !important;
    }
    
    // 移动端基本信息布局优化
    .base-avatar-wrap {
      flex-direction: column !important;
      align-items: center;
      
      > div:first-child {
        width: 100% !important;
        margin-bottom: 16px;
        
        .n-row {
          .n-col {
            width: 50% !important;
            flex: 0 0 50% !important;
          }
        }
      }
      
      .base-avatar-content {
        width: auto !important;
        margin-bottom: 16px;
      }
    }
    
    // 移动端表格优化
    .n-data-table {
      font-size: 12px;
      
      .n-data-table-th,
      .n-data-table-td {
        padding: 8px 4px !important;
      }
    }
  }
}
```

## 技术实现细节 🔧

### 移动端检测逻辑
```typescript
// 移动端检测
const isMobileDevice = computed(() => {
  return window.innerWidth <= 768
})
```

### 响应式表单布局
```vue
<!-- 头像区域表单字段 -->
<n-col :span="isMobileDevice ? 12 : 8" v-for="(baseItem, index) in module.formItems as any">

<!-- 其他基本信息字段 -->
<n-col :span="isMobileDevice ? 12 : 6" v-for="(baseItem, index) in module.formItems as any">
```

### 条件渲染右侧导航
```vue
<!-- 右侧导航 - 仅在桌面端显示 -->
<right-bar v-if="!isMobileDevice" :list-data="modules"></right-bar>
```

## 优化效果 🎯

1. **空间利用率提升**：移动端隐藏右侧导航，内容区域占满全宽
2. **表单布局优化**：移动端每行显示2个字段，提高可读性
3. **头像布局改进**：移动端头像区域垂直排列，更适合小屏幕
4. **表格显示优化**：移动端表格字体和间距调整，提高可读性
5. **卡片间距优化**：移动端减少卡片间距和内边距，节省空间

## 兼容性说明 ⚠️

- 保持桌面端原有布局和功能不变
- 移动端检测基于屏幕宽度，支持横竖屏切换
- 所有原有功能在移动端均可正常使用
- 样式使用 `!important` 确保移动端样式优先级

## 后续优化建议 💡

1. 考虑添加窗口大小变化监听，实现动态响应
2. 可以进一步优化表格在移动端的显示方式（如卡片化显示）
3. 考虑添加移动端专用的导航方式（如底部导航或抽屉导航）
4. 优化表单验证提示在移动端的显示效果
