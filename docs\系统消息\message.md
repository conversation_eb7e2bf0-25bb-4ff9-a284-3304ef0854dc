# 🌟 消息系统详细开发文档 🌟

## 📋 1. 消息系统架构概述

消息系统采用了基于插件的架构设计，遵循开闭原则（对扩展开放，对修改封闭）。主要组件包括：

- **消息处理服务 (MessageHandlerService)**: 核心服务，管理和协调消息处理器
- **消息处理器 (MessageHandler)**: 处理特定类型消息的插件
- **事件总线 (EventBus)**: 提供组件间通信的机制
- **消息展示组件**: UI 组件，包括消息列表和消息详情等

消息系统架构图：

```
┌─────────────────┐      ┌───────────────────────┐
│                 │      │                       │
│  消息展示组件    │◄────►│   消息处理服务        │
│  (Message.vue)  │      │(MessageHandlerService)│
│                 │      │                       │
└─────────────────┘      └───────────┬───────────┘
                                     │
                                     ▼
                         ┌───────────────────────┐
                         │                       │
                         │    消息处理器         │
                         │  (MessageHandler)     │
                         │                       │
                         └───────────┬───────────┘
                                     │
                                     ▼
                         ┌───────────────────────┐
                         │                       │
                         │     事件总线          │
                         │    (EventBus)         │
                         │                       │
                         └───────────────────────┘
```

## 🔍 2. 核心组件和服务详解

### 2.1 消息处理服务 (MessageHandlerService)

文件路径: `src/utils/messageHandlerService.ts`

这是整个消息系统的核心服务，负责注册、管理和调用消息处理器。

```typescript
export interface MessageHandler {
  // 判断是否可以处理此类消息
  canHandle: (messageType: string) => boolean
  // 处理消息
  handle: (messageData: any) => void
  // 处理器优先级，数字越大优先级越高
  priority: number
}

class MessageHandlerService {
  private handlers: MessageHandler[] = []

  // 注册消息处理器
  registerHandler(handler: MessageHandler): void {...}

  // 获取可以处理此消息的处理器
  getHandlerForMessage(messageType: string): MessageHandler | undefined {...}

  // 处理消息
  handleMessage(messageData: any): boolean {...}
}

// 单例模式导出服务实例
export const messageHandlerService = new MessageHandlerService()
```

### 2.2 消息处理器 (MessageHandler)

消息处理器是实现 `MessageHandler` 接口的类，负责处理特定类型的消息。

例如，工作流消息处理器 (`BpmMessageHandler`):

文件路径: `src/utils/handlers/bpmMessageHandler.ts`

```typescript
export class BpmMessageHandler implements MessageHandler {
  // 优先级设置为10
  priority = 10

  // 判断是否可以处理此消息
  canHandle(messageType: string): boolean {
    return bpmClassifyEnum.some(type => String(type) === messageType)
  }

  // 处理工作流消息
  handle(messageData: any): void {
    if (messageData && messageData.gotoUrl) {
      // 使用事件总线触发打开流程详情的事件
      useEventBus().emit('open-process-detail', {
        processInstanceId: messageData.gotoUrl,
      })
    }
  }
}
```

### 2.3 事件总线 (EventBus)

文件路径: `src/utils/eventBus.ts`

事件总线提供了组件间通信的机制，使用 mitt 库实现。

```typescript
import mitt from 'mitt'

type Events = {
  'open-process-detail': { processInstanceId: string }
  [key: string]: any
}

const emitter = mitt<Events>()

export const useEventBus = () => {
  return {
    // 发送事件
    emit: <K extends keyof Events>(event: K, data: Events[K]) => {...},

    // 监听事件
    on: <K extends keyof Events>(event: K, callback: (data: Events[K]) => void) => {...},

    // 移除事件监听
    off: <K extends keyof Events>(event: K, callback?: (data: Events[K]) => void) => {...},
  }
}
```

### 2.4 消息处理器提供者 (MessageHandlerProvider)

文件路径: `src/components/message/MessageHandlerProvider.vue`

这个组件负责在应用启动时注册所有的消息处理器。

```typescript
export default defineComponent({
  name: 'MessageHandlerProvider',
  setup() {
    // 注册各种消息处理器
    const registerMessageHandlers = () => {
      // 注册工作流消息处理器
      messageHandlerService.registerHandler(new BpmMessageHandler())

      // 在这里可以注册更多的消息处理器
      // messageHandlerService.registerHandler(new AnotherMessageHandler());
    }

    onMounted(() => {
      registerMessageHandlers()
    })

    return {}
  },
})
```

## 📊 3. 消息类型和分组

消息类型和分组定义在 `src/types/sys/SysMessageTypeEnum.ts` 文件中：

```typescript
// 消息类型枚举
export enum SysMessageTypeEnum {
  // 系统消息
  SYSTEM = 'sys-1001',

  // 通知消息
  NOTICE = 'note-1002',

  // 借用消息
  AMS_BORROWING = 'ams-11001',

  // 工作流相关消息
  BPM_TASK = 'bpm-2001',
  BPM_TODO = 'bpm-2002',
  BPM_PASS = 'bpm-2003',
  BPM_NOT_PASS = 'bpm-2004',
  BPM_RECALL = 'bpm-2005',
  BPM_CANCEL = 'bpm-2006',
}

// 消息类型信息
export interface SysMessageTypeInfo {
  code: string
  message: string
  url: string
  icon: any
  color: string
  group: string // 消息分组
}

// 工作流消息类型
export const bpmClassifyEnum = [
  SysMessageTypeEnum.BPM_TASK,
  SysMessageTypeEnum.BPM_TODO,
  SysMessageTypeEnum.BPM_PASS,
  SysMessageTypeEnum.BPM_NOT_PASS,
  SysMessageTypeEnum.BPM_RECALL,
  SysMessageTypeEnum.BPM_CANCEL,
]
```

## 🔄 4. 消息流程

1. **消息显示**:

   - 用户点击顶部栏的消息图标
   - TopBar 组件的 `messageMouseOver` 方法被调用
   - 系统获取消息列表并显示消息窗口

2. **消息点击处理**:

   - 用户点击某条消息
   - `message.vue` 组件的 `handleMessageClick` 方法被调用
   - 该方法调用 `messageHandlerService.handleMessage()` 处理消息
   - 消息处理服务根据消息类型找到合适的处理器
   - 处理器处理消息（例如，打开工作流程详情）
   - 消息窗口关闭

3. **监听处理结果**:
   - App.vue 监听 `open-process-detail` 事件
   - 事件触发时，App.vue 显示流程详情弹窗

## 🧩 5. 如何新增消息处理器

### 步骤 1: 创建新的消息处理器类

例如，创建资产消息处理器 `src/utils/handlers/assetMessageHandler.ts`：

```typescript
import { MessageHandler } from '@/utils/messageHandlerService'
import { SysMessageTypeEnum } from '@/types/sys/SysMessageTypeEnum'
import { useRouter } from 'vue-router'

export class AssetMessageHandler implements MessageHandler {
  priority = 8 // 优先级设置为8，低于工作流处理器

  private router = useRouter()

  canHandle(messageType: string): boolean {
    return messageType === SysMessageTypeEnum.AMS_BORROWING
  }

  handle(messageData: any): void {
    if (messageData && messageData.gotoUrl) {
      // 跳转到资产详情页面
      this.router.push({
        path: '/asset/detail',
        query: { id: messageData.gotoUrl },
      })
    }
  }
}
```

### 步骤 2: 在 MessageHandlerProvider 中注册处理器

修改 `src/components/message/MessageHandlerProvider.vue`：

```typescript
import { AssetMessageHandler } from '@/utils/handlers/assetMessageHandler'

const registerMessageHandlers = () => {
  // 注册工作流消息处理器
  messageHandlerService.registerHandler(new BpmMessageHandler())

  // 注册资产消息处理器
  messageHandlerService.registerHandler(new AssetMessageHandler())
}
```

### 步骤 3: 添加新的事件类型（如果需要）

如果新的消息处理需要新的事件类型，修改 `src/utils/eventBus.ts`：

```typescript
type Events = {
  'open-process-detail': { processInstanceId: string }
  'open-asset-detail': { assetId: string } // 新增事件类型
  [key: string]: any
}
```

### 步骤 4: 监听新事件（如果需要）

在相应的组件中监听新事件，例如：

```typescript
import { useEventBus } from '@/utils/eventBus'

setup() {
  // 监听打开资产详情事件
  const unsubscribe = useEventBus().on('open-asset-detail', data => {
    // 处理事件
  })

  // 组件卸载时移除事件监听
  onUnmounted(() => {
    unsubscribe()
  })
}
```

## 📝 6. 最佳实践

1. **处理器的职责单一**: 每个消息处理器只负责处理一种或一组相关的消息类型
2. **优先级合理设置**: 通过 `priority` 属性合理设置处理器的优先级
3. **类型安全**: 尽量使用类型系统来保证消息处理的类型安全
4. **错误处理**: 在处理器中加入适当的错误处理机制
5. **文档完善**: 为每个处理器和事件类型添加清晰的注释和文档

## 🚀 7. 使用示例

### 7.1 在消息组件中使用消息处理服务

```typescript
// 处理消息点击
const handleMessageClick = (messageData: MessageItem) => {
  // 使用消息处理服务处理消息
  const handled = messageHandlerService.handleMessage(messageData)

  if (!handled) {
    // 如果没有处理器处理此消息，提示用户
    message.info('暂不支持此类消息的处理')
  }

  // 关闭消息窗口
  emit('close')
}
```

### 7.2 监听处理结果

```typescript
// 监听打开流程详情事件
const unsubscribe = useEventBus().on('open-process-detail', data => {
  processInstanceId.value = data.processInstanceId
  showProcessDetail.value = true
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  unsubscribe()
})
```

## 📑 8. 故障排查

遇到消息处理问题时可以检查以下几点:

1. 检查消息类型是否正确
2. 确认消息处理器是否已注册
3. 检查消息处理器的 `canHandle` 方法是否返回 `true`
4. 检查消息数据格式是否符合处理器的预期
5. 查看控制台是否有错误日志

---

🎉 通过以上步骤和示例，开发人员可以了解消息系统的架构，并能轻松扩展系统以处理新类型的消息。
