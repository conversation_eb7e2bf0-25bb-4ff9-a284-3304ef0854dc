---
description: 
globs: 
alwaysApply: false
---
# 项目结构与文件组织

## 📁 模块组织结构
- 业务模块按照功能域进行组织，如 `ams`（资产管理系统）
- 模块下按具体业务功能分类，如 `amsAssetAdjustment/outBorrowing`
- 页面组件默认使用 `index.vue` 作为入口文件

## 📊 API 文件
- API 相关方法通常封装在与组件同级的 Web 文件中，如 `BorrowFromOtherHospitalsWeb.js`
- API 命名规范：
  - 查询列表：`pageQueryXxx`
  - 新增：`addXxx`
  - 修改：`updateXxx`
  - 删除：`deleteXxx`

## 🪝 Hooks 文件
- hooks 文件存放在同级的 `hooks` 目录下
- 按功能拆分不同的 hooks 文件，如 `useBorrow.js`, `useReturn.js`
- 在 `.vue` 文件中通过 import 引入并使用这些 hooks

## 🧩 组件引用规范
- 通用组件从 `@/components/common` 引入
- 业务组件从对应业务目录引入
- UI 组件库从 `naive-ui` 直接引入
- 工具类型从 `@/types` 目录引入

## 📋 类型定义
- 使用 TypeScript 定义接口类型，如 `CRUDColumnInterface`
- 枚举类型从 `@/types/enums/enums` 引入
- 组件类型从 `@/types/comps` 引入

## 🔄 状态管理
- Store 文件放在 `@/store` 目录下
- 使用 `useXxxStore` 的格式命名 store hooks
- 主要的 store 包括 `sysStore`(系统配置)和 `userStore`(用户信息)

## 💠 图标处理
- 图标组件从 `@vicons` 库引入，如 `CloseOutline`
- 自定义图标可能从 `@/types/common/jcomponents` 的 Icon 组件中引入

## 🔧 工具和配置
- 全局工具类从 `@/types/common/jglobal` 引入
- 通用 API 从 `@/api/common/common` 引入，如 `download`
