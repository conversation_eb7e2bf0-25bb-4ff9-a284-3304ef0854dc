<!-- n-tabs data-table 性能优化测试示例 -->
<template>
  <div class="test-container">
    <h2>n-tabs data-table 性能优化测试</h2>
    
    <!-- 使用优化后的 j-crud 组件 -->
    <j-crud
      ref="crudRef"
      :columns="columns"
      :tabs="tabs"
      :query-method="queryMethod"
      :add-method="addMethod"
      :update-method="updateMethod"
      :del-method="delMethod"
      name="测试数据"
      :default-check-tab="'tab1'"
    >
      <!-- 自定义按钮 -->
      <template #extendButtons>
        <n-button type="primary" @click="switchTab">切换到 Tab2</n-button>
        <n-button type="info" @click="addTestData">添加测试数据</n-button>
      </template>
    </j-crud>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { NButton } from 'naive-ui'

// 表格列配置
const columns = ref([
  {
    title: 'ID',
    key: 'id',
    width: 80,
  },
  {
    title: '姓名',
    key: 'name',
    width: 120,
  },
  {
    title: '年龄',
    key: 'age',
    width: 80,
  },
  {
    title: '部门',
    key: 'department',
    width: 120,
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    dictType: 'user_status',
    tagRender: true,
  },
])

// Tab 配置
const tabs = ref([
  {
    name: 'tab1',
    tab: '活跃用户',
    data: [],
    useBadge: true,
    badge: 0,
  },
  {
    name: 'tab2', 
    tab: '非活跃用户',
    data: [],
    useBadge: true,
    badge: 0,
  },
  {
    name: 'tab3',
    tab: '已删除用户',
    data: [],
    useBadge: true,
    badge: 0,
  },
])

const crudRef = ref()

// 模拟查询方法
const queryMethod = async (params: any) => {
  console.log('🔍 查询参数:', params)
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // 根据当前 tab 返回不同数据
  const currentTab = tabs.value.find(tab => tab.name === crudRef.value?.curTabName)
  
  let mockData = []
  if (currentTab?.name === 'tab1') {
    mockData = generateMockData('active', 50)
  } else if (currentTab?.name === 'tab2') {
    mockData = generateMockData('inactive', 30)
  } else if (currentTab?.name === 'tab3') {
    mockData = generateMockData('deleted', 10)
  }
  
  // 更新 tab 数据和徽章
  if (currentTab) {
    currentTab.data = mockData
    currentTab.badge = mockData.length
  }
  
  return {
    code: 200,
    data: {
      records: mockData,
      total: mockData.length,
      pages: 1,
    },
  }
}

// 生成模拟数据
const generateMockData = (type: string, count: number) => {
  const data = []
  for (let i = 1; i <= count; i++) {
    data.push({
      id: `${type}_${i}`,
      name: `用户${i}`,
      age: 20 + Math.floor(Math.random() * 40),
      department: ['技术部', '产品部', '运营部', '市场部'][Math.floor(Math.random() * 4)],
      status: type === 'active' ? 1 : type === 'inactive' ? 0 : -1,
    })
  }
  return data
}

// 模拟新增方法
const addMethod = async (data: any) => {
  console.log('➕ 新增数据:', data)
  await new Promise(resolve => setTimeout(resolve, 300))
  return { code: 200, message: '新增成功' }
}

// 模拟更新方法
const updateMethod = async (data: any) => {
  console.log('✏️ 更新数据:', data)
  await new Promise(resolve => setTimeout(resolve, 300))
  return { code: 200, message: '更新成功' }
}

// 模拟删除方法
const delMethod = async (data: any) => {
  console.log('🗑️ 删除数据:', data)
  await new Promise(resolve => setTimeout(resolve, 300))
  return { code: 200, message: '删除成功' }
}

// 切换 Tab 测试
const switchTab = () => {
  if (crudRef.value) {
    crudRef.value.curTabName = 'tab2'
    crudRef.value.tabChange('tab2', true, true)
  }
}

// 添加测试数据
const addTestData = () => {
  const currentTab = tabs.value.find(tab => tab.name === crudRef.value?.curTabName)
  if (currentTab) {
    const newData = {
      id: `new_${Date.now()}`,
      name: `新用户${Date.now()}`,
      age: 25,
      department: '技术部',
      status: 1,
    }
    currentTab.data.push(newData)
    currentTab.badge = currentTab.data.length
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}
</style>
