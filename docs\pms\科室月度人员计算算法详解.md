# 科室月度人员计算算法详解 🧮

## 核心原理 🎯

**计算人数 = 基础人数 × 有效工作系数**

医院科室人员计算采用精确的数学算法，确保每个人员的贡献度都能被准确统计。

## 计算方法分类 📊

### 1. 固定系数类（直接乘系数）

**适用场景**: 稳定在岗人员和特殊角色人员

```java
// 算法实现
public BigDecimal calculateFixed(int staffCount, BigDecimal coefficient) {
    return new BigDecimal(staffCount).multiply(coefficient);
}
```

**具体类型**:
- **本月领取100%人员**: `人数 × 1.0`
  - 包含主任、护士长、退休返聘人员
  - 不含进修、休假人员
- **离任主任**: `人数 × 0.3` 
  - 体现离任主任的部分贡献
- **正常在岗**: `人数 × 1.0`
  - 辅助护士、临时人员的基础贡献

### 2. 按天数比例类（请假扣减）

**核心公式**: `人数 × (实际工作天数 ÷ 月总天数)`

```java
// 算法实现
public BigDecimal calculateByDays(int staffCount, int workDays, int totalDays) {
    if (workDays <= 0) {
        return BigDecimal.ZERO; // 整月请假
    }
    return new BigDecimal(staffCount)
        .multiply(new BigDecimal(workDays))
        .divide(new BigDecimal(totalDays), 4, RoundingMode.HALF_UP);
}
```

**个性化填报支持**:
- ✅ **支持单独填写每个人的请假情况**
- ✅ **例如**: 小红请假20天，小李请假10天，小王整月请假

**适用情况**:
- **进修**: `人数 × (30 - 进修天数) ÷ 30`
- **对口支援**: `人数 × (30 - 支援天数) ÷ 30`
- **病假**: `人数 × (30 - 病假天数) ÷ 30`
- **产假/哺乳假**: `人数 × (30 - 假期天数) ÷ 30`
- **其他假期**: `人数 × (30 - 假期天数) ÷ 30`
- **借调抽调**: `人数 × (30 - 借调天数) ÷ 30`

**特殊情况处理**:
- 如果整月请假（工作天数 = 0），计算结果 = 0
- 支持小数点后4位精度

### 3. 按比例系数类

**核心公式**: `人数 × 比例系数`

```java
// 算法实现  
public BigDecimal calculateByRatio(int staffCount, BigDecimal ratio) {
    return new BigDecimal(staffCount)
        .multiply(ratio)
        .divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
}
```

**适用场景**:
- **辅助护士**: 根据每人的工作比例计算
- **临时聘用人员**: 根据合同约定比例计算

### 4. 组合计算类（比例+天数）

**核心公式**: `人数 × 比例 × (工作天数 ÷ 月总天数)`

```java
// 算法实现
public BigDecimal calculateCombined(int staffCount, BigDecimal ratio, int workDays, int totalDays) {
    if (workDays <= 0) {
        return BigDecimal.ZERO;
    }
    return new BigDecimal(staffCount)
        .multiply(ratio).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP)
        .multiply(new BigDecimal(workDays))
        .divide(new BigDecimal(totalDays), 4, RoundingMode.HALF_UP);
}
```

## 实际应用示例 💡

### 示例1: 消化内科11月人员计算

**人员构成**:
- 正常医生: 10人
- 小红进修: 20天  
- 小李病假: 10天
- 小王产假: 整月
- 离任主任: 1人
- 辅助护士张三: 0.8比例
- 辅助护士李四: 0.6比例

**计算过程**:
```
1. 正常医生: 10 × 1.0 = 10.0000人
2. 小红进修: 1 × (30-20)/30 = 0.3333人
3. 小李病假: 1 × (30-10)/30 = 0.6667人  
4. 小王产假: 1 × 0/30 = 0.0000人
5. 离任主任: 1 × 0.3 = 0.3000人
6. 辅助护士张三: 1 × 0.8 = 0.8000人
7. 辅助护士李四: 1 × 0.6 = 0.6000人

总计算人数 = 10.0000 + 0.3333 + 0.6667 + 0.0000 + 0.3000 + 0.8000 + 0.6000 = 12.8000人
```

### 示例2: 急诊科复杂情况

**人员构成**:
- 本月100%医生: 8人
- 护士长: 1人  
- 小刘对口支援: 15天
- 小陈其他假期: 5天
- 辅助护士A: 0.9比例，工作25天
- 临时人员B: 0.7比例，工作20天

**计算过程**:
```
1. 本月100%医生: 8 × 1.0 = 8.0000人
2. 护士长: 1 × 1.0 = 1.0000人
3. 小刘对口支援: 1 × (30-15)/30 = 0.5000人
4. 小陈其他假期: 1 × (30-5)/30 = 0.8333人
5. 辅助护士A: 1 × 0.9 × 25/30 = 0.7500人
6. 临时人员B: 1 × 0.7 × 20/30 = 0.4667人

总计算人数 = 8.0000 + 1.0000 + 0.5000 + 0.8333 + 0.7500 + 0.4667 = 11.5500人
```

## 系统汇总逻辑 📝

### 最终汇总公式

```
各科室最终计算人数 = Σ正贡献项 - Σ负贡献项

其中:
正贡献项 = 本月100%人员 + 离任主任 + 辅助护士 + 临时人员 + ...
负贡献项 = 进修扣减 + 病假扣减 + 产假扣减 + 借调扣减 + ...
```

### 数据库存储结构

```sql
-- 每条记录存储单个人员类型的计算结果
INSERT INTO pms_monthly_dept_staff_report (
    pms_dept_name,           -- 科室名称
    type_code,               -- 人员类型代码  
    staff_count,             -- 基础人数
    work_days,               -- 工作天数
    percentage,              -- 比例系数
    calculated_count,        -- 计算结果
    category,                -- positive/negative
    calculation_method       -- fixed/ratio_days/percentage/percentage_days
);
```

## 前端交互设计 🖥️

### 个性化填报界面

```typescript
// 支持每个人员的详细信息填报
interface PersonDetail {
    name: string;          // 姓名
    empCode: string;       // 员工号  
    workDays: number;      // 工作天数
    leaveType: string;     // 请假类型
    leaveDays: number;     // 请假天数
    ratio?: number;        // 个人比例
    remark: string;        // 备注说明
}

interface StaffTypeReport {
    typeCode: string;           // 人员类型
    totalCount: number;         // 总人数
    personDetails: PersonDetail[];  // 每个人的详情
    calculatedCount: number;    // 计算结果
}
```

### 实时计算反馈

- ✅ **输入验证**: 天数不能超过月总天数
- ✅ **实时计算**: 输入后立即显示计算结果  
- ✅ **数据检查**: 异常数据高亮提示
- ✅ **历史对比**: 与上月数据对比分析

## 质量控制 🔍

### 数据验证规则

1. **基础验证**:
   - 人数必须 ≥ 0
   - 工作天数 0 ≤ 天数 ≤ 月总天数
   - 比例 0% ≤ 比例 ≤ 100%

2. **逻辑验证**:
   - 请假天数 + 工作天数 = 月总天数
   - 同一人不能同时在多个类型中
   - 特殊角色人数合理性检查

3. **异常告警**:
   - 人数变化超过20%预警
   - 请假人数异常增加提醒
   - 计算结果为0的情况确认

### 审核流程

```mermaid
graph LR
    A[科室填报] --> B[系统计算]
    B --> C[数据验证]
    C --> D{验证通过?}
    D -->|否| E[错误提示]
    E --> A
    D -->|是| F[提交保存]
    F --> G[管理员审核]
    G --> H[最终确认]
```

## 总结 🎊

新的人员计算算法具备以下特点:

- **🎯 精确性**: 支持到小数点后4位的精确计算
- **🔧 灵活性**: 支持多种计算方法和个性化填报
- **👥 人性化**: 可以单独记录每个人的具体情况  
- **🔍 可追溯**: 完整保留计算过程和原始数据
- **⚡ 实时性**: 输入即计算，提供即时反馈
- **🛡️ 可靠性**: 多层验证确保数据质量

这套算法完全满足医院人事管理的复杂需求，为科室绩效评价提供了准确的数据基础！ 