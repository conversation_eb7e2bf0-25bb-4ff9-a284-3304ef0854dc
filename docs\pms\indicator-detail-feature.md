# 绩效指标详情查看功能

## 📋 功能概述

为绩效管理系统的月度绩效页面添加了指标详情查看功能，用户可以点击绩效项目名称旁的眼睛图标查看该指标的详细信息。

## 🎯 功能特性

### 1. 视觉标识
- **高亮显示**：有详情数据的指标名称以蓝色高亮显示
- **眼睛图标**：在指标名称旁显示可点击的眼睛图标 👁️
- **悬停效果**：图标支持悬停变色效果

### 2. 详情展示
- **弹窗形式**：使用模态框展示详情信息
- **多标签页**：支持多个数据表的标签页切换
- **动态表格**：根据指标类型动态渲染不同的表格结构
- **数据分页**：大量数据自动分页显示

### 3. 支持的指标
目前支持以下七个指标的详情查看（使用前端Mock数据）：

#### 医疗指标
##### CMI值奖励
- **汇总数据**：科室CMI值、全院平均CMI、差值计算、奖励金额等
- **病例明细**：包含住院号、患者信息、诊断、DRG分组、CMI值、复杂度等级

##### 出院病例数
- **日统计**：每日出院人次、出院类型分布、累计统计
- **数据来源**：HIS住院系统

##### 出院3天内再入同科患者
- **统计汇总**：再入院率、达标情况、质控标准对比
- **病例明细**：具体再入院病例信息、间隔天数、再入院原因

#### 人员指标
##### 医生人数
- **人员统计**：按职级分类的在岗、请假、进修人数统计
- **人员明细**：医生详细信息、联系方式、工作状态

##### 护士人数
- **护理人员统计**：按职级分类的护理人员配置情况
- **排班情况**：各班次人员安排、缺岗情况统计

##### 进修人数
- **进修明细**：进修人员信息、进修医院、进修内容、时间安排

##### 请假人数
- **请假统计**：按请假类型分类的统计数据
- **请假明细**：具体请假人员信息、请假原因、审批状态

## 🔧 技术实现

### 文件结构
```
src/views/modules/pms/pmsCalc/monthlyPerformance/
├── comp/
│   ├── PerformanceDataTable.vue          # 主表格组件（已修改）
│   ├── SideInformation.vue               # 指标详情信息页面（已修改）
│   └── IndicatorDetailModal.vue          # 指标详情弹窗组件（新增）
├── hooks/
│   └── useIndicatorDetailMock.ts         # Mock数据管理Hook（新增）
└── index.vue                             # 主页面（无需修改）
```

### 核心组件

#### SideInformation.vue
- 指标详情信息页面组件
- 展示所有支持详情查看的指标列表
- 提供统一的指标详情入口
- 支持指标分类和图标展示
- 复用IndicatorDetailModal弹窗组件

#### IndicatorDetailModal.vue
- 指标详情弹窗组件
- 支持多标签页展示
- 动态渲染表格列和数据
- 响应式设计，适配不同屏幕尺寸

#### useIndicatorDetailMock.ts
- Mock数据管理Hook
- 提供指标详情数据获取方法
- 支持检查指标是否有详情数据
- 易于扩展新的指标类型
- 包含7个指标的完整Mock数据

### 关键功能点

#### 1. 指标识别
```typescript
// 检查指标是否有详情数据
const hasDetail = hasIndicatorDetail(row.itemName)
```

#### 2. 视觉高亮
```typescript
// 有详情的指标使用蓝色高亮样式
const highlightStyle = {
  color: '#1890ff',
  fontSize: '14px',
  fontWeight: '500'
}
```

#### 3. 图标交互
```typescript
// 眼睛图标点击处理
h(NIcon, {
  size: 16,
  style: { color: '#1890ff', cursor: 'pointer' },
  onClick: (e: Event) => {
    e.stopPropagation()
    handleIndicatorDetailClick(row)
  }
}, () => h(EyeOutline))
```

## 🎨 UI设计

### 弹窗布局
- **头部**：指标名称和基本信息卡片
- **主体**：数据表格区域（支持标签页切换）
- **底部**：操作按钮（导出、关闭）

### 样式特点
- 渐变背景的信息卡片
- 清晰的表格边框和条纹
- 响应式网格布局
- 统一的色彩主题

## 🚀 使用方法

### 方式一：从绩效数据表格查看
1. **查看指标详情**
   - 在绩效数据表格中找到有蓝色高亮的指标名称
   - 点击指标名称旁的眼睛图标 👁️
   - 在弹出的详情窗口中查看数据

### 方式二：从指标详情信息页面查看
1. **进入指标详情信息页面**
   - 点击顶部标签页中的"指标详情信息"
   - 查看所有支持详情查看的指标列表

2. **查看指标详情**
   - 在指标列表中找到需要查看的指标
   - 点击对应行的"查看详情"按钮
   - 在弹出的详情窗口中查看数据

### 通用操作
1. **切换数据视图**
   - 如果指标有多个数据表，使用顶部标签页切换
   - 每个标签页显示不同维度的数据

2. **数据导出**
   - 点击弹窗底部的"导出数据"按钮
   - 目前显示开发中提示（功能预留）

## 🔄 扩展指南

### 添加新指标
1. 在 `useIndicatorDetailMock.ts` 中的 `indicatorMockData` 对象添加新指标配置
2. 定义指标的列结构和数据
3. 支持单表格或多标签页模式

### 示例配置
```typescript
'新指标名称': {
  dataSource: '数据来源系统',
  columns: [
    { title: '列名', key: 'key', width: 120 },
    // ... 更多列定义
  ],
  data: [
    { key: 'value1' },
    // ... 数据行
  ]
}
```

## 📝 注意事项

1. **性能优化**：大量数据自动启用分页功能
2. **错误处理**：包含完整的错误边界和加载状态
3. **响应式**：支持不同屏幕尺寸的适配
4. **可访问性**：支持键盘导航和屏幕阅读器

## 🎉 总结

该功能为绩效管理系统提供了更丰富的数据查看体验，通过两种不同的访问方式和直观的视觉标识，帮助用户更好地理解和分析绩效指标数据：

### ✅ 已实现功能
1. **双重访问方式**：
   - 绩效数据表格中的眼睛图标快速查看
   - 指标详情信息页面的统一管理入口

2. **丰富的Mock数据**：
   - 7个不同类型的指标详情
   - 医疗指标和人员指标分类
   - 多标签页和单表格两种展示模式

3. **优秀的用户体验**：
   - 统一的弹窗组件复用
   - 美观的图标和颜色标识
   - 响应式设计和动画效果

4. **良好的代码架构**：
   - Hook模式的数据管理
   - 组件复用和功能解耦
   - 易于扩展的设计模式

Mock数据的设计也为后续接入真实API提供了良好的基础架构，整个功能模块具有很好的可维护性和扩展性。🚀
