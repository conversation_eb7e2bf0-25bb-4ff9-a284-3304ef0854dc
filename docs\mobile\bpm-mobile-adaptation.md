# BPM流程详情移动端适配说明 📱

本文档说明了BPM流程任务详情页面的移动端适配实现，包括全屏显示、流程图放大查看、审批操作优化等功能。

## 🎯 适配概览

### 核心改进
- ✅ **流程详情弹窗全屏显示** - 移动端自动切换为全屏模式
- ✅ **流程图放大查看功能** - 独立弹窗支持旋转和缩放，置顶显示
- ✅ **审批操作移动端优化** - 表单布局、按钮排列、下一任务显示
- ✅ **流程记录移动端适配** - 时间线布局、按钮组织、卡片设计
- ✅ **响应式布局优化** - 移动端友好的表单和按钮设计
- ✅ **企业级简洁设计** - 保持与PC端样式一致性

## 🔧 技术实现

### 1. 流程详情弹窗适配 (`processDetailModal.vue`)

#### 移动端全屏显示
```vue
<template>
  <n-modal 
    v-model:show="show" 
    :style="modalStyle"
    :mask-closable="!isMobileDevice"
  >
    <n-card :style="cardStyle">
      <!-- 内容 -->
    </n-card>
  </n-modal>
</template>

<script setup>
// 移动端检测
const isMobileDevice = computed(() => checkMobileDevice())

// 模态框样式
const modalStyle = computed(() => {
  if (isMobileDevice.value) {
    return {
      width: '100vw',
      height: '100vh',
      margin: '0',
      maxWidth: 'none',
      maxHeight: 'none',
      borderRadius: '0',
    }
  }
  return {}
})
</script>
```

### 2. 流程图放大查看组件 (`ProcessDiagramModal.vue`)

#### 核心功能
- **全屏显示** - 移动端占满整个屏幕
- **旋转功能** - 支持90度旋转查看
- **缩放重置** - 一键重置流程图缩放
- **触摸优化** - 适配移动端触摸操作

#### 关键特性
```vue
<template>
  <n-modal v-model:show="show" :style="modalStyle">
    <n-card :style="cardStyle">
      <template #header>
        <div class="flex items-center justify-between">
          <span>流程图详情</span>
          
          <!-- 移动端工具栏 -->
          <div v-if="isMobileDevice" class="flex gap-2">
            <n-button @click="toggleRotation">旋转</n-button>
            <n-button @click="resetZoom">重置</n-button>
          </div>
        </div>
      </template>
      
      <!-- 流程图容器 -->
      <div :class="containerClasses" :style="containerStyle">
        <MyProcessViewer
          :activityData="activityData"
          :processInstanceData="processInstance"
          :taskData="tasks"
          :value="bpmnXml"
        />
      </div>
    </n-card>
  </n-modal>
</template>
```

### 3. 主详情页面优化 (`index.vue`)

#### 移动端布局改进
```vue
<template>
  <div class="tabs-container" :class="{ 'mobile-layout': isMobileDevice }">
    <n-tabs
      :size="isMobileDevice ? 'large' : 'medium'"
      v-model:value="activeTab"
    >
      <!-- 审批操作Tab -->
      <n-tab-pane name="approval" tab="审批操作">
        <!-- 移动端优化的审批表单 -->
        <el-form
          :label-width="isMobileDevice ? '100%' : '136px'"
          :label-position="isMobileDevice ? 'top' : 'right'"
          :class="{ 'mobile-form': isMobileDevice }"
        >
          <!-- 表单内容 -->
        </el-form>

        <!-- 移动端网格布局的操作按钮 -->
        <div :class="isMobileDevice ? 'mobile-action-buttons' : 'desktop-action-buttons'">
          <!-- 按钮组 -->
        </div>

        <!-- 移动端卡片式下一任务显示 -->
        <div v-if="isMobileDevice" class="mobile-next-tasks-list">
          <div v-for="task in nextTasks" class="mobile-task-item">
            <!-- 任务信息 -->
          </div>
        </div>
      </n-tab-pane>

      <!-- 流程记录Tab -->
      <n-tab-pane name="history" tab="流程记录 流程图">
        <!-- 移动端流程图放大查看按钮 - 置顶 -->
        <div v-if="isMobileDevice && bpmnXml" class="mobile-diagram-actions-top">
          <n-button type="primary" size="large" @click="showDiagramModal = true">
            放大查看流程图
          </n-button>
        </div>

        <!-- 流程记录时间线 -->
        <ProcessInstanceTaskList :class="{ 'mobile-task-list': isMobileDevice }" />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>
```

#### 审批操作移动端优化
- **表单布局**: 标签置顶，全宽度输入框
- **按钮排列**: 2x2网格布局，触摸友好
- **下一任务**: 卡片式显示替代表格
- **快捷标签**: 优化间距和大小

### 4. 流程记录组件优化 (`ProcessInstanceTaskList.vue`)

#### 移动端时间线适配
```vue
<template>
  <el-card :class="['box-card', { 'mobile-task-list-card': isMobileDevice }]">
    <template #header>
      <div :class="{ 'mobile-header': isMobileDevice }">
        审批记录
      </div>
    </template>

    <!-- 移动端时间线容器 -->
    <div :class="isMobileDevice ? 'mobile-timeline-container' : 'desktop-timeline-container'">
      <el-timeline :class="{ 'mobile-timeline': isMobileDevice }">
        <el-timeline-item
          :size="isMobileDevice ? 'normal' : 'large'"
          :class="{ 'mobile-timeline-item': isMobileDevice }"
        >
          <!-- 移动端任务头部 -->
          <div :class="{ 'mobile-task-header': isMobileDevice }">
            <div class="task-title">
              审批任务：{{ item.name }}
              <dict-tag :value="item.status" />
            </div>

            <!-- 移动端按钮组 -->
            <div v-if="isMobileDevice" class="mobile-task-buttons">
              <el-button v-if="item.children" type="primary" plain>子任务</el-button>
              <el-button v-if="item.formId > 0" type="info" plain>查看表单</el-button>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-card>
</template>
```

#### 流程记录移动端特性
- **时间线布局**: 紧凑的节点和间距
- **按钮组织**: 垂直排列，类型区分
- **容器适配**: 移除偏移，全宽显示
- **卡片设计**: 圆角和阴影优化

## 🎨 样式设计

### 移动端优化样式
```css
/* 移动端审批卡片优化 */
.mobile-approval-card {
  margin: 12px 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-form {
  padding: 0;
}

.mobile-form :deep(.el-form-item__label) {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

/* 移动端操作按钮网格布局 */
.mobile-action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin: 20px 0;
  padding: 0;
}

/* 移动端下一任务卡片 */
.mobile-task-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 8px;
  margin-bottom: 12px;
}

.mobile-task-item .task-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12px;
}

/* 移动端流程图放大按钮 - 置顶样式 */
.mobile-diagram-actions-top {
  margin: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.mobile-diagram-actions-top .n-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

/* 移动端时间线样式 */
.mobile-timeline-container {
  padding: 0 16px;
}

.mobile-timeline-item :deep(.el-timeline-item__node) {
  width: 16px !important;
  height: 16px !important;
  margin-left: -8px;
}

.mobile-task-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.mobile-task-buttons .el-button {
  min-height: 36px;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 8px;
}

/* 移动端表单优化 */
@media (max-width: 768px) {
  :deep(.el-input__inner) {
    font-size: 16px;
    padding: 14px 16px;
    min-height: 48px;
    border-radius: 8px;
  }

  :deep(.el-textarea__inner) {
    font-size: 16px;
    padding: 14px 16px;
    min-height: 100px;
    border-radius: 8px;
  }

  :deep(.el-button) {
    min-height: 48px;
    font-size: 15px;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 500;
  }

  :deep(.n-button) {
    min-height: 48px;
    font-size: 15px;
    border-radius: 8px;
    font-weight: 500;
  }
}
```

## 📱 用户体验

### 移动端交互优化
1. **触摸友好** - 所有按钮最小48px高度，适合手指操作
2. **全屏体验** - 弹窗占满整个屏幕，沉浸式操作
3. **流程图查看** - 置顶放大按钮，支持纵向和旋转显示
4. **审批操作** - 网格布局按钮，卡片式下一任务显示
5. **流程记录** - 紧凑时间线，垂直按钮组织
6. **企业级设计** - 简洁专业的视觉风格，渐变色点缀

### 移动端特色功能
- **流程图置顶**: 在流程记录tab顶部显示放大查看按钮
- **纵向优化**: 流程图弹窗默认纵向显示，适合手机长边
- **卡片式任务**: 下一审批任务使用卡片替代表格显示
- **网格按钮**: 审批操作按钮采用2x2网格布局
- **表单优化**: 标签置顶，输入框全宽，触摸友好

### 响应式断点
- **移动端**: < 768px - 全屏弹窗，大尺寸组件，卡片布局
- **平板**: 768px - 1024px - 中等尺寸组件，混合布局
- **桌面端**: > 1024px - 原有PC端样式，表格布局

## 🔍 功能特性

### 1. 自动设备检测
```typescript
import { isMobileDevice as checkMobileDevice } from '@/utils/device'

const isMobileDevice = computed(() => checkMobileDevice())
```

### 2. 流程图放大查看
- 点击"放大查看流程图"按钮
- 独立弹窗全屏显示
- 支持旋转和缩放操作
- 保持流程图数据同步

### 3. 全屏弹窗体验
- 移动端自动全屏显示
- 禁用遮罩点击关闭
- 优化关闭按钮位置

## 📋 文件结构

```
src/views/modules/bpm/processInstance/detail/
├── processDetailModal.vue          # 流程详情弹窗（已适配全屏）
├── index.vue                       # 主详情页面（已适配审批操作和流程记录）
├── ProcessInstanceBpmnViewer.vue   # 流程图查看器
├── ProcessInstanceTaskList.vue     # 流程记录组件（已适配移动端）
└── components/
    └── ProcessDiagramModal.vue     # 流程图放大查看弹窗（新增纵向优化）
```

## 🚀 使用方法

### 1. 流程详情查看
```vue
<ProcessDetailModal
  v-model:show="showModal"
  :process-instance-id="processId"
  :other-props="otherProps"
/>
```

### 2. 流程图放大查看
```vue
<ProcessDiagramModal
  v-model:show="showDiagramModal"
  :activity-data="activityList"
  :process-instance="processInstance"
  :tasks="tasks"
  :bpmn-xml="bpmnXml"
/>
```

## ✅ 测试要点

### 移动端测试
1. **弹窗显示** - 确认全屏显示效果
2. **审批操作** - 测试表单布局、按钮网格、下一任务卡片
3. **流程记录** - 验证时间线布局、按钮组织
4. **流程图查看** - 测试置顶按钮、放大查看、纵向显示
5. **旋转功能** - 验证流程图旋转效果
6. **触摸操作** - 确认按钮大小和响应（最小48px）
7. **样式一致性** - 对比PC端保持企业级风格

### 功能测试
- **审批表单**: 标签置顶、输入框全宽、快捷标签
- **操作按钮**: 网格布局、触摸友好、视觉反馈
- **下一任务**: 卡片显示、信息完整、状态标签
- **流程记录**: 时间线紧凑、按钮垂直、附件显示
- **流程图**: 置顶按钮、纵向显示、旋转功能

### 兼容性测试
- iOS Safari (iPhone 12/13/14/15系列)
- Android Chrome (各主流品牌)
- 微信内置浏览器
- 各种屏幕尺寸 (375px - 428px宽度)

## 🎯 设计原则

1. **保持一致性** - 与PC端样式风格统一
2. **企业级简洁** - 避免花哨装饰，专注功能
3. **触摸友好** - 符合移动端交互规范
4. **性能优化** - 流畅的动画和响应速度
