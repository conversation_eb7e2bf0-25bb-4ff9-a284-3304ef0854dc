<template>
  <div class="med-container login-container">
    <n-config-provider :theme-overrides="themeOverrides">
      <div class="login-panel">
        <!-- v-if="!isMobile || isTablet" -->
        <div class="login-panel-left"></div>
        <div class="login-panel-right">
          <img src="@/assets/images/login/form-logo.png" class="form-logo" />
          <div class="form-info">
            <n-form ref="formRef" :model="form" :rules="rules" label-placement="left">
              <n-form-item path="username" label="">
                <n-input
                  placeholder="请输入用户名"
                  v-model:value="form.username"
                  :input-props="{ autocomplete: 'username' }"
                >
                  <template #prefix>
                    <n-icon size="24" color="#0e7a0d">
                      <img src="@/assets/images/login/form-logon-user.png" />
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>
              <n-form-item path="password" label="">
                <n-input
                  placeholder="请输入密码"
                  type="password"
                  :input-props="{ autocomplete: 'current-password' }"
                  show-password-on="click"
                  v-model:value="form.password"
                >
                <!-- 原生密码查看按钮 -->

                  <template #prefix>
                    <n-icon size="24" color="#0e7a0d">
                      <img src="@/assets/images/login/form-login-password.png" />
                    </n-icon>
                  </template>
                  <template #suffix>
                    <span class="forgot-password" @click="handleForgotPassword">忘记密码</span>
                  </template>
                </n-input>
              </n-form-item>
              <n-form-item path="captcha" label="" class="captcha-container">
                <n-input placeholder="请输入验证码" v-model:value="form.captcha" class="captcha-input">
                  <template #prefix>
                    <n-icon size="24" color="#0e7a0d">
                      <img src="@/assets/images/login/form-login-captcha.png" />
                    </n-icon>
                  </template>
                </n-input>
                <img :src="captchaUrl" class="captcha" />
                <div class="form-refresh" @click="genCaptcha">
                  <img src="@/assets/images/login/form-login-refresh.png" />
                  <div style="margin-left: 5px">{{ isMobile ? '换一张' : '看不清？换一张' }}</div>
                </div>
              </n-form-item>
            </n-form>

            <n-button type="info" class="form-info-login" @click="login">立即登录</n-button>
          </div>
        </div>
      </div>
    </n-config-provider>
  </div>
</template>

<script lang="ts">
  import { defineComponent, onMounted, reactive, ref, onBeforeUnmount, watch } from 'vue'
  import { getCaptcha, userLogin } from '@/api/user'
  import Auth from '@/utils/auth'
  import { FormInst, FormRules } from 'naive-ui'
  import { useRouter } from 'vue-router'
  import { useGlobalInit } from '@/types/common/globalInit'
  import { useSysStore, useUserStore } from '@/store'
  import {
    LockClosedOutline as LockClosedIcon,
    PersonOutline as PersonIcon,
    ShieldCheckmarkOutline as ShieldIcon,
  } from '@vicons/ionicons5'
  import { useFastArriveStore } from '@/store/fastArrive'

  export default defineComponent({
    components: {
      PersonIcon,
      LockClosedIcon,
      ShieldIcon,
    },
    setup() {
      // 添加响应式状态
      const isMobile = ref(false)
      const isTablet = ref(false)

      // 检测设备类型
      const checkDeviceType = () => {
        isMobile.value = window.innerWidth <= 768
        isTablet.value = window.innerWidth > 768 && window.innerWidth <= 1024
      }

      onMounted(() => {
        genCaptcha()
        checkDeviceType()

        // 添加窗口大小变化监听
        window.addEventListener('resize', checkDeviceType)
      })

      onBeforeUnmount(() => {
        // 移除事件监听和清除定时器
        window.removeEventListener('resize', checkDeviceType)
        clearInterval(refreshCaptcha)
      })

      const refreshCaptcha = setInterval(() => {
        genCaptcha()
      }, 60000)

      const auth = new Auth()
      const router = useRouter()
      const sysStore = useSysStore()
      const userStore = useUserStore()
      const formRef = ref<FormInst | null>(null)
      let form = reactive({
        username: '',
        password: '',
        captcha: '',
      })
      let captchaUrl = ref('')

      // 校验规则
      const rules: FormRules = {
        username: [
          {
            required: true,
            message: '请输入用户名',
            trigger: ['input', 'blur'],
          },
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: ['input', 'blur'],
          },
        ],
        captcha: [
          {
            required: true,
            message: '请输入验证码',
            trigger: ['input', 'blur'],
          },
        ],
      }

      // 登录
      const login = (e: MouseEvent | null) => {
        e?.preventDefault()
        formRef.value?.validate(errors => {
          if (!errors) {
            let params: any = {}
            Object.assign(params, form)
            userLogin(params)
              .then(async (res: any) => {
                clearInterval(refreshCaptcha)
                auth.setToken(res.data.token)
                useGlobalInit().init()
                // 验证密码是否是弱密码
                if (
                  /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,16}$/.test(
                    form.password
                  )
                ) {
                  sysStore.setUpdatePwdState(false)
                  userStore.setUserInfo(res.data.userInfo)
                  router.push({ path: '/gateway' })
                  await useFastArriveStore().getFastArrive()
                } else {
                  sysStore.setUpdatePwdState(true)
                  router.push({ path: '/updatePwd' })
                }
              })
              .catch(e => {
                genCaptcha()
              })
          }
        })
      }

      // 生成验证码
      const genCaptcha = () => {
        getCaptcha({}).then(res => {
          const blob = new Blob([res.data])
          captchaUrl.value = URL.createObjectURL(blob)
        })
      }

      // 处理忘记密码
      const handleForgotPassword = () => {
        // 这里可以根据实际需求实现忘记密码功能
        // 例如：跳转到忘记密码页面或弹出忘记密码对话框
        console.log('忘记密码功能')
        // router.push('/forgot-password') // 如果有忘记密码页面
        // 或者可以弹出一个对话框提示用户联系管理员
        window.$message?.info('请联系系统管理员重置密码')
      }

      // 键盘监听事件 - 仅在非移动设备上添加
      const setupKeyboardListener = () => {
        if (!isMobile.value) {
          document.onkeydown = e => {
            if (['Enter', 'NumpadEnter'].includes(e.code)) {
              login(null)
            }
          }
        } else {
          document.onkeydown = null
        }
      }

      // 监听设备类型变化时更新键盘监听
      watch(isMobile, newVal => {
        setupKeyboardListener()
      })

      // 初始设置键盘监听
      setupKeyboardListener()

      const inputProps = {
        height: '40px',
        padding: '14px',
        fontSize: '15px',
      }

      return {
        formRef,
        form,
        rules,
        login,
        captchaUrl,
        genCaptcha,
        handleForgotPassword,
        isMobile,
        isTablet,
        themeOverrides: {
          Input: {
            heightTiny: inputProps.height,
            paddingTiny: inputProps.padding,
            fontSizeTiny: inputProps.fontSize,
            heightSmall: inputProps.height,
            paddingSmall: inputProps.padding,
            fontSizeSmall: inputProps.fontSize,
            heightMedium: inputProps.height,
            paddingMedium: inputProps.padding,
            fontSizeMedium: inputProps.fontSize,
            heightLarge: inputProps.height,
            paddingLarge: inputProps.padding,
            fontSizeLarge: inputProps.fontSize,
          },
        },
      }
    },
  })
</script>
<style lang="less" scoped>
  @borderRadius: 10px;
  @formPanelWidth: 28rem;

  // 添加响应式断点变量
  @mobile-breakpoint: 768px;
  @tablet-breakpoint: 1024px;

  .login-container {
    background: url(@/assets/images/login/login2_zjxrmyy.png) 100% / cover fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
    box-sizing: border-box;

    @media (max-width: @mobile-breakpoint) {
      background-attachment: scroll; // 修复移动端背景图片问题
    }
  }

  .login-panel {
    height: 30rem;
    width: 70rem;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    display: flex;
    max-width: 100%;

    @media (max-width: @tablet-breakpoint) {
      height: auto;
      min-height: 30rem;
    }

    @media (max-width: @mobile-breakpoint) {
      width: 100%;
      width: 80vw;

      flex-direction: column;
      height: auto;
    }

    &-left {
      width: calc(100% - @formPanelWidth);
      background: url(@/assets/images/login/panel-left.jpg) 100% / cover no-repeat;
      border-radius: @borderRadius 0 0 @borderRadius;

      @media (max-width: @tablet-breakpoint) {
        width: 40%;
      }

      @media (max-width: @mobile-breakpoint) {
        width: 100%;
        height: 150px;
        border-radius: @borderRadius @borderRadius 0 0;
      }
    }

    &-right {
      width: @formPanelWidth;
      border-radius: 0 @borderRadius @borderRadius 0;
      box-sizing: border-box;
      padding: 45px;
      background: rgba(255, 255, 255, 0.95);
      display: flex;
      flex-direction: column;
      align-items: center;

      @media (max-width: @tablet-breakpoint) {
        width: 60%;
        padding: 30px;
      }

      @media (max-width: @mobile-breakpoint) {
        width: 100%;
        padding: 30px 20px;
        border-radius: 0 0 @borderRadius @borderRadius;
      }

      .form-logo {
        width: 20rem;
        max-width: 100%;

        @media (max-width: @tablet-breakpoint) {
          width: 16rem;
        }

        @media (max-width: @mobile-breakpoint) {
          width: 14rem;
        }
      }

      .form-info {
        margin-top: 40px;
        width: 80%;

        @media (max-width: @tablet-breakpoint) {
          width: 90%;
          margin-top: 30px;
        }

        @media (max-width: @mobile-breakpoint) {
          width: 100%;
          margin-top: 20px;
        }

        &-login {
          width: 100%;
          height: 40px;
          background: rgb(16, 112, 229);
          color: white;
          border-radius: 3px;
          text-align: center;
          line-height: 40px;
          cursor: pointer;
          margin-top: 20px;

          @media (max-width: @mobile-breakpoint) {
            height: 44px;
            line-height: 44px;
            font-size: 16px;
            border-radius: 4px;
            margin-top: 30px;
          }
        }
      }

      .form-refresh {
        display: flex;
        align-items: center;
        position: absolute;
        bottom: -30px;
        right: 0;
        font-size: 12px;
        color: rgb(68, 114, 221);
        cursor: pointer;

        @media (max-width: @mobile-breakpoint) {
          bottom: -25px;
          font-size: 10px;

          img {
            width: 12px;
            height: 12px;
          }
        }
      }
    }
  }

  .logo {
    position: absolute;
    left: 20px;
    top: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .captcha-container {
    position: relative;
  }

  .captcha-input {
    width: calc(100% - 120px) !important;
    height: 40px;

    @media (max-width: @mobile-breakpoint) {
      width: calc(100% - 100px) !important;
    }
  }

  .captcha {
    position: absolute;
    right: 0;
    width: 110px;
    height: 40px;

    @media (max-width: @mobile-breakpoint) {
      width: 90px;
    }
  }

  .input-item {
    height: 40px;
    display: flex;
    align-items: center;
  }

  .forgot-password {
    color: #1890ff;
    font-size: 12px;
    cursor: pointer;
    white-space: nowrap;
    margin-left: 8px;

    &:hover {
      color: #40a9ff;
      text-decoration: underline;
    }

    @media (max-width: @mobile-breakpoint) {
      font-size: 11px;
      margin-left: 6px;
    }
  }

  // 移动端触摸优化
  @media (max-width: @mobile-breakpoint) {
    :deep(.n-input) {
      input {
        font-size: 16px !important; // 防止iOS自动缩放
      }
    }

    :deep(.n-form-item) {
      margin-bottom: 20px;
    }

    // 增加点击区域
    .form-refresh,
    .form-info-login,
    .captcha {
      touch-action: manipulation;
    }

    .form-info-login {
      -webkit-tap-highlight-color: transparent;

      &:active {
        opacity: 0.8;
      }
    }
  }

  // 覆盖
  .n-tabs {
    margin: 0 !important;
    padding-right: 4%;
  }
</style>
