<template>
  <div class="med-container login-container">
    <n-config-provider :theme-overrides="themeOverrides">
      <div class="login-panel">
        <!-- v-if="!isMobile || isTablet" -->
        <div class="login-panel-left"></div>
        <div class="login-panel-right">
          <img src="@/assets/images/login/form-logo.png" class="form-logo" />
          <div class="form-info">
            <n-form ref="formRef" :model="form" :rules="rules" label-placement="left">
              <n-form-item path="username" label="">
                <n-input
                  placeholder="请输入用户名"
                  v-model:value="form.username"
                  :input-props="{ autocomplete: 'username' }"
                >
                  <template #prefix>
                    <n-icon size="24" color="#0e7a0d">
                      <img src="@/assets/images/login/form-logon-user.png" />
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>
              <n-form-item path="password" label="" class="password-container">
                <n-input
                  placeholder="请输入密码"
                  type="password"
                  :input-props="{ autocomplete: 'current-password' }"
                  show-password-on="click"
                  v-model:value="form.password"
                  class="password-input-with-forgot"
                >
                <!-- 原生密码查看按钮 -->

                  <template #prefix>
                    <n-icon size="24" color="#0e7a0d">
                      <img src="@/assets/images/login/form-login-password.png" />
                    </n-icon>
                  </template>
                </n-input>
              </n-form-item>
              <n-form-item path="captcha" label="" class="captcha-container">
                <n-input placeholder="请输入验证码" v-model:value="form.captcha" class="captcha-input">
                  <template #prefix>
                    <n-icon size="24" color="#0e7a0d">
                      <img src="@/assets/images/login/form-login-captcha.png" />
                    </n-icon>
                  </template>
                </n-input>
                <img :src="captchaUrl" class="captcha" />
                <div class="form-refresh" @click="genCaptcha">
                  <img src="@/assets/images/login/form-login-refresh.png" />
                  <div style="margin-left: 5px">{{ isMobile ? '换一张' : '看不清？换一张' }}</div>
                </div>
              </n-form-item>
            </n-form>

            <n-button type="info" class="form-info-login" @click="login">立即登录</n-button>
          </div>
        </div>
      </div>
    </n-config-provider>

    <!-- 忘记密码Modal -->
    <n-modal v-model:show="showForgotPasswordModal" preset="dialog" title="忘记密码">
      <template #header>
        <div class="modal-header">
          <n-icon size="20" color="#1890ff">
            <LockClosedIcon />
          </n-icon>
          <span style="margin-left: 8px;">重置密码</span>
        </div>
      </template>

      <n-form
        ref="forgotPasswordFormRef"
        :model="forgotPasswordForm"
        :rules="forgotPasswordRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item path="nickname" label="姓名">
          <n-input
            v-model:value="forgotPasswordForm.nickname"
            placeholder="请输入您的姓名"
            clearable
          />
        </n-form-item>

        <n-form-item path="emp_code" label="工号">
          <n-input
            v-model:value="forgotPasswordForm.emp_code"
            placeholder="请输入您的工号"
            clearable
          />
        </n-form-item>

        <n-form-item path="phone" label="电话">
          <n-input
            v-model:value="forgotPasswordForm.phone"
            placeholder="请输入您的电话号码"
            clearable
          />
        </n-form-item>

        <n-form-item path="sms_code" label="短信验证码">
          <div class="sms-code-wrapper">
            <n-input
              v-model:value="forgotPasswordForm.sms_code"
              placeholder="请输入短信验证码"
              clearable
              class="sms-code-input"
            />
            <n-button
              type="primary"
              :disabled="!forgotPasswordForm.phone || smsCodeCountdown > 0"
              @click="sendSmsCode"
              class="sms-code-button"
            >
              {{ smsCodeCountdown > 0 ? `${smsCodeCountdown}s后重发` : '发送验证码' }}
            </n-button>
          </div>
        </n-form-item>
      </n-form>

      <template #action>
        <n-space>
          <n-button @click="showForgotPasswordModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmitForgotPassword">提交申请</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts">
  import { defineComponent, onMounted, reactive, ref, onBeforeUnmount, watch } from 'vue'
  import { getCaptcha, userLogin } from '@/api/user'
  import Auth from '@/utils/auth'
  import { FormInst, FormRules } from 'naive-ui'
  import { useRouter } from 'vue-router'
  import { useGlobalInit } from '@/types/common/globalInit'
  import { useSysStore, useUserStore } from '@/store'
  import {
    LockClosedOutline as LockClosedIcon,
    PersonOutline as PersonIcon,
    ShieldCheckmarkOutline as ShieldIcon,
  } from '@vicons/ionicons5'
  import { useFastArriveStore } from '@/store/fastArrive'

  export default defineComponent({
    components: {
      PersonIcon,
      LockClosedIcon,
      ShieldIcon,
    },
    setup() {
      // 添加响应式状态
      const isMobile = ref(false)
      const isTablet = ref(false)

      // 检测设备类型
      const checkDeviceType = () => {
        isMobile.value = window.innerWidth <= 768
        isTablet.value = window.innerWidth > 768 && window.innerWidth <= 1024
      }

      onMounted(() => {
        genCaptcha()
        checkDeviceType()

        // 添加窗口大小变化监听
        window.addEventListener('resize', checkDeviceType)

        // 添加忘记密码点击事件监听
        setupForgotPasswordListener()
      })

      onBeforeUnmount(() => {
        // 移除事件监听和清除定时器
        window.removeEventListener('resize', checkDeviceType)
        clearInterval(refreshCaptcha)
      })

      const refreshCaptcha = setInterval(() => {
        genCaptcha()
      }, 60000)

      const auth = new Auth()
      const router = useRouter()
      const sysStore = useSysStore()
      const userStore = useUserStore()
      const formRef = ref<FormInst | null>(null)
      const forgotPasswordFormRef = ref<FormInst | null>(null)
      let form = reactive({
        username: '',
        password: '',
        captcha: '',
      })
      let captchaUrl = ref('')

      // 忘记密码相关状态
      const showForgotPasswordModal = ref(false)
      const smsCodeCountdown = ref(0)
      const forgotPasswordForm = reactive({
        nickname: '',
        emp_code: '',
        phone: '',
        sms_code: ''
      })

      // 校验规则
      const rules: FormRules = {
        username: [
          {
            required: true,
            message: '请输入用户名',
            trigger: ['input', 'blur'],
          },
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: ['input', 'blur'],
          },
        ],
        captcha: [
          {
            required: true,
            message: '请输入验证码',
            trigger: ['input', 'blur'],
          },
        ],
      }

      // 忘记密码表单验证规则
      const forgotPasswordRules: FormRules = {
        nickname: [
          {
            required: true,
            message: '请输入姓名',
            trigger: ['input', 'blur'],
          },
          {
            min: 2,
            max: 20,
            message: '姓名长度应在2-20个字符之间',
            trigger: ['input', 'blur'],
          },
        ],
        emp_code: [
          {
            required: true,
            message: '请输入工号',
            trigger: ['input', 'blur'],
          },
          {
            pattern: /^[A-Za-z0-9]+$/,
            message: '工号只能包含字母和数字',
            trigger: ['input', 'blur'],
          },
        ],
        phone: [
          {
            required: true,
            message: '请输入电话号码',
            trigger: ['input', 'blur'],
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: ['input', 'blur'],
          },
        ],
        sms_code: [
          {
            required: true,
            message: '请输入短信验证码',
            trigger: ['input', 'blur'],
          },
          {
            pattern: /^\d{6}$/,
            message: '验证码应为6位数字',
            trigger: ['input', 'blur'],
          },
        ],
      }

      // 登录
      const login = (e: MouseEvent | null) => {
        e?.preventDefault()
        formRef.value?.validate(errors => {
          if (!errors) {
            let params: any = {}
            Object.assign(params, form)
            userLogin(params)
              .then(async (res: any) => {
                clearInterval(refreshCaptcha)
                auth.setToken(res.data.token)
                useGlobalInit().init()
                // 验证密码是否是弱密码
                if (
                  /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,16}$/.test(
                    form.password
                  )
                ) {
                  sysStore.setUpdatePwdState(false)
                  userStore.setUserInfo(res.data.userInfo)
                  router.push({ path: '/gateway' })
                  await useFastArriveStore().getFastArrive()
                } else {
                  sysStore.setUpdatePwdState(true)
                  router.push({ path: '/updatePwd' })
                }
              })
              .catch(e => {
                genCaptcha()
              })
          }
        })
      }

      // 生成验证码
      const genCaptcha = () => {
        getCaptcha({}).then(res => {
          const blob = new Blob([res.data])
          captchaUrl.value = URL.createObjectURL(blob)
        })
      }

      // 处理忘记密码
      const handleForgotPassword = () => {
        // 重置表单
        forgotPasswordForm.nickname = ''
        forgotPasswordForm.emp_code = ''
        forgotPasswordForm.phone = ''
        forgotPasswordForm.sms_code = ''
        // 重置短信验证码倒计时
        smsCodeCountdown.value = 0
        // 显示Modal
        showForgotPasswordModal.value = true
      }

      // 发送短信验证码
      const sendSmsCode = () => {
        // 验证手机号码格式
        if (!/^1[3-9]\d{9}$/.test(forgotPasswordForm.phone)) {
          window.$message?.error('请输入正确的手机号码')
          return
        }

        // 这里可以调用API发送短信验证码
        console.log('发送短信验证码到:', forgotPasswordForm.phone)

        // 模拟API调用
        window.$message?.success('验证码已发送，请查收短信')

        // 开始倒计时
        smsCodeCountdown.value = 60
        const timer = setInterval(() => {
          smsCodeCountdown.value--
          if (smsCodeCountdown.value <= 0) {
            clearInterval(timer)
          }
        }, 1000)

        // 这里可以添加实际的API调用
        // sendSmsCodeRequest(forgotPasswordForm.phone)
        //   .then(res => {
        //     window.$message?.success('验证码已发送')
        //     // 开始倒计时
        //     smsCodeCountdown.value = 60
        //     const timer = setInterval(() => {
        //       smsCodeCountdown.value--
        //       if (smsCodeCountdown.value <= 0) {
        //         clearInterval(timer)
        //       }
        //     }, 1000)
        //   })
        //   .catch(err => {
        //     window.$message?.error('发送失败，请重试')
        //   })
      }

      // 提交忘记密码申请
      const handleSubmitForgotPassword = () => {
        forgotPasswordFormRef.value?.validate((errors) => {
          if (!errors) {
            // 这里可以调用API提交忘记密码申请
            console.log('提交忘记密码申请:', forgotPasswordForm)

            // 关闭Modal（移除了多余的提示消息）
            showForgotPasswordModal.value = false

            // 这里可以添加实际的API调用
            // submitForgotPasswordRequest(forgotPasswordForm)
            //   .then(res => {
            //     window.$message?.success('申请已提交成功')
            //     showForgotPasswordModal.value = false
            //   })
            //   .catch(err => {
            //     window.$message?.error('提交失败，请重试')
            //   })
          } else {
            window.$message?.error('请填写完整信息')
          }
        })
      }

      // 设置忘记密码点击事件监听
      const setupForgotPasswordListener = () => {
        // 使用事件委托来监听点击事件
        document.addEventListener('click', (e) => {
          const passwordInput = document.querySelector('.password-input-with-forgot')
          if (passwordInput) {
            const rect = passwordInput.getBoundingClientRect()
            const isMobileDevice = window.innerWidth <= 768
            const rightOffset = isMobileDevice ? -70 : -80 // 调整到输入框外部的位置
            const clickAreaWidth = isMobileDevice ? 60 : 70

            const afterElementArea = {
              left: rect.right + Math.abs(rightOffset), // 输入框右侧外部
              right: rect.right + Math.abs(rightOffset) + clickAreaWidth,
              top: rect.top,
              bottom: rect.bottom
            }

            const clickX = e.clientX
            const clickY = e.clientY

            // 检查点击是否在伪元素区域内
            if (clickX >= afterElementArea.left && clickX <= afterElementArea.right &&
                clickY >= afterElementArea.top && clickY <= afterElementArea.bottom) {
              e.preventDefault()
              e.stopPropagation()
              handleForgotPassword()
            }
          }
        })
      }

      // 键盘监听事件 - 仅在非移动设备上添加
      const setupKeyboardListener = () => {
        if (!isMobile.value) {
          document.onkeydown = e => {
            if (['Enter', 'NumpadEnter'].includes(e.code)) {
              login(null)
            }
          }
        } else {
          document.onkeydown = null
        }
      }

      // 监听设备类型变化时更新键盘监听
      watch(isMobile, () => {
        setupKeyboardListener()
      })

      // 初始设置键盘监听
      setupKeyboardListener()

      const inputProps = {
        height: '40px',
        padding: '14px',
        fontSize: '15px',
      }

      return {
        formRef,
        forgotPasswordFormRef,
        form,
        rules,
        forgotPasswordRules,
        login,
        captchaUrl,
        genCaptcha,
        handleForgotPassword,
        handleSubmitForgotPassword,
        sendSmsCode,
        setupForgotPasswordListener,
        showForgotPasswordModal,
        forgotPasswordForm,
        smsCodeCountdown,
        isMobile,
        isTablet,
        themeOverrides: {
          Input: {
            heightTiny: inputProps.height,
            paddingTiny: inputProps.padding,
            fontSizeTiny: inputProps.fontSize,
            heightSmall: inputProps.height,
            paddingSmall: inputProps.padding,
            fontSizeSmall: inputProps.fontSize,
            heightMedium: inputProps.height,
            paddingMedium: inputProps.padding,
            fontSizeMedium: inputProps.fontSize,
            heightLarge: inputProps.height,
            paddingLarge: inputProps.padding,
            fontSizeLarge: inputProps.fontSize,
          },
        },
      }
    },
  })
</script>
<style lang="less" scoped>
  @borderRadius: 10px;
  @formPanelWidth: 28rem;

  // 添加响应式断点变量
  @mobile-breakpoint: 768px;
  @tablet-breakpoint: 1024px;

  .login-container {
    background: url(@/assets/images/login/login2_zjxrmyy.png) 100% / cover fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
    box-sizing: border-box;

    @media (max-width: @mobile-breakpoint) {
      background-attachment: scroll; // 修复移动端背景图片问题
    }
  }

  .login-panel {
    height: 30rem;
    width: 70rem;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    display: flex;
    max-width: 100%;

    @media (max-width: @tablet-breakpoint) {
      height: auto;
      min-height: 30rem;
    }

    @media (max-width: @mobile-breakpoint) {
      width: 100%;
      width: 80vw;

      flex-direction: column;
      height: auto;
    }

    &-left {
      width: calc(100% - @formPanelWidth);
      background: url(@/assets/images/login/panel-left.jpg) 100% / cover no-repeat;
      border-radius: @borderRadius 0 0 @borderRadius;

      @media (max-width: @tablet-breakpoint) {
        width: 40%;
      }

      @media (max-width: @mobile-breakpoint) {
        width: 100%;
        height: 150px;
        border-radius: @borderRadius @borderRadius 0 0;
      }
    }

    &-right {
      width: @formPanelWidth;
      border-radius: 0 @borderRadius @borderRadius 0;
      box-sizing: border-box;
      padding: 45px;
      background: rgba(255, 255, 255, 0.95);
      display: flex;
      flex-direction: column;
      align-items: center;

      @media (max-width: @tablet-breakpoint) {
        width: 60%;
        padding: 30px;
      }

      @media (max-width: @mobile-breakpoint) {
        width: 100%;
        padding: 30px 20px;
        border-radius: 0 0 @borderRadius @borderRadius;
      }

      .form-logo {
        width: 20rem;
        max-width: 100%;

        @media (max-width: @tablet-breakpoint) {
          width: 16rem;
        }

        @media (max-width: @mobile-breakpoint) {
          width: 14rem;
        }
      }

      .form-info {
        margin-top: 40px;
        width: 80%;

        @media (max-width: @tablet-breakpoint) {
          width: 90%;
          margin-top: 30px;
        }

        @media (max-width: @mobile-breakpoint) {
          width: 100%;
          margin-top: 20px;
        }

        &-login {
          width: 100%;
          height: 40px;
          background: rgb(16, 112, 229);
          color: white;
          border-radius: 3px;
          text-align: center;
          line-height: 40px;
          cursor: pointer;
          margin-top: 20px;

          @media (max-width: @mobile-breakpoint) {
            height: 44px;
            line-height: 44px;
            font-size: 16px;
            border-radius: 4px;
            margin-top: 30px;
          }
        }
      }

      .form-refresh {
        display: flex;
        align-items: center;
        position: absolute;
        bottom: -30px;
        right: 0;
        font-size: 12px;
        color: rgb(68, 114, 221);
        cursor: pointer;

        @media (max-width: @mobile-breakpoint) {
          bottom: -25px;
          font-size: 10px;

          img {
            width: 12px;
            height: 12px;
          }
        }
      }
    }
  }

  .logo {
    position: absolute;
    left: 20px;
    top: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .captcha-container {
    position: relative;
  }

  .captcha-input {
    width: calc(100% - 120px) !important;
    height: 40px;

    @media (max-width: @mobile-breakpoint) {
      width: calc(100% - 100px) !important;
    }
  }

  .captcha {
    position: absolute;
    right: 0;
    width: 110px;
    height: 40px;

    @media (max-width: @mobile-breakpoint) {
      width: 90px;
    }
  }

  .input-item {
    height: 40px;
    display: flex;
    align-items: center;
  }

  .password-container {
    position: relative;

    .password-input-with-forgot {
      position: relative;

      // 使用深度选择器来修改内部输入框样式
      :deep(.n-input) {
        .n-input__input-el {
          padding-right: 50px !important; // 恢复正常的右侧内边距，为小眼睛留出空间

          @media (max-width: @mobile-breakpoint) {
            padding-right: 45px !important;
          }
        }
      }

      &::after {
        content: "忘记密码";
        position: absolute;
        right: -80px; // 移动到输入框外部，小眼睛的右侧
        top: 50%;
        transform: translateY(-50%);
        color: #1890ff;
        font-size: 12px;
        cursor: pointer;
        white-space: nowrap;
        z-index: 10;
        pointer-events: none; // 伪元素本身不响应点击
        transition: color 0.2s ease;

        @media (max-width: @mobile-breakpoint) {
          font-size: 11px;
          right: -70px;
        }
      }

      // 为了让伪元素区域可以点击，我们需要一个透明的覆盖层
      &::before {
        content: "";
        position: absolute;
        right: -80px; // 调整点击区域位置到输入框外部
        top: 0;
        width: 70px;
        height: 100%;
        z-index: 11;
        cursor: pointer;

        @media (max-width: @mobile-breakpoint) {
          right: -70px;
          width: 60px;
        }
      }

      // 鼠标悬停效果
      &:hover::after {
        color: #40a9ff;
        text-decoration: underline;
      }
    }
  }

  // 移动端触摸优化
  @media (max-width: @mobile-breakpoint) {
    :deep(.n-input) {
      input {
        font-size: 16px !important; // 防止iOS自动缩放
      }
    }

    :deep(.n-form-item) {
      margin-bottom: 20px;
    }

    // 增加点击区域
    .form-refresh,
    .form-info-login,
    .captcha {
      touch-action: manipulation;
    }

    .form-info-login {
      -webkit-tap-highlight-color: transparent;

      &:active {
        opacity: 0.8;
      }
    }
  }

  // 覆盖
  .n-tabs {
    margin: 0 !important;
    padding-right: 4%;
  }

  // 忘记密码Modal样式
  .modal-header {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  // 短信验证码样式
  .sms-code-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-start;

    .sms-code-input {
      flex: 1;
    }

    .sms-code-button {
      height: 40px;
      padding: 0 16px;
      font-size: 12px;
      white-space: nowrap;
      flex-shrink: 0;
      min-width: 100px;

      @media (max-width: @mobile-breakpoint) {
        min-width: 80px;
        font-size: 11px;
        padding: 0 12px;
      }
    }

    @media (max-width: @mobile-breakpoint) {
      gap: 8px;
    }
  }

  // Modal内表单样式优化
  :deep(.n-modal) {
    .n-dialog {
      max-width: 480px;

      @media (max-width: @mobile-breakpoint) {
        max-width: 90vw;
        margin: 20px;
      }
    }

    .n-form-item {
      margin-bottom: 20px;

      .n-form-item-label {
        font-weight: 500;
        color: #333;
      }

      .n-input {
        height: 40px;

        input {
          font-size: 14px;
        }
      }
    }

    .n-dialog__action {
      padding-top: 20px;

      .n-button {
        height: 36px;
        padding: 0 20px;
        font-size: 14px;

        &:first-child {
          margin-right: 12px;
        }
      }
    }
  }
</style>
