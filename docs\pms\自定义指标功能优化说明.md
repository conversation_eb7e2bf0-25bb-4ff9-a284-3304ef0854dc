# 自定义指标功能优化说明

## 📋 需求概述

优化月度绩效管理中的"添加其他指标"功能，实现以下核心需求：

1. **主页面传递分类数据**：将"其他指标"分类的数据传递到添加自定义指标页面
2. **过滤重复选择**：通过itemCode过滤itemOptions，防止用户重复选择已存在的指标
3. **动态创建选项**：使用tag & filterable功能允许用户动态创建新的指标名称

## 🔧 实现方案

### 1. 主页面修改 (`index.vue`)

#### 1.1 添加已存在自定义指标数据状态
```typescript
// 已存在的自定义指标数据
const existingCustomItems = ref<any[]>([])
```

#### 1.2 传递数据到子组件
```vue
<AddCustomItem
  :show="showAddCustomItem"
  @update:show="showAddCustomItem = $event"
  :template-id="awardCalcTemplateStore.templateId"
  :calc-month="JPGlobal.timestampToTime(searchForm.month, 'yyyy-MM')"
  :existing-custom-items="existingCustomItems"
  @success="handleSearch"
/>
```

#### 1.3 更新自定义指标数据
在获取自定义指标数据时，同步更新`existingCustomItems`：
```typescript
const customItems = await queryPmsAwardCalcCustomResult({
  calcMonth: JPGlobal.timestampToTime(searchForm.month, 'yyyy-MM'),
  templateId: awardCalcTemplateStore.templateId,
})

if (customItems.code == 200 && customItems.data?.length > 0) {
  // 更新已存在的自定义指标数据
  existingCustomItems.value = customItems.data
  // ... 其他逻辑
} else {
  // 如果没有自定义指标数据，清空已存在的数据
  existingCustomItems.value = []
}
```

### 2. AddCustomItem组件修改

#### 2.1 Props定义
```typescript
const props = defineProps<{
  show: boolean
  templateId: number
  calcMonth: string
  existingCustomItems?: any[] // 已存在的自定义指标数据
}>()
```

#### 2.2 指标名称选择器优化
```vue
<n-select
  v-model:value="formValue.itemCode"
  :options="filteredItemOptions"
  placeholder="请选择或输入指标名称"
  filterable
  tag
  :show-arrow="false"
  :max-tag-count="1"
  @create="handleCreateOption"
/>
```

#### 2.3 过滤逻辑实现
```typescript
// 过滤后的选项列表，排除已存在的自定义指标
const filteredItemOptions = computed(() => {
  const existingItemCodes = props.existingCustomItems?.map(item => item.itemCode) || []
  return itemOptions.value.filter(option => !existingItemCodes.includes(option.value))
})
```

#### 2.4 动态创建选项功能
```typescript
// 存储用户新创建的选项
const userCreatedOptions = ref<Set<string>>(new Set())

// 处理动态创建选项
const handleCreateOption = (label: string) => {
  // 创建新选项并添加到选项列表中
  const newOption = {
    label: label,
    value: label,
  }

  // 检查是否已存在相同的选项
  const exists = itemOptions.value.some(option => option.value === label)
  if (!exists) {
    // 将新选项添加到原始选项列表中
    itemOptions.value.push(newOption)
  }

  // 标记为用户创建的选项
  userCreatedOptions.value.add(label)

  // 设置表单值
  formValue.itemCode = label

  return newOption
}
```

#### 2.5 临时项标识功能
```typescript
// 保存时判断是否为用户新创建的选项
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    // 判断是否为用户新创建的选项
    // 通过检查是否在用户创建的选项集合中来确定
    const isTempItem = userCreatedOptions.value.has(formValue.itemCode) ? '1' : '0'

    const params: AddCustomItemParams = {
      ...formValue,
      templateId: props.templateId,
      calcMonth: props.calcMonth,
      formattedTotalAwardAmount: formValue.totalAwardAmount.toFixed(2),
      isTempItem, // 添加临时项标识
    }

    const res = await addCustomItem(params)
    // ... 处理响应
  } catch (err) {
    console.error('表单验证失败:', err)
  }
}
```

#### 2.6 表单重置功能
```typescript
// 重置表单
const resetForm = () => {
  formValue.itemCode = ''
  formValue.totalAwardAmount = 0
  formValue.calcStatus = 'SUCCESS'
  formValue.calcDetails = ''
  formValue.config = ''
  // 清空用户创建的选项记录
  userCreatedOptions.value.clear()
}

const handleUpdateShow = (value: boolean) => {
  if (!value) {
    // 弹窗关闭时重置表单
    resetForm()
  }
  emit('update:show', value)
}
```

## ✨ 功能特性

### 🎯 核心功能
1. **智能过滤**：自动过滤已存在的自定义指标，避免重复添加
2. **动态创建**：支持用户输入新的指标名称，实时创建选项
3. **临时项标识**：自动识别用户新创建的选项，添加`isTempItem`标识
4. **数据同步**：主页面与子组件数据实时同步
5. **表单重置**：弹窗关闭或提交成功后自动重置表单

### 🔄 交互流程
1. 用户点击"其他指标项目"按钮
2. 弹窗显示，自动过滤已存在的指标选项
3. 用户可以：
   - 从下拉列表选择预定义的"其他指标"分类项目
   - 直接输入新的指标名称（tag模式）
4. 输入金额和说明信息
5. 提交成功后刷新主页面数据

### 🛡️ 数据安全
- 前端过滤防止重复选择
- 后端验证确保数据完整性
- 表单验证确保必填项完整
- 临时项标识确保数据来源可追溯

### 📡 API接口更新
更新了`AddCustomItemParams`接口，新增`isTempItem`字段：
```typescript
export interface AddCustomItemParams {
  /** 计算月份,格式:YYYY-MM */
  calcMonth: string
  /** 模板ID */
  templateId: number
  /** 指标编码 */
  itemCode: string
  /** 指标金额 */
  totalAwardAmount: number
  /** 格式化后的金额 */
  formattedTotalAwardAmount: string
  /** 计算状态 */
  calcStatus: 'SUCCESS' | 'ERROR'
  /** 计算详情 */
  calcDetails?: string
  /** 是否为临时项目 '1'-是 '0'-否 */
  isTempItem?: string
}
```

## 📝 使用说明

1. **选择已有指标**：从下拉列表中选择"其他指标"分类下的预定义项目（isTempItem='0'）
2. **创建新指标**：直接在输入框中输入新的指标名称，系统会自动创建选项（isTempItem='1'）
3. **避免重复**：系统会自动过滤掉当前科室当前月份已添加的自定义指标
4. **数据持久化**：新创建的指标名称会临时添加到选项列表中，便于后续使用
5. **标识区分**：系统会自动标识指标来源，便于后端进行不同的处理逻辑

## 🔍 技术要点

- 使用Vue 3 Composition API
- NaiveUI组件库的n-select组件
- 响应式数据管理
- 计算属性实现动态过滤
- 事件处理实现动态创建

## 🎨 界面美化升级

### 📱 弹窗界面重新设计
- **更大的弹窗尺寸**：从500px扩展到700px，提供更好的视觉体验
- **精美的头部设计**：添加图标和标题，增强视觉识别度
- **响应式布局**：支持移动端适配，确保在不同设备上的良好体验

### 🏷️ 指标信息智能展示
- **动态信息卡片**：根据选择的指标自动显示详细信息
- **系统指标展示**：参考ItemHoverCard组件，展示统计规则、统计频次、适用范围等详细信息
- **临时指标标识**：清晰标识用户新创建的临时指标，提供友好提示

### 🎯 视觉状态区分
- **系统指标**：绿色渐变背景，书签图标，展示完整的指标信息
- **临时指标**：橙色渐变背景，闪电图标，提示用户这是新创建的指标
- **分类标签**：使用统一的颜色体系，与系统其他部分保持一致

### ✨ 交互体验优化
- **实时信息更新**：选择指标时立即显示相关信息
- **表单验证增强**：添加最小值限制、字符计数等
- **按钮状态管理**：根据表单完整性动态启用/禁用提交按钮

## 🔧 新增功能实现

### 📝 编辑功能
- **复用弹窗组件**：AddCustomItem组件支持编辑模式，通过`editData` prop传递编辑数据
- **智能模式切换**：根据是否有编辑数据自动切换添加/编辑模式
- **数据回填**：编辑时自动填充现有数据到表单
- **API支持**：新增`updateCustomItem`接口支持数据更新

### 📋 备注功能
- **表格新增备注列**：在CustomIndicators组件中添加备注显示列
- **优先级显示**：公式/计算详情列优先显示备注，无备注时显示公式
- **主页面渲染优化**：在主页面渲染时优先使用备注信息
- **完整信息展示**：用户可以同时看到备注和公式信息

### 🔄 数据流程优化
```mermaid
graph TD
    A[用户点击编辑] --> B[传递editData到弹窗]
    B --> C[弹窗检测编辑模式]
    C --> D[自动填充表单数据]
    D --> E[用户修改信息]
    E --> F[提交时调用更新API]
    F --> G[刷新列表数据]
    G --> H[主页面重新渲染]
```

## 🔄 数据同步机制

### 📡 事件驱动更新
- **CustomIndicators组件**：在增删改查操作成功后发出`data-changed`事件
- **主页面监听**：通过`onDataChanged`回调监听数据变更事件
- **自动刷新**：触发`handleSearch`方法重新加载所有数据

### 🔧 实现细节
```typescript
// CustomIndicators组件 - 发出事件
const handleSuccess = () => {
  loadCustomItems() // 刷新列表
  emit('data-changed') // 通知主页面数据已变更
}

// 主页面 - 监听事件
const handleCustomIndicatorsDataChanged = async () => {
  console.log('自定义指标数据已变更，触发主页面数据更新')
  await handleSearch()
}
```

### 🎯 触发场景
- ✅ **添加自定义指标**：成功添加后触发主页面更新
- ✅ **编辑自定义指标**：成功修改后触发主页面更新
- ✅ **删除自定义指标**：成功删除后触发主页面更新
- ✅ **数据一致性**：确保主页面显示的数据与自定义指标列表保持同步

## 🏷️ 系统指标名称显示优化

### 📋 智能名称映射
- **系统指标识别**：通过`isTempItem`字段判断指标类型
- **名称获取**：系统指标通过store的`findItemNameByCode`方法获取真实名称
- **显示优化**：主页面显示友好的指标名称而非代码

### 🔧 实现逻辑
```typescript
// 判断是否为系统指标，如果是则通过store获取itemName
let displayItemName = item.itemCode
if (item.isTempItem === '0') {
  // 系统指标，通过store获取itemName
  const systemItemName = awardCalcStore.findItemNameByCode(item.itemCode)
  if (systemItemName && !systemItemName.includes('未知项目')) {
    displayItemName = systemItemName
  }
}
```

### 🎯 显示规则
- **系统指标** (`isTempItem = '0'`)：显示从store获取的真实指标名称
- **临时指标** (`isTempItem = '1'`)：显示用户输入的指标名称
- **未知指标**：如果在store中找不到，则显示原始itemCode

## 💡 智能提示功能

### 🎨 renderOption自定义渲染
- **已配置指标**：显示红色警告提示，说明该指标已在模板中配置
- **可选指标**：显示绿色确认提示，说明该指标可以添加
- **详细说明**：提供清晰的操作指导和状态说明

### 🔧 实现细节
```typescript
// 自定义渲染选项，为已配置的指标添加提示
const renderOption = ({ node, option }: { node: any, option: any }) => {
  const existingItemCodes = props.existingCustomItems?.map(item => item.itemCode) || []
  const isExisting = existingItemCodes.includes(option.value)

  if (isExisting) {
    return h(NTooltip, {
      placement: 'right'
    }, {
      trigger: () => node,
      default: () => h('div', [
        h('div', { style: { color: '#f56565' } }, '⚠️ 该指标已配置'),
        h('div', '指标已在当前模板中配置过，无法重复添加')
      ])
    })
  }

  return h(NTooltip, {
    placement: 'right'
  }, {
    trigger: () => node,
    default: () => h('div', [
      h('div', { style: { color: '#52c41a' } }, '✅ 可选择指标'),
      h('div', '该指标可以添加到当前模板中')
    ])
  })
}
```

### 🎯 用户体验提升
- **视觉反馈**：通过颜色和图标直观显示指标状态
- **操作指导**：提供明确的操作建议和限制说明
- **防误操作**：避免用户选择已配置的指标
- **友好提示**：使用Tooltip提供详细信息而不占用界面空间

## 🎉 优化效果

- ✅ 提升用户体验，避免重复选择
- ✅ 支持灵活的指标名称创建
- ✅ 数据同步准确，界面响应及时
- ✅ 表单操作更加智能化
- ✅ 界面美观现代，信息展示清晰
- ✅ 系统指标与临时指标区分明确
- ✅ 响应式设计，适配多种设备
- ✅ 完整的编辑功能，支持数据修改
- ✅ 备注信息优先显示，提升可读性
- ✅ 自动数据同步，操作后立即更新主页面
- ✅ 智能名称显示，系统指标显示真实名称而非代码
- ✅ 智能提示功能，清晰显示指标配置状态和操作建议
