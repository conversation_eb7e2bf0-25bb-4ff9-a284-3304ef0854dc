import request from '@/utils/request'
import { ContentTypes, RequestType } from '@/types/enums/enums'

/**
 * 查询用户菜单/路由
 * @param data 参数
 * @returns
 */
export function queryUserMenus(data: Object) {
  return request({
    url: '/core/user/queryUserMenu',
    method: RequestType.POST,
    data: data,
    hideLoadingBar: true,
  })
}

/**
 * 登录
 * @param data 参数
 * @returns
 */
export function userLogin(data: any) {
  return request({
    url: '/login',
    method: RequestType.POST,
    contentType: ContentTypes.FORM_URL,
    data: data,
    headers: {
      CAPTCHA: data.captcha,
    },
  })
}

/**
 * 登出
 * @param data 参数
 * @returns
 */
export function userLogout(data: Object) {
  return request({
    url: '/logout',
    method: RequestType.POST,
    data: data,
  })
}

/**
 * 修改密码
 * @param data 参数
 * @returns
 */
export function updatePwd(data: Object) {
  return request({
    url: '/core/user/updatePwd',
    method: RequestType.POST,
    data: data,
  })
}

/**
 * 修改用户信息
 * @param data 参数
 * @returns
 */
export function updateUserInfo(data: Object) {
  return request({
    url: '/core/user/updateUserInfo',
    method: RequestType.POST,
    data: data,
  })
}

/**
 * 查询用户信息
 * @param data 参数
 * @returns
 */
export function queryCacheUserInfo(data: Object) {
  return request({
    url: '/core/user/queryCacheUserInfo',
    method: RequestType.POST,
    data: data,
    hideLoadingBar: true,
  })
}

/**
 * 查询用户信息
 * @param data 参数
 * @returns
 */
export function modifyDept(data: Object) {
  return request({
    url: '/core/user/modifyDept',
    method: RequestType.POST,
    data: data,
    hideLoadingBar: true,
  })
}

/**
 * 判断用户名是否存在
 * @param data 参数
 * @returns
 */
export function existUser(data: Object) {
  return request({
    url: '/core/user/existUser',
    method: RequestType.POST,
    data: data,
  })
}

/**
 * 判断旧密码是否正确
 * @param data 参数
 * @returns
 */
export function isOldPwdTrue(data: Object) {
  return request({
    url: '/core/user/isOldPwdTrue',
    method: RequestType.POST,
    data: data,
  })
}

/**
 * 查询用户列表
 * @param data 参数
 * @returns
 */
export function queryUserList(data: Object) {
  return request({
    url: '/core/user/list',
    method: RequestType.POST,
    data: data,
  })
}
/**
 * 添加用户
 * @param data 参数
 * @returns
 */
export function saveUser(data: Object) {
  return request({
    url: '/core/user/save',
    method: RequestType.POST,
    data: data,
  })
}
/**
 * 注册用户
 * @param data 参数
 * @returns
 */
export function register(data: Object) {
  return request({
    url: '/core/user/register',
    method: RequestType.POST,
    data: data,
  })
}

/**
 * 修改用户
 * @param data 参数
 * @returns
 */
export function updateUser(data: Object) {
  return request({
    url: '/core/user/update',
    method: RequestType.PUT,
    data: data,
  })
}

/**
 * 删除用户
 * @param data 参数
 * @returns
 */
export function deleteUser(data: Object) {
  return request({
    url: '/core/user/delete',
    method: RequestType.DEL,
    data: data,
  })
}

/**
 * 删除用户
 * @param data 参数
 * @returns
 */
export function deleteUserToken(data: Object) {
  return request({
    url: '/core/user/deleteUserTokenByIds',
    method: RequestType.DEL,
    data: data,
  })
}

/**
 * 刷新token
 * @param data 参数
 * @returns
 */
export function refreshToken(data: Object) {
  return request({
    url: '/token/refreshToken',
    method: RequestType.POST,
    data: data,
  })
}

/**
 * 获取验证码
 * @param data 参数
 * @returns
 */
export function getCaptcha(data: Object) {
  return request({
    url: '/core/captcha/generate',
    method: RequestType.GET,
    data: data,
    responseType: 'blob',
  })
}

/**
 * 生成同步用户穿梭框的 options
 * @param data 参数
 * @returns
 */
export function genSynUserOptions(data: Object) {
  return request({
    url: '/core/user/genSynUserOptions',
    method: RequestType.POST,
    data: data,
  })
}

/**
 * 将指定员工同步到用户
 * @param data
 */
export function synchEmpToUser(data: Object) {
  return request({
    url: '/core/user/synchEmpToUser',
    method: RequestType.PUT,
    data: data,
  })
}

/**
 * 获取短信验证码
 * @param data 参数
 */
export function getSmsCode(data: Object) {
  return request({
    url: '/core/user/getSmsCode',
    method: RequestType.POST,
    data: data,
  })
}

/**
 * 重置密码
 * @param data 参数
 */
export function resetPwd(data: Object) {
  return request({
    url: '/core/user/resetPwd',
    method: RequestType.POST,
    data: data,
  })
}
