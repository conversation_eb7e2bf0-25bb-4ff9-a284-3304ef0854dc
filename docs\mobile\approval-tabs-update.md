# 移动端审批页面Tab化改造 📱

## 🎯 更新概述

根据用户需求，将移动端审批页面的统计卡片改造为顶部Tab切换模式，并添加了前端筛选功能。

## ✅ 主要改动

### 1. **统计卡片改为Tab切换** 🔄

**修改前：**
```vue
<!-- 顶部统计卡片 -->
<div class="stat-cards-container">
  <div class="stat-card todo-card" @click="switchTab('todo')">
    <div class="card-icon">
      <n-icon size="24" color="#409eff">
        <ClockOutline />
      </n-icon>
    </div>
    <div class="card-content">
      <div class="card-value">{{ todoCount }}</div>
      <div class="card-label">待办任务</div>
    </div>
  </div>
  <!-- 其他卡片... -->
</div>
```

**修改后：**
```vue
<!-- 顶部Tab切换 -->
<div class="approval-tabs-container">
  <n-tabs 
    v-model:value="activeTab" 
    type="line" 
    animated 
    size="medium"
    class="approval-tabs"
  >
    <n-tab-pane name="todo" :tab="renderTabWithBadge('todo', '待办', todoCount)" />
    <n-tab-pane name="done" :tab="renderTabWithBadge('done', '已办', doneCount)" />
    <n-tab-pane name="my" :tab="renderTabWithBadge('my', '我的流程', myCount)" />
    <n-tab-pane name="copy" :tab="renderTabWithBadge('copy', '抄送', copyCount)" />
  </n-tabs>
</div>
```

### 2. **添加前端筛选功能** 🔍

**筛选输入框：**
```vue
<n-input
  v-model:value="searchKeyword"
  placeholder="搜索科室/人员/任务..."
  clearable
  size="medium"
  class="search-input"
>
  <template #prefix>
    <n-icon size="16">
      <SearchOutline />
    </n-icon>
  </template>
</n-input>
```

**筛选逻辑：**
- 支持搜索科室名称
- 支持搜索人员姓名  
- 支持搜索任务名称
- 支持搜索流程名称
- 实时筛选，无需点击搜索按钮

### 3. **Tab徽章显示** 🏷️

使用 `renderTabWithBadge` 方法为每个Tab添加数量徽章：

```typescript
const renderTabWithBadge = (key: string, title: string, count: number) => {
  return h('div', { class: 'flex items-center gap-2' }, [
    h('span', title),
    count > 0 ? h('n-badge', { 
      value: count, 
      size: 'small', 
      type: 'info',
      style: { fontSize: '10px' }
    }) : null
  ])
}
```

## 🎨 UI/UX 改进

### 1. **更好的空间利用**
- Tab模式比卡片模式节省垂直空间
- 筛选功能与日期筛选并排显示
- 内容区域获得更多显示空间

### 2. **更直观的导航**
- Tab切换更符合移动端使用习惯
- 徽章显示让用户一目了然各类任务数量
- 动画效果提升用户体验

### 3. **强大的筛选功能**
- 实时搜索，即时反馈
- 多字段匹配，搜索更精准
- 清空按钮，操作更便捷

## 🔧 技术实现

### 1. **响应式筛选**
```typescript
const filteredTodoList = computed(() => {
  if (!searchKeyword.value) return todoList.value
  return todoList.value.filter((item: any) => {
    const keyword = searchKeyword.value.toLowerCase()
    const processName = item.processInstance?.name?.toLowerCase() || ''
    const taskName = item.name?.toLowerCase() || ''
    const empName = item.processInstance?.startUser?.empName?.toLowerCase() || ''
    const deptName = item.processInstance?.startUser?.deptName?.toLowerCase() || ''
    
    return processName.includes(keyword) || 
           taskName.includes(keyword) || 
           empName.includes(keyword) || 
           deptName.includes(keyword)
  })
})
```

### 2. **Tab样式定制**
```css
/* Tab容器样式 */
.approval-tabs-container {
  @apply bg-white border-b border-gray-100;
}

.approval-tabs {
  @apply px-4;
}

.approval-tabs :deep(.n-tabs-nav) {
  @apply border-b-0;
}

.approval-tabs :deep(.n-tabs-tab) {
  @apply px-3 py-3;
}

.approval-tabs :deep(.n-tabs-tab-label) {
  @apply text-sm font-medium;
}
```

### 3. **搜索输入框样式**
```css
.search-input {
  @apply flex-1 ml-2;
}
```

## 📊 筛选支持的字段

### 待办任务 (Todo)
- ✅ 流程名称 (`processInstance.name`)
- ✅ 任务名称 (`name`)
- ✅ 发起人姓名 (`processInstance.startUser.empName`)
- ✅ 发起科室 (`processInstance.startUser.deptName`)

### 已办任务 (Done)
- ✅ 流程名称 (`processInstance.name`)
- ✅ 任务名称 (`name`)
- ✅ 发起人姓名 (`processInstance.startUser.empName`)
- ✅ 发起科室 (`processInstance.startUser.deptName`)

### 我的流程 (My)
- ✅ 流程名称 (`name`)
- ✅ 流程分类 (`categoryName`)
- ✅ 发起人姓名 (`startUser.empName`)
- ✅ 发起科室 (`startUser.deptName`)

### 抄送我的 (Copy)
- ✅ 流程名称 (`processInstanceName`)
- ✅ 任务名称 (`taskName`)
- ✅ 发起人姓名 (`startUserName`)
- ✅ 发起科室 (`startDeptName`)

## 🚀 使用方法

### 1. **Tab切换**
- 点击顶部Tab标签切换不同类型的任务
- Tab标签显示对应类型的任务数量徽章

### 2. **搜索筛选**
- 在右侧搜索框输入关键词
- 支持搜索科室、人员、任务名称
- 实时显示筛选结果
- 点击清空按钮清除搜索条件

### 3. **日期筛选**
- 使用左侧下拉框选择时间范围
- 支持今天、本周、本月、全部选项
- 与搜索筛选可以组合使用

## 📱 移动端优化

### 1. **触摸友好**
- Tab切换区域足够大，便于手指点击
- 搜索输入框适配移动端键盘
- 清空按钮位置合理，易于操作

### 2. **性能优化**
- 使用computed属性实现响应式筛选
- 避免不必要的重新渲染
- 搜索防抖处理（如需要可添加）

### 3. **视觉效果**
- Tab切换有动画效果
- 徽章颜色与主题一致
- 搜索图标提升视觉识别度

## 🔄 与原版本的兼容性

- ✅ 保持所有原有功能不变
- ✅ API调用逻辑完全一致
- ✅ 数据结构无任何变化
- ✅ 任务操作功能完整保留

## 📋 后续优化建议

### 1. **功能增强**
- [ ] 添加搜索历史记录
- [ ] 支持高级筛选条件
- [ ] 添加排序功能
- [ ] 支持批量操作

### 2. **性能优化**
- [ ] 添加搜索防抖
- [ ] 实现虚拟滚动
- [ ] 优化大数据量渲染

### 3. **用户体验**
- [ ] 添加搜索建议
- [ ] 支持语音搜索
- [ ] 添加快捷筛选标签

---

## 📝 总结

本次改造成功将移动端审批页面从卡片模式升级为Tab模式，并添加了强大的前端筛选功能。新设计更符合移动端使用习惯，提供了更好的用户体验和更高的操作效率。🎉

**主要优势：**
- ✅ 更好的空间利用率
- ✅ 更直观的导航体验  
- ✅ 强大的实时搜索功能
- ✅ 完全兼容原有功能
- ✅ 优秀的移动端适配
