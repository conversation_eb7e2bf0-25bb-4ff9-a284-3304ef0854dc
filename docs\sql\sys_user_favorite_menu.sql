-- 用户常用菜单表
CREATE TABLE sys_user_favorite_menu (
  id BIGSERIAL PRIMARY KEY,
  username VARCHAR(50) NOT NULL,
  menu_path VARCHAR(255) NOT NULL,
  menu_name VARCHAR(100) NOT NULL,
  menu_icon VARCHAR(100),
  sort_order INTEGER DEFAULT 0,
  system_id INTEGER,
  menu_id INTEGER,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status CHAR(1) DEFAULT '1',
  is_pinned CHAR(1) DEFAULT '0',
  pin_order INTEGER,
  remark VARCHAR(500)
);

-- 创建注释
COMMENT ON TABLE sys_user_favorite_menu IS '用户常用菜单表';
COMMENT ON COLUMN sys_user_favorite_menu.id IS '主键ID';
COMMENT ON COLUMN sys_user_favorite_menu.username IS '用户名';
COMMENT ON COLUMN sys_user_favorite_menu.menu_path IS '菜单路径';
COMMENT ON COLUMN sys_user_favorite_menu.menu_name IS '菜单名称';
COMMENT ON COLUMN sys_user_favorite_menu.menu_icon IS '菜单图标';
COMMENT ON COLUMN sys_user_favorite_menu.sort_order IS '排序号';
COMMENT ON COLUMN sys_user_favorite_menu.system_id IS '系统ID';
COMMENT ON COLUMN sys_user_favorite_menu.menu_id IS '菜单ID';
COMMENT ON COLUMN sys_user_favorite_menu.create_time IS '创建时间';
COMMENT ON COLUMN sys_user_favorite_menu.update_time IS '更新时间';
COMMENT ON COLUMN sys_user_favorite_menu.status IS '状态 1:启用 0:禁用';
COMMENT ON COLUMN sys_user_favorite_menu.is_pinned IS '是否PIN置顶 1:是 0:否';
COMMENT ON COLUMN sys_user_favorite_menu.pin_order IS 'PIN排序号';
COMMENT ON COLUMN sys_user_favorite_menu.remark IS '备注';

-- 创建唯一约束和索引
ALTER TABLE sys_user_favorite_menu ADD CONSTRAINT uk_user_menu UNIQUE (username, menu_path);
CREATE INDEX idx_username ON sys_user_favorite_menu(username);
CREATE INDEX idx_menu_path ON sys_user_favorite_menu(menu_path);
CREATE INDEX idx_sort_order ON sys_user_favorite_menu(sort_order);
CREATE INDEX idx_pin_status ON sys_user_favorite_menu(is_pinned, pin_order);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
CREATE TRIGGER update_sys_user_favorite_menu_modtime
    BEFORE UPDATE ON sys_user_favorite_menu
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_column();

-- 插入示例数据（可选）
INSERT INTO sys_user_favorite_menu (username, menu_path, menu_name, menu_icon, sort_order, system_id, status, is_pinned, pin_order) VALUES
('admin', '/oa/bpm/processInstance', 'OA审批', 'FlowerOutline', 1, 16, '1', '1', 1),
('admin', '/ams/property/amsProperty', '资产管理', 'CubeOutline', 2, 11, '1', '1', 2),
('admin', '/pms/pmsProject', '绩效管理', 'BarChartOutline', 3, 4, '1', '0', NULL),
('admin', '/hrm/emp/employeeInfo', '人员管理', 'PeopleOutline', 4, 2, '1', '0', NULL),
('admin', '/sys/sysConfig', '系统配置', 'SettingsOutline', 5, 1, '1', '0', NULL),
('admin', '/cms/contractManager/contractControl', '合同管理', 'DocumentTextOutline', 6, 5, '1', '0', NULL);
