<template>
  <div class="desktop-demo-page">
    <j-container>
      <template #content>
        <div class="demo-content">
          <!-- 页面标题 -->
          <div class="demo-header mb-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">移动端适配演示 - 桌面版</h1>
            <p class="text-gray-600">这是桌面端版本，展示与移动端的差异</p>
          </div>

          <!-- 设备信息 -->
          <n-card class="mb-6">
            <template #header>
              <div class="flex items-center gap-2">
                <n-icon size="20" class="text-blue-500">
                  <DesktopOutline />
                </n-icon>
                <span>设备信息</span>
              </div>
            </template>

            <n-descriptions :column="3" bordered>
              <n-descriptions-item label="设备类型">
                <n-tag type="info">桌面端</n-tag>
              </n-descriptions-item>
              <n-descriptions-item label="屏幕尺寸">
                {{ screenInfo.width }} × {{ screenInfo.height }}
              </n-descriptions-item>
              <n-descriptions-item label="触摸设备">
                <n-tag type="default">否</n-tag>
              </n-descriptions-item>
            </n-descriptions>
          </n-card>

          <!-- 组件对比 -->
          <n-grid :cols="2" :x-gap="24" :y-gap="16">
            <!-- 左侧：桌面端组件 -->
            <n-grid-item>
              <n-card>
                <template #header>
                  <div class="flex items-center gap-2">
                    <n-icon size="20" class="text-green-500">
                      <DesktopOutline />
                    </n-icon>
                    <span>桌面端组件</span>
                  </div>
                </template>

                <n-space vertical size="large">
                  <!-- 按钮组 -->
                  <div>
                    <h4 class="mb-3 font-medium">按钮尺寸 (Small)</h4>
                    <n-space>
                      <n-button size="small" type="primary">主要</n-button>
                      <n-button size="small" type="default">默认</n-button>
                      <n-button size="small" type="tertiary">次要</n-button>
                    </n-space>
                  </div>

                  <!-- 输入框 -->
                  <div>
                    <h4 class="mb-3 font-medium">输入组件 (Small)</h4>
                    <n-space vertical>
                      <n-input size="small" placeholder="桌面端输入框" />
                      <n-select size="small" placeholder="桌面端选择器" :options="selectOptions" />
                    </n-space>
                  </div>

                  <!-- 表格 -->
                  <div>
                    <h4 class="mb-3 font-medium">数据表格 (Small)</h4>
                    <j-n-data-table size="small" :columns="tableColumns" :data="tableData" :pagination="false" />
                  </div>
                </n-space>
              </n-card>
            </n-grid-item>

            <!-- 右侧：移动端组件预览 -->
            <n-grid-item>
              <n-card>
                <template #header>
                  <div class="flex items-center gap-2">
                    <n-icon size="20" class="text-orange-500">
                      <PhonePortraitOutline />
                    </n-icon>
                    <span>移动端组件预览</span>
                  </div>
                </template>

                <n-space vertical size="large">
                  <!-- 按钮组 -->
                  <div>
                    <h4 class="mb-3 font-medium">按钮尺寸 (Large)</h4>
                    <n-space>
                      <n-button size="large" type="primary">主要</n-button>
                      <n-button size="large" type="default">默认</n-button>
                      <n-button size="large" type="tertiary">次要</n-button>
                    </n-space>
                  </div>

                  <!-- 输入框 -->
                  <div>
                    <h4 class="mb-3 font-medium">输入组件 (Large)</h4>
                    <n-space vertical>
                      <n-input size="large" placeholder="移动端输入框" />
                      <n-select size="large" placeholder="移动端选择器" :options="selectOptions" />
                    </n-space>
                  </div>

                  <!-- 表格 -->
                  <div>
                    <h4 class="mb-3 font-medium">数据表格 (Medium)</h4>
                    <j-n-data-table size="medium" :columns="tableColumns" :data="tableData" :pagination="false" />
                  </div>
                </n-space>
              </n-card>
            </n-grid-item>
          </n-grid>

          <!-- 功能说明 -->
          <n-card class="mt-6">
            <template #header>
              <div class="flex items-center gap-2">
                <n-icon size="20" class="text-purple-500">
                  <InformationCircleOutline />
                </n-icon>
                <span>移动端适配说明</span>
              </div>
            </template>

            <n-space vertical size="large">
              <n-alert type="info">
                <template #icon>
                  <n-icon><InformationCircleOutline /></n-icon>
                </template>
                <strong>自动组件加载：</strong>
                当在移动端访问时，系统会自动加载 <code>mobile-demo-mob.vue</code> 组件， 而在桌面端则加载当前的
                <code>mobile-demo.vue</code> 组件。
              </n-alert>

              <div class="feature-list">
                <h4 class="mb-3 font-medium">移动端适配特性：</h4>
                <ul class="space-y-2 text-sm text-gray-600">
                  <li class="flex items-start gap-2">
                    <span class="text-green-500 mt-1">✓</span>
                    <span>自动设备检测和布局切换</span>
                  </li>
                  <li class="flex items-start gap-2">
                    <span class="text-green-500 mt-1">✓</span>
                    <span>响应式组件尺寸（移动端使用更大的触摸目标）</span>
                  </li>
                  <li class="flex items-start gap-2">
                    <span class="text-green-500 mt-1">✓</span>
                    <span>移动端专用布局（底部导航 + 顶部标题栏）</span>
                  </li>
                  <li class="flex items-start gap-2">
                    <span class="text-green-500 mt-1">✓</span>
                    <span>触摸友好的交互设计</span>
                  </li>
                  <li class="flex items-start gap-2">
                    <span class="text-green-500 mt-1">✓</span>
                    <span>安全区域适配（支持刘海屏等）</span>
                  </li>
                  <li class="flex items-start gap-2">
                    <span class="text-green-500 mt-1">✓</span>
                    <span>TailwindCSS 响应式断点配置</span>
                  </li>
                </ul>
              </div>

              <n-space>
                <n-button type="primary" @click="handleViewMobile"> 查看移动端效果 </n-button>
                <n-button type="default" @click="handleViewDocs"> 查看文档 </n-button>
              </n-space>
            </n-space>
          </n-card>
        </div>
      </template>
    </j-container>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue'
  import {
    NCard,
    NSpace,
    NTag,
    NIcon,
    NButton,
    NInput,
    NSelect,
    NDataTable,
    NDescriptions,
    NDescriptionsItem,
    NGrid,
    NGridItem,
    NAlert,
  } from 'naive-ui'
  import { PhonePortraitOutline, DesktopOutline, InformationCircleOutline } from '@vicons/ionicons5'
  import { getScreenInfo } from '@/utils/device'

  // 响应式数据
  const screenInfo = ref(getScreenInfo())

  // 选择器选项
  const selectOptions = [
    { label: '选项一', value: 'option1' },
    { label: '选项二', value: 'option2' },
    { label: '选项三', value: 'option3' },
  ]

  // 表格配置
  const tableColumns = [
    { title: '姓名', key: 'name', width: 100 },
    { title: '年龄', key: 'age', width: 80 },
    { title: '地址', key: 'address' },
  ]

  const tableData = [
    { name: '张三', age: 25, address: '北京市朝阳区' },
    { name: '李四', age: 30, address: '上海市浦东新区' },
    { name: '王五', age: 28, address: '广州市天河区' },
  ]

  // 方法
  const handleViewMobile = () => {
    window.$message?.info('请在移动设备或浏览器开发者工具的移动端模式下查看移动端效果')
  }

  const handleViewDocs = () => {
    window.open('/docs/mobile-adaptation.md', '_blank')
  }

  // 生命周期
  onMounted(() => {
    const updateScreenInfo = () => {
      screenInfo.value = getScreenInfo()
    }

    window.addEventListener('resize', updateScreenInfo)

    onUnmounted(() => {
      window.removeEventListener('resize', updateScreenInfo)
    })
  })
</script>

<style scoped>
  @reference "tailwindcss";

  .desktop-demo-page {
    @apply min-h-screen bg-gray-50;
  }

  .demo-content {
    @apply max-w-7xl mx-auto;
  }

  .demo-header {
    @apply text-center py-8;
  }

  .feature-list ul {
    @apply pl-0;
  }

  .feature-list li {
    @apply list-none;
  }

  code {
    @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono;
  }
</style>
