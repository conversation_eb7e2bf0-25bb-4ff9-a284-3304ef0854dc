# 绩效管理系统主页设计文档 📊

## 概述

为绩效管理系统(PMS)设计了一个美观高效的主页快速启动菜单，基于 Naive UI 组件库实现，提供直观的导航和数据展示功能。

## 🎨 设计特色

### 1. 响应式布局
- 采用 24 栅格系统，左中右三栏布局
- 左侧(8栅格)：快捷入口和常用配置
- 中间(10栅格)：功能模块导航和数据处理流程
- 右侧(6栅格)：待办事项和系统状态

### 2. 统计卡片
顶部展示四个关键指标：
- **科室总数**：已配置科室数量
- **本月任务**：待处理任务数量
- **计算进度**：本月绩效计算完成度
- **待审核**：待审核的绩效申请数量

### 3. 快捷入口
提供 6 个核心功能的快速访问：
- 绩效计算
- 信息采集
- 人数上报
- 绩效查询
- 成本管控
- 数据采集

## 🏗️ 组件架构

```
src/views/modules/home/<USER>/
├── index.vue                    # 主页面
└── components/
    ├── ShortcutEntry.vue        # 快捷入口组件
    ├── ConfigShortcuts.vue      # 常用配置组件
    ├── ModuleNavigation.vue     # 功能模块导航
    ├── ProcessFlow.vue          # 数据处理流程
    ├── TodoList.vue             # 待办事项列表
    └── SystemStatus.vue         # 系统状态监控
```

## 📋 功能模块

### 1. 配置管理
- 科室数据配置
- 收费项目配置
- 资产收费配置
- 医嘱项目配置

### 2. 成本管控
- 成本管控采集配置
- 成本管控数据采集
- 医疗服务收入
- 成本管控报表

### 3. 绩效配置与模板
- 绩效计算科室模版配置
- 绩效信息采集配置
- 绩效奖计算项目配置
- 奖励系数配置

### 4. 绩效数据处理
- 绩效信息采集
- 科室月度绩效计算
- 月度绩效确认申请
- 科室每月人数上报

## 🎯 交互设计

### 界面优化
- **清晰边框**：所有模块都有明显的边框分界
- **统一样式**：采用一致的圆角、阴影和间距
- **简洁交互**：移除了过多的悬停效果，保持界面稳定

### 状态指示
- **已完成**：绿色标签和图标
- **进行中**：橙色标签和图标
- **待处理**：灰色标签和图标

### 优先级标识
- **高优先级**：红色圆点
- **中优先级**：橙色圆点
- **低优先级**：绿色圆点

## 🎨 样式规范

### 颜色主题
- 主色调：`#409EFF` (蓝色)
- 成功色：`#67C23A` (绿色)
- 警告色：`#E6A23C` (橙色)
- 危险色：`#F56C6C` (红色)
- 信息色：`#909399` (灰色)

### 间距规范
- 卡片间距：16px
- 内容边距：12px-20px
- 图标大小：16px-32px

## 🔧 技术实现

### 核心技术栈
- **Vue 3** + **TypeScript**
- **Naive UI** 组件库
- **@vicons/ionicons5** 图标库
- **SCSS** 样式预处理

### 响应式设计
- 使用 `n-grid` 实现栅格布局
- 支持不同屏幕尺寸自适应
- 移动端友好的触摸交互

## 📊 数据流

```mermaid
graph TD
    A[主页面] --> B[统计数据]
    A --> C[快捷入口]
    A --> D[功能模块]
    A --> E[待办事项]
    A --> F[系统状态]

    C --> G[路由跳转]
    D --> G
    E --> H[任务管理]
    F --> I[状态监控]
```

## 🚀 使用指南

### 快速导航
1. 点击顶部统计卡片查看详细数据
2. 使用左侧快捷入口快速访问核心功能
3. 通过中间模块导航浏览所有功能
4. 查看右侧待办事项了解工作进度

### 自定义配置
- 可根据用户角色显示不同的快捷入口
- 支持个性化的待办事项筛选
- 可配置系统状态监控项目

## 🔮 扩展性

### 组件复用
- 所有子组件都支持独立使用
- 通过 props 传递数据，便于扩展
- 统一的样式规范，易于维护

### 功能扩展
- 支持添加新的快捷入口
- 可扩展更多统计指标
- 支持自定义主题色彩

## 📝 维护说明

### 数据更新
- 统计数据可通过 API 实时更新
- 待办事项支持增删改查操作
- 系统状态支持实时监控

### 性能优化
- 组件懒加载
- 图标按需引入
- 样式模块化管理

---

*该主页设计遵循现代 Web 设计原则，注重用户体验和视觉美观，为绩效管理系统提供了高效的工作入口。*
