# HRM员工信息表分析报告

## 📋 表结构概览

### 基本信息
- **表名**: `hrm_employee_info`
- **总字段数**: 59个字段
- **主键**: `id` (自增整型)
- **业务主键**: `emp_code` (员工编号)

### 字段分类

#### 🔑 核心标识字段
- `id`: 主键ID (integer, NOT NULL)
- `emp_code`: 员工编号 (varchar)
- `emp_name`: 员工姓名 (varchar)

#### 🏢 组织关联字段
- `hospital_id`: 医院ID (varchar)
- `org_id`: 部门ID (varchar)
- `ajt_org_ids`: 兼任科室 (varchar)

#### 👤 个人基础信息
- `sex`: 性别 (varchar)
- `age`: 年龄 (integer)
- `birthday`: 出生日期 (varchar)
- `phone`: 手机号 (varchar)
- `email`: 邮箱 (varchar)
- `icd_card`: 身份证号 (varchar)
- `icd_card_type`: 证件类型 (varchar)

#### 💼 职业信息
- `emp_type`: 员工类型 (varchar)
- `job`: 职务 (varchar)
- `job_type`: 工作状态 (varchar)
- `status`: 员工状态 (varchar)

#### 📅 时间相关字段
- `join_job_time`: 参加工作时间 (varchar)
- `to_company_time`: 入职时间 (varchar)
- `start_time`: 状态开始时间 (varchar)
- `end_time`: 状态结束时间 (varchar)
- `status_chg_date`: 状态变更日期 (varchar)
- `leave_date`: 请假日期 (varchar)
- `exit_date`: 离职日期 (varchar)
- `retire_date`: 退休日期 (varchar)

## 📊 数据统计分析

### 总体概况
- **总员工数**: 1,314人
- **活跃部门数**: 91个
- **平均部门规模**: 14.4人/部门

### 员工状态分布
| 状态码 | 状态描述 | 人数 | 占比 |
|--------|----------|------|------|
| 398 | 正式员工 | 1,276 | 97.11% |
| 397 | 试用期 | 17 | 1.29% |
| 396 | 其他状态 | 11 | 0.84% |
| 404 | 状态4 | 5 | 0.38% |
| 405 | 状态5 | 3 | 0.23% |
| 399 | 状态6 | 1 | 0.08% |

### 员工类型分布
| 类型码 | 人数 | 占比 |
|--------|------|------|
| 18 | 579 | 44.1% |
| 21 | 378 | 28.8% |
| 20 | 127 | 9.7% |
| 23 | 82 | 6.2% |
| 24 | 71 | 5.4% |
| 其他 | 77 | 5.8% |

### 部门人员分布TOP10
| 部门ID | 人数 |
|--------|------|
| 207 | 57 |
| 203006001 | 50 |
| 136001 | 49 |
| 227001 | 45 |
| 312 | 44 |
| 318001 | 43 |
| 205001 | 40 |
| 304 | 40 |
| 203002001 | 38 |
| 221001 | 36 |

## 📈 数据质量评估

### 完整性指标
- ✅ **员工编号覆盖率**: 100% (所有记录都有emp_code)
- ✅ **部门信息覆盖率**: 93.1% (大部分员工有部门归属)
- ✅ **状态信息覆盖率**: 100% (所有员工都有状态标识)
- ⚠️ **员工类型覆盖率**: 95.4% (少数员工缺少类型信息)

### 数据一致性
- 主键完整性: ✅ 良好
- 外键关联性: ⚠️ 需要验证与组织表的关联
- 业务规则: ✅ 员工状态分布合理

## 🔍 业务洞察

### 关键发现
1. **高正式化率**: 97.11%的员工为正式员工，组织稳定性良好
2. **试用期管理**: 仅1.29%的试用期员工，转正流程高效
3. **部门集中度**: 前10个部门集中了约1/3的员工
4. **员工类型**: 主要集中在18、21两个类型，占比超过70%

### 管理建议
1. **试用期跟踪**: 建立试用期员工转正跟踪机制
2. **部门平衡**: 关注大型部门的管理效率和小型部门的资源配置
3. **数据完善**: 补充缺失的员工类型信息
4. **档案管理**: 完善员工档案信息的数字化管理

## 🛠️ 技术建议

### 索引优化
```sql
-- 建议添加的索引
CREATE INDEX idx_hrm_employee_org_id ON hrm_employee_info(org_id);
CREATE INDEX idx_hrm_employee_status ON hrm_employee_info(status);
CREATE INDEX idx_hrm_employee_emp_type ON hrm_employee_info(emp_type);
CREATE INDEX idx_hrm_employee_emp_code ON hrm_employee_info(emp_code);
```

### 数据清理
```sql
-- 清理建议查询
-- 1. 检查重复员工编号
SELECT emp_code, COUNT(*) FROM hrm_employee_info 
WHERE emp_code IS NOT NULL 
GROUP BY emp_code HAVING COUNT(*) > 1;

-- 2. 检查无效部门关联
SELECT DISTINCT org_id FROM hrm_employee_info 
WHERE org_id IS NOT NULL AND org_id NOT IN (
    SELECT org_id FROM hrm_organization
);
```

## 📋 维护清单

### 定期检查项目
- [ ] 员工状态更新及时性
- [ ] 部门调动记录完整性  
- [ ] 试用期员工转正跟踪
- [ ] 离职员工数据归档
- [ ] 兼任科室信息准确性

---
*报告生成时间: 2024年*
*数据来源: hrm_employee_info表*

```mermaid
erDiagram
    hrm_employee_info {
        integer id PK "主键ID"
        varchar emp_code "员工编号"
        varchar emp_name "员工姓名"
        varchar hospital_id "医院ID"
        varchar org_id "部门ID"
        varchar emp_type "员工类型"
        varchar job "职务"
        varchar status "员工状态"
        varchar sex "性别"
        integer age "年龄"
        varchar phone "手机号"
        varchar email "邮箱"
        varchar icd_card "身份证号"
        varchar icd_card_type "证件类型"
        varchar birthday "出生日期"
        varchar join_job_time "参加工作时间"
        varchar to_company_time "入职时间"
        varchar start_time "状态开始时间"
        varchar end_time "状态结束时间"
        varchar blood_type "血型"
        varchar marriage_condition "婚姻状况"
        varchar mz "民族"
        varchar politics_status "政治面貌"
        varchar job_type "工作状态"
        varchar enter_channel "入职渠道"
        varchar health_condition "健康状况"
        varchar native_place "籍贯"
        varchar birth_place "出生地"
        varchar communication_address "通讯地址"
        varchar hobby "爱好特长"
        varchar blacklist "黑名单"
        varchar portrait_path "头像路径"
        varchar ajt_org_ids "兼任科室"
        varchar archive_num "档案号"
        varchar archive_location "档案位置"
        varchar create_time "创建时间"
        varchar crter "创建人"
        integer is_deleted "删除标记"
        varchar status_chg_date "状态变更日期"
        varchar leave_date "请假日期"
        varchar exit_date "离职日期"
        varchar retire_date "退休日期"
    }
    
    hrm_organization {
        varchar org_id PK "部门ID"
        varchar org_name "部门名称"
        varchar org_code "部门代码"
        varchar parent_id "父部门ID"
    }
    
    hrm_hospital {
        varchar hospital_id PK "医院ID"
        varchar hospital_name "医院名称"
    }
    
    hrm_employee_info ||--o{ hrm_organization : "belongs_to"
    hrm_employee_info ||--o{ hrm_hospital : "works_in"
```


```mermaid
graph TB
    subgraph "📊 HRM员工信息总览"
        A[总员工数: 1,314人] --> B[活跃部门: 91个]
        A --> C[在职状态分布]
        A --> D[员工类型分布]
        A --> E[部门人员分布]
    end
    
    subgraph "👥 员工状态统计"
        C --> C1[正式员工: 1,276人<br/>97.11%]
        C --> C2[试用期: 17人<br/>1.29%]
        C --> C3[其他状态: 21人<br/>1.6%]
        
        C1 -.-> C1A[状态码: 398]
        C2 -.-> C2A[状态码: 397]
        C3 -.-> C3A[离职/退休等]
    end
    
    subgraph "🏢 员工类型分析"
        D --> D1[类型18: 579人<br/>44.1%]
        D --> D2[类型21: 378人<br/>28.8%]
        D --> D3[类型20: 127人<br/>9.7%]
        D --> D4[类型23: 82人<br/>6.2%]
        D --> D5[其他类型: 148人<br/>11.2%]
    end
    
    subgraph "🏥 部门人员TOP10"
        E --> E1[部门207: 57人]
        E --> E2[部门203006001: 50人]
        E --> E3[部门136001: 49人]
        E --> E4[部门227001: 45人]
        E --> E5[部门312: 44人]
        E --> E6[部门318001: 43人]
        E --> E7[部门205001: 40人]
        E --> E8[部门304: 40人]
        E --> E9[部门203002001: 38人]
        E --> E10[部门221001: 36人]
    end
    
    subgraph "📈 数据质量指标"
        F[数据完整性检查]
        F --> F1[员工编号覆盖率: 100%]
        F --> F2[部门信息覆盖率: 93.1%]
        F --> F3[状态信息覆盖率: 100%]
        F --> F4[员工类型覆盖率: 95.4%]
    end
    
    subgraph "🔍 关键业务指标"
        G[业务分析]
        G --> G1[平均部门规模: 14.4人]
        G --> G2[正式化率: 97.11%]
        G --> G3[试用期转化待跟踪]
        G --> G4[大型部门集中度高]
    end
    
    style A fill:#e1f5fe
    style C1 fill:#c8e6c9
    style C2 fill:#fff3e0
    style C3 fill:#ffebee
    style D1 fill:#e8f5e8
    style D2 fill:#f3e5f5
    style E1 fill:#e3f2fd
    style F1 fill:#e8f5e8
    style G2 fill:#c8e6c9
```