# 移动端适配快速开始 🚀

本指南将帮助你快速上手项目的移动端适配功能，5分钟内让你的页面支持移动端！

## 🎯 快速体验

### 1. 查看演示页面
访问演示页面查看移动端适配效果：
- 桌面端：`/demo/mobile-demo`
- 移动端：自动加载 `mobile-demo-mob.vue`

### 2. 浏览器测试
1. 打开浏览器开发者工具 (F12)
2. 点击设备模拟器图标 📱
3. 选择移动设备（如iPhone、Android）
4. 刷新页面查看移动端布局

## ⚡ 5分钟创建移动端页面

### 步骤1：创建移动端组件
```bash
# 在你的模块目录下创建移动端专用组件
touch src/views/modules/your-module/index-mob.vue
```

### 步骤2：编写移动端组件
```vue
<!-- src/views/modules/your-module/index-mob.vue -->
<template>
  <div class="mobile-page p-4">
    <!-- 使用响应式主题 -->
    <div :class="deviceClasses">
      <h1 class="text-xl-mobile font-bold mb-4">移动端页面</h1>
      
      <!-- 触摸友好的按钮 -->
      <n-button 
        :size="componentSizes.button"
        type="primary"
        class="w-full h-touch mb-4"
      >
        移动端按钮
      </n-button>
      
      <!-- 响应式输入框 -->
      <n-input 
        :size="componentSizes.input"
        placeholder="移动端输入框"
        class="mb-4"
      />
      
      <!-- 移动端友好的卡片 -->
      <n-card class="mb-4">
        <h3 class="text-lg-mobile font-semibold mb-2">卡片标题</h3>
        <p class="text-base-mobile text-gray-600">
          这是一个移动端优化的卡片内容
        </p>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { useResponsiveTheme } from '@/composables/useResponsiveTheme'

// 使用响应式主题
const { deviceClasses, componentSizes } = useResponsiveTheme()
</script>

<style scoped>
.mobile-page {
  min-height: calc(100vh - 120px);
  -webkit-overflow-scrolling: touch;
}
</style>
```

### 步骤3：创建对应的桌面端组件（可选）
```vue
<!-- src/views/modules/your-module/index.vue -->
<template>
  <j-container>
    <template #content>
      <h1 class="text-2xl font-bold mb-4">桌面端页面</h1>
      
      <n-button size="small" type="primary">
        桌面端按钮
      </n-button>
      
      <n-input size="small" placeholder="桌面端输入框" />
    </template>
  </j-container>
</template>
```

### 步骤4：测试效果
1. 在桌面端访问页面 → 显示 `index.vue`
2. 在移动端访问页面 → 自动显示 `index-mob.vue`

## 🛠️ 常用工具函数

### 设备检测
```typescript
import { isMobile, isTablet, isDesktop, isTouchDevice } from '@/utils/device'

// 在组件中使用
if (isMobile()) {
  console.log('当前是移动端')
}

if (isTouchDevice()) {
  console.log('当前是触摸设备')
}
```

### 响应式主题
```typescript
import { useResponsiveTheme } from '@/composables/useResponsiveTheme'

const {
  isMobile,           // 是否移动端
  isTablet,           // 是否平板
  isDesktop,          // 是否桌面端
  deviceClasses,      // 设备CSS类名
  componentSizes,     // 组件尺寸配置
  spacingConfig,      // 间距配置
  fontConfig          // 字体配置
} = useResponsiveTheme()
```

## 🎨 快速样式类

### TailwindCSS 响应式类
```html
<!-- 移动端显示，桌面端隐藏 -->
<div class="mobile:block desktop:hidden">移动端内容</div>

<!-- 桌面端显示，移动端隐藏 -->
<div class="mobile:hidden desktop:block">桌面端内容</div>

<!-- 触摸友好的尺寸 -->
<button class="h-touch w-full">触摸按钮</button>

<!-- 移动端友好的字体 -->
<h1 class="text-xl-mobile">移动端标题</h1>
<p class="text-base-mobile">移动端正文</p>
```

### 全局响应式类
```html
<!-- 设备特定显示 -->
<div class="mobile-only">仅移动端显示</div>
<div class="tablet-only">仅平板显示</div>
<div class="desktop-only">仅桌面端显示</div>
```

## 📱 移动端布局特性

### 自动布局切换
- **移动端**：底部导航 + 顶部标题栏
- **桌面端**：侧边栏 + 顶部导航

### 移动端布局组件
```vue
<template>
  <!-- 移动端会自动使用 MobileLayout -->
  <router-view />
</template>
```

### 自定义顶部操作
```vue
<template>
  <MobileLayout>
    <template #header-actions>
      <n-button size="small" circle>
        <n-icon><SettingsOutline /></n-icon>
      </n-button>
    </template>
  </MobileLayout>
</template>
```

## 🔧 高级配置

### 自定义断点
```javascript
// tailwind.config.js
export default {
  theme: {
    extend: {
      screens: {
        'xs': '480px',
        'mobile': {'max': '767px'},
        'tablet': {'min': '768px', 'max': '1023px'},
        'desktop': {'min': '1024px'},
      }
    }
  }
}
```

### 自定义组件尺寸
```typescript
// 在组件中覆盖默认尺寸
const customSizes = computed(() => {
  if (isMobile.value) {
    return {
      button: 'large',
      input: 'large',
      // ... 其他配置
    }
  }
  return componentSizes.value
})
```

## 🐛 常见问题

### Q: 移动端组件没有自动加载？
A: 确保文件命名正确：`index-mob.vue` 或 `index-mob.tsx`

### Q: 样式在移动端显示异常？
A: 检查是否使用了响应式断点类，如 `mobile:p-4`

### Q: 触摸事件不响应？
A: 添加 `touch-device` 类或使用 `touch-action: manipulation`

### Q: 输入框在iOS上被放大？
A: 确保输入框字体大小至少16px：`font-size: 16px`

## 📚 更多资源

- [完整移动端适配指南](./mobile-adaptation.md)
- [TailwindCSS 响应式设计](https://tailwindcss.com/docs/responsive-design)
- [NaiveUI 组件文档](https://www.naiveui.com/)

## 🎉 完成！

现在你已经掌握了移动端适配的基础知识，可以开始创建移动端友好的页面了！

如果遇到问题，请查看完整的[移动端适配指南](./mobile-adaptation.md)或联系开发团队。
