<template>
  <div class="p-4 space-y-4">
    <n-card title="自适应数据表格演示" size="small">
      <template #header-extra>
        <n-space>
          <n-tag :type="isMobileDevice ? 'success' : 'info'">
            {{ isMobileDevice ? '移动端' : 'PC端' }}
          </n-tag>
          <n-button size="small" @click="refreshData">
            刷新数据
          </n-button>
        </n-space>
      </template>

      <!-- 自适应数据表格 -->
      <adaptive-data-table
        :data="tableData"
        :columns="tableColumns"
        :loading="loading"
        :pagination="paginationConfig"
        row-key="id"
        mobile-title="员工列表"
        :card-columns="2"
        :show-actions="true"
        @row-click="handleRowClick"
      >
        <!-- 操作按钮插槽 -->
        <template #actions="{ row }">
          <n-space :size="4">
            <n-button size="tiny" type="primary">
              编辑
            </n-button>
            <n-button size="tiny" type="error">
              删除
            </n-button>
          </n-space>
        </template>
      </adaptive-data-table>
    </n-card>

    <!-- 功能说明 -->
    <n-card title="功能说明" size="small">
      <n-list>
        <n-list-item>
          <n-thing title="PC端体验">
            完全继承 NaiveUI DataTable 的所有功能，保持原有体验
          </n-thing>
        </n-list-item>
        <n-list-item>
          <n-thing title="移动端适配">
            自动切换为卡片视图，支持视图切换和配置
          </n-thing>
        </n-list-item>
        <n-list-item>
          <n-thing title="响应式设计">
            根据屏幕尺寸自动调整布局和交互方式
          </n-thing>
        </n-list-item>
        <n-list-item>
          <n-thing title="现代设计">
            iOS风格的卡片设计，毛玻璃效果和触摸反馈
          </n-thing>
        </n-list-item>
      </n-list>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, onMounted, h } from 'vue'
import {
  NCard,
  NSpace,
  NButton,
  NTag,
  NList,
  NListItem,
  NThing,
  useMessage
} from 'naive-ui'
import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'

// 移动端检测
const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice') || ref(false)
const message = useMessage()

// 响应式数据
const loading = ref(false)

// 模拟数据
const tableData = ref([
  {
    id: 1,
    name: '张三',
    age: 28,
    department: '技术部',
    position: '前端工程师',
    salary: 15000,
    status: '在职',
    joinDate: '2023-01-15',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: 'https://avatars.githubusercontent.com/u/1?v=4'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    department: '产品部',
    position: '产品经理',
    salary: 18000,
    status: '在职',
    joinDate: '2022-08-20',
    email: '<EMAIL>',
    phone: '13800138002',
    avatar: 'https://avatars.githubusercontent.com/u/2?v=4'
  },
  {
    id: 3,
    name: '王五',
    age: 26,
    department: '设计部',
    position: 'UI设计师',
    salary: 12000,
    status: '试用期',
    joinDate: '2023-06-01',
    email: '<EMAIL>',
    phone: '13800138003',
    avatar: 'https://avatars.githubusercontent.com/u/3?v=4'
  },
  {
    id: 4,
    name: '赵六',
    age: 30,
    department: '运营部',
    position: '运营专员',
    salary: 13000,
    status: '在职',
    joinDate: '2022-12-10',
    email: '<EMAIL>',
    phone: '13800138004',
    avatar: 'https://avatars.githubusercontent.com/u/4?v=4'
  }
])

// 列配置
const tableColumns = ref([
  {
    key: 'name',
    title: '姓名',
    width: 100,
    mobileTitle: true,  // 移动端主标题
    mobileOrder: 1,
    render: (row: any) => {
      return h('div', { class: 'flex items-center space-x-2' }, [
        h('img', {
          src: row.avatar,
          alt: row.name,
          class: 'w-8 h-8 rounded-full'
        }),
        h('span', { class: 'font-semibold' }, row.name)
      ])
    }
  },
  {
    key: 'position',
    title: '职位',
    width: 120,
    mobileSubtitle: true,  // 移动端副标题
    mobileOrder: 2
  },
  {
    key: 'status',
    title: '状态',
    width: 80,
    mobilePosition: 'header',  // 移动端头部显示
    mobileOrder: 3,
    render: (row: any) => {
      const type = row.status === '在职' ? 'success' : 'warning'
      return h(NTag, { type, size: 'small' }, () => row.status)
    }
  },
  {
    key: 'age',
    title: '年龄',
    width: 80,
    mobileOrder: 4,
    render: (row: any) => `${row.age}岁`
  },
  {
    key: 'department',
    title: '部门',
    width: 100,
    mobileOrder: 5
  },
  {
    key: 'salary',
    title: '薪资',
    width: 100,
    mobileOrder: 6,
    render: (row: any) => {
      return h('span', { class: 'text-green-600 font-semibold' },
        `¥${row.salary.toLocaleString()}`
      )
    }
  },
  {
    key: 'joinDate',
    title: '入职日期',
    width: 120,
    mobilePosition: 'footer',  // 移动端底部显示
    mobileOrder: 7,
    render: (row: any) => {
      return h('span', { class: 'text-gray-500 text-sm' },
        `入职: ${row.joinDate}`
      )
    }
  },
  {
    key: 'email',
    title: '邮箱',
    width: 180,
    mobileShow: false,  // 移动端隐藏
    mobileOrder: 8
  },
  {
    key: 'phone',
    title: '电话',
    width: 120,
    mobileOrder: 9
  }
])

// 分页配置
const paginationConfig = computed(() => ({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [5, 10, 20],
  showQuickJumper: true,
  total: tableData.value.length
}))

// 事件处理
const handleRowClick = (row: any, index: number) => {
  message.info(`点击了 ${row.name} (第${index + 1}行)`)
}

const refreshData = async () => {
  loading.value = true
  
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 随机打乱数据
  tableData.value = [...tableData.value].sort(() => Math.random() - 0.5)
  
  loading.value = false
  message.success('数据已刷新')
}

onMounted(() => {
  message.info(`当前设备: ${(isMobileDevice as any)?.value ? '移动端' : 'PC端'}`)
})
</script>

<style scoped lang="less">
// 演示页面样式
</style>
