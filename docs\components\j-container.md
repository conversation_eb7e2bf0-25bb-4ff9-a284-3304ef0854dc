---
description: 
globs: 
alwaysApply: false
---
# j-container 组件

## 📝 组件概述
`j-container` 组件是一个通用的布局容器，用于构建包含查询表单、操作按钮、内容区域和分页的页面结构。它提供了灵活的配置选项和插槽，以适应不同的布局需求。

## 🔧 Props 属性

### 一、布局与模式 (Layout & Mode)
- `leftSideMode: Boolean` (默认: `false`)
  - **说明**: 是否启用左侧边栏模式。启用后，可以通过 `#leftSide` 插槽自定义左侧内容。
- `leftSideWidth: String` (默认: `'20%'`)
  - **说明**: 当 `leftSideMode` 为 `true` 时，左侧边栏的宽度。
- `showHeader: Boolean` (默认: `true`)
  - **说明**: 是否显示容器的头部区域（通常包含查询表单和操作按钮）。

### 二、查询表单配置 (Query Form Configuration)
- `queryForm: Object`
  - **说明**: 查询表单的数据对象，与父组件双向绑定 (`v-model:queryForm`)。
- `labelWidth: String` (默认: `'auto'`)
  - **说明**: 查询表单中表单项标签的宽度。
- `hideEmptyFormItems: Boolean` (默认: `false`)
  - **说明**: (此属性在 `j-crud` 中控制，`j-container` 本身不直接使用) 是否隐藏查询表单中的空表单项。
- `showQueryButton: Boolean` (默认: `true`)
  - **说明**: 是否显示查询按钮。
- **动态表单项显示**: 组件内部定义了一些默认的表单项（如时间范围、期号、医疗机构），可以通过 `show<ItemPath>` 形式的 prop 控制其显示，例如 `showTimeRange: Boolean`。

### 三、操作按钮与导出 (Action Buttons & Export)
- `showExport: Boolean` (默认: `false`)
  - **说明**: 是否显示导出按钮。需要配合 `exportConfig` 使用。
- `showExportExcelTemplate: Boolean` (默认: `true`)
  - **说明**: 是否显示导出Excel模板按钮 (当 `showExport` 为 `true` 时生效)。
- `exportConfig: Object` (类型: `ExcelType`)
  - **说明**: 导出功能的配置对象。详见 `j-crud` 组件的 `exportConfig` 说明。
- `showUpload: Boolean` (默认: `false`)
  - **说明**: (此 prop 已定义但似乎未在模板中使用，可能为早期遗留或由父组件如 `j-crud` 控制相关功能)。
- `showDownload: Boolean` (默认: `false`)
  - **说明**: 是否显示"查看手册"按钮，点击会触发 `downloadPdf` 方法尝试打开手册链接。

### 四、分页配置 (Pagination Configuration)
- `pagination: Object`
  - **说明**: 分页配置对象，包含 `show: Boolean` (是否显示分页) 和 `total: Number` (总条数)。
- `defaultPageSize: Number` (默认: `20`)
  - **说明**: 默认的每页显示条数。
- `pageSizes: Array<number>` (默认: `[10, 20, 30, 40, 100]`)
  - **说明**: 可供选择的每页显示条数列表。

### 五、样式与内容调整 (Style & Content Adjustment)
- `calcTableHeight: Boolean` (默认: `true`)
  - **说明**: (此 prop 已定义但主要影响内部高度计算逻辑，对外表现为内容区高度自适应)。
- `calcTabHeight: Boolean` (默认: `true`)
  - **说明**: (此 prop 已定义但主要影响内部高度计算逻辑，当与 Tab 结合使用时)。
- `headerStyle: Object` (默认: `{}`)
  - **说明**: 自定义头部区域的内联样式。
- `contentStyle: Object` (默认: `{}`)
  - **说明**: 自定义内容区域的内联样式。
- `footerStyle: Object` (默认: `{}`)
  - **说明**: 自定义底部区域（分页）的内联样式。

## 📌 插槽 (Slots)

- `#leftSide`: 左侧边栏内容插槽，当 `leftSideMode` 为 `true` 时使用。
- `#extendFormItems`: 用于在默认查询表单项之后扩展自定义表单项。
  ```vue
  <template #extendFormItems>
    <n-form-item label="自定义查询项" path="customField">
      <n-input v-model:value="queryForm.customField" />
    </n-form-item>
  </template>
  ```
- `#extendButtons`: 用于在查询按钮和导出按钮组的左侧（即主要操作按钮区域）扩展自定义按钮。
- `#suffix`: 用于在主要操作按钮组的右侧（"查看手册"按钮旁）扩展自定义按钮或内容。
- `#extendRightHeader`: 用于在容器头部最右侧（全屏按钮图标旁，如果显示）扩展自定义内容。
- `#contentTop`: 内容区域的顶部插槽，位于主内容区上方，可用于放置 Tab 或其他辅助信息。
- `#content`: 主要内容区域插槽，用于放置表格、列表或其他自定义内容。

## 🎯 事件 (Emits)

- `query`: 点击查询按钮或表单回车时触发。
- `update:queryForm (formValue)`: 查询表单 `queryForm` 的数据发生变化时触发 (主要由内部表单项更新引起)。
- `contentHeight (heightString)`: 内容区域计算出的最大高度变化时触发，参数为CSS高度字符串 (如 `'calc(100vh - 200px)'`)。
- `accurateQuery (isAccurate: boolean)`: 点击"精确查询"/"收起查询"按钮时触发，参数表示当前是否为精确查询模式。
- `pageSizeUpdate (pageSize: number)`: 分页组件的每页条数变化时触发。
- `pageNumUpdate (pageNum: number)`: 分页组件的当前页码变化时触发。

## ⚙️ 组件方法 (Exposed Methods - 通过 ref 调用)

获取组件实例: `<j-container ref="containerRef" ... />`

- `containerRef.value.query()`: 手动触发查询操作。
- `containerRef.value.accurateQuery(isAccurate: boolean)`: 手动切换精确查询模式的展开/收起状态。
- `containerRef.value.adjustHeight(exeQuery = true, animation = false, accurateQueryFlag = false, isInit = true)`: 内部方法，用于调整容器各部分高度，通常不需要手动调用。 `exeQuery` 表示调整后是否执行查询。
- `containerRef.value.initFormItems()`: 内部方法，用于初始化表单项，通常不需要手动调用。
- `containerRef.value.getFormItems()`: 获取当前容器配置的表单项数组。
- `containerRef.value.downloadPdf()`: 尝试打开配置的"操作手册"链接。

## 🌟 使用示例

```vue
<template>
  <j-container
    ref="containerRef"
    v-model:queryForm="searchParams"
    :pagination="pageConfig"
    :show-export="canExport"
    :export-config="exportSettings"
    @query="handleSearch"
    @pageSizeUpdate="handleSizeChange"
    @pageNumUpdate="handlePageChange"
  >
    <template #extendFormItems>
      <n-form-item label="活动名称" path="activityName">
        <n-input v-model:value="searchParams.activityName" placeholder="请输入活动名称" />
      </n-form-item>
    </template>

    <template #extendButtons>
      <n-button type="primary" @click="handleAddNew">新增活动</n-button>
    </template>

    <template #content>
      <!-- 表格或其他内容 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
      />
    </template>
  </j-container>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';

const containerRef = ref(null);
const searchParams = reactive({
  activityName: '',
  timeRange: null, // 对应默认的时间范围查询项
  pageNum: 1,
  pageSize: 20,
});

const pageConfig = reactive({
  show: true,
  total: 0,
});

const canExport = ref(true);
const exportSettings = reactive({
  excelName: '活动数据',
  // 其他导出配置
});

const columns = ref([/* 表格列定义 */]);
const tableData = ref([]);
const loading = ref(false);

const handleSearch = () => {
  loading.value = true;
  // 调用API查询数据，更新 tableData 和 pageConfig.total
  console.log('执行查询:', searchParams);
  setTimeout(() => {
    loading.value = false;
    pageConfig.total = 100; // 模拟数据
  }, 1000);
};

const handleSizeChange = (pageSize: number) => {
  searchParams.pageSize = pageSize;
  handleSearch(); // 页码大小改变后重新查询
};

const handlePageChange = (pageNum: number) => {
  searchParams.pageNum = pageNum;
  handleSearch(); // 页码改变后重新查询
};

const handleAddNew = () => {
  console.log('新增活动');
};

// 如果需要手动调用容器方法
// onMounted(() => {
//   containerRef.value?.query(); 
// });
</script>

<style scoped>
/* 你的样式 */
</style>
```

## ⚠️ 注意事项
- **查询表单数据 (`queryForm`)**: `j-container` 期望 `queryForm` 对象中包含 `pageNum` 和 `pageSize` 字段用于分页。
- **默认表单项**: 组件内置了一些默认表单项，如 `timeRange`、`issue`、`hospital`。可以通过如 `showTimeRange`、`showIssue` 等 props 控制它们的显示。这些 props 是动态生成的，其命名规则为 `show` + 首字母大写的路径名 (e.g., `timeRange` -> `showTimeRange`)。
- **高度计算**: 组件内部有复杂的高度计算逻辑，以实现内容区域的自适应。多数情况下不需要干预。
- **与 `j-crud` 的关系**: `j-crud` 组件内部封装并使用了 `j-container` 作为其布局基础。许多在 `j-crud` 中配置的属性（如表单项、导出等）会传递给 `j-container`。因此，在直接使用 `j-container` 时，需要自行处理更多数据交互和状态管理逻辑。

---
description:
globs:
alwaysApply: false
---
# j-container 组件

## 📝 组件概述
`j-container` 组件是一个基础的布局容器组件，用于包裹页面内容，提供统一的内边距、背景色、阴影等样式，支持不同的容器类型和大小。

## 🔧 Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| title | String | '' | 容器标题 |
| type | String | 'default' | 容器类型，可选值: 'default', 'card', 'ghost', 'bordered' |
| size | String | 'medium' | 容器大小，可选值: 'small', 'medium', 'large' |
| bordered | Boolean | true | 是否显示边框 |
| shadow | Boolean | false | 是否显示阴影 |
| padding | [String, Number] | '16px' | 内边距 |
| contentStyle | Object | {} | 内容区域样式 |
| headerStyle | Object | {} | 头部区域样式 |
| footerStyle | Object | {} | 底部区域样式 |
| loading | Boolean | false | 是否显示加载状态 |
| showHeader | Boolean | true | 是否显示头部 |
| showFooter | Boolean | false | 是否显示底部 |
| valueType | String | '' | 容器值类型，用于特定类型的容器展示 |

## 🎯 事件

| 事件名 | 参数 | 说明 |
| --- | --- | --- |
| headerClick | (event: Event) | 头部点击事件 |
| contentClick | (event: Event) | 内容区域点击事件 |
| footerClick | (event: Event) | 底部点击事件 |

## 📋 插槽

| 插槽名 | 说明 |
| --- | --- |
| default | 默认插槽，容器主体内容 |
| header | 自定义头部内容 |
| title | 自定义标题内容 |
| extra | 头部右侧额外内容 |
| footer | 自定义底部内容 |

## 🌟 使用示例

### 基础使用
```vue
<template>
  <j-container title="基本信息">
    <p>这是容器内容</p>
  </j-container>
</template>
```

### 不同类型的容器
```vue
<template>
  <div class="container-demo">
    <j-container title="默认容器" type="default">
      <p>默认类型的容器</p>
    </j-container>
    
    <j-container title="卡片容器" type="card" shadow>
      <p>卡片类型的容器，带阴影</p>
    </j-container>
    
    <j-container title="无背景容器" type="ghost" :bordered="false">
      <p>透明背景的容器，无边框</p>
    </j-container>
    
    <j-container title="仅边框容器" type="bordered" padding="24px">
      <p>仅有边框的容器，自定义内边距</p>
    </j-container>
  </div>
</template>

<style scoped>
.container-demo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
</style>
```

### 使用插槽自定义容器
```vue
<template>
  <j-container>
    <template #header>
      <div class="custom-header">
        <n-icon size="20"><settings-outline /></n-icon>
        <span>系统设置</span>
      </div>
    </template>
    
    <n-form label-placement="left" label-width="100px">
      <n-form-item label="系统名称">
        <n-input />
      </n-form-item>
      <n-form-item label="管理员邮箱">
        <n-input />
      </n-form-item>
    </n-form>
    
    <template #footer>
      <div class="footer-buttons">
        <n-button type="primary">保存</n-button>
        <n-button>取消</n-button>
      </div>
    </template>
  </j-container>
</template>

<script lang="tsx" setup>
  import { SettingsOutline } from '@vicons/ionicons5'
</script>

<style scoped>
.custom-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.footer-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
```

### 加载状态的容器
```vue
<template>
  <j-container title="数据统计" :loading="loading">
    <div class="data-stats">
      <div class="stat-item">
        <div class="stat-title">总用户数</div>
        <div class="stat-value">{{ totalUsers }}</div>
      </div>
      <div class="stat-item">
        <div class="stat-title">今日活跃</div>
        <div class="stat-value">{{ activeUsers }}</div>
      </div>
      <div class="stat-item">
        <div class="stat-title">总订单</div>
        <div class="stat-value">{{ totalOrders }}</div>
      </div>
    </div>
    
    <template #extra>
      <n-button size="small" @click="refreshData">刷新</n-button>
    </template>
  </j-container>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  
  const loading = ref(false)
  const totalUsers = ref(0)
  const activeUsers = ref(0)
  const totalOrders = ref(0)
  
  const refreshData = async () => {
    loading.value = true
    try {
      // 模拟数据加载
      await new Promise(resolve => setTimeout(resolve, 1000))
      totalUsers.value = 1256
      activeUsers.value = 328
      totalOrders.value = 5678
    } finally {
      loading.value = false
    }
  }
  
  // 初始加载
  refreshData()
</script>

<style scoped>
.data-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}
</style>
```

## 🔍 组件实现细节

`j-container` 组件基于 HTML 的 `<div>` 元素实现，通过 CSS 样式控制容器的外观。组件内部使用了插槽来支持自定义内容，提供了灵活的布局功能。

容器内部结构分为三部分：

1. 头部（Header）：显示标题和额外操作区域
2. 内容（Content）：主要内容区域
3. 底部（Footer）：底部操作区域

组件通过 prop 控制各部分的显示和样式，例如可以通过 `showHeader` 和 `showFooter` 控制头部和底部的显示与隐藏。

## ⚠️ 注意事项

1. 当使用 `type="ghost"` 时，容器默认没有背景色，适合用于透明背景的场景
2. 设置 `loading` 属性为 `true` 时，会显示加载中的状态，内容会被遮罩
3. 如果不需要显示标题，可以设置 `showHeader` 为 `false`
4. 自定义样式可以通过 `contentStyle`, `headerStyle`, `footerStyle` 属性实现
