---
description: 
globs: 
alwaysApply: false
---
# 组件模式与最佳实践

## 📋 CRUD 组件模式
- 使用 `j-crud` 组件实现列表、增删改查功能
- 通过传递不同的方法实现增删改查功能：
  ```
  :addMethod="addXXX"
  :queryMethod="pageQueryXXX"
  :updateMethod="updateXXX"
  ```
- 使用 `@queryComplete` 处理查询完成后的回调
- 自定义按钮文案使用 `addButtonAlias` 属性

## 🔍 表单与查询
- 使用 `#extendFormItems` 插槽扩展查询表单
- 表单项使用 `n-form-item` 组件，定义 `label` 和 `path` 属性
- 自定义业务组件如 `j-bus-hos-org`, `AssetSearch`, `EmpSearch` 用于特定字段选择

## 📊 标签页管理
- 使用 `:tabs` 属性定义标签页
- `default-check-tab` 设置默认选中的标签页
- 使用 `tabChange` 方法在逻辑中切换标签页

## 🪟 弹窗处理
- 使用 `j-modal` 或 `n-modal` 实现弹窗功能
- 弹窗可通过 ref 进行控制显示/隐藏
- 使用 `<template #content>` 或 `<template #header>` 自定义弹窗内容
- 签名确认弹窗搭配 `j-sign` 组件使用

## 📂 文件预览
- 使用 `Preview` 组件实现文件预览功能
- 预览弹窗通常需要处理：
  - 预览状态 `previewVisible`
  - 预览文件信息 `previewFile`
  - 关闭按钮和预览内容布局

## 🔧 hooks 抽离与复用
- 业务逻辑抽离到 hooks 中，如 `useBorrowHooks`, `useReturnHooks`
- hooks 返回的内容包括：
  - 响应式状态（如 `queryForm`, `showSignModal`）
  - 方法（如 `tabChange`, `signDone`）
  - 组件引用（如 `crudRef`）

## 🎯 事件处理
- 使用 `@` 绑定事件，如 `@queryComplete`, `@add`
- 事件处理函数可以是内联箭头函数或引用方法
- 特定条件下可以使用事件处理进行标签页切换
