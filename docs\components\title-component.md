# 增强标题组件使用指南 🎨

## 概述

增强版标题组件 (`index-new.vue`) 提供了丰富的风格化控制选项，包括大小、颜色、样式、边框等多种自定义配置。基于 Vue 3 Composition API 构建，采用 TypeScript 提供完整的类型支持。

## 功能特性

- ✨ **多种尺寸**: 支持 5 种不同大小 (tiny, small, normal, large, huge)
- 🎨 **丰富颜色**: 内置主题色彩 + 自定义颜色支持
- 🖼️ **样式变体**: 实心、轮廓、纯文本三种风格
- 📏 **边框控制**: 可控制边框位置、宽度和样式
- 🔤 **字体选项**: 支持不同字重和大小写转换
- 🎯 **对齐方式**: 支持居中对齐和自定义对齐
- 🌙 **主题模式**: 支持浅色/深色主题
- 📱 **响应式**: 自动适配移动设备
- 🎛️ **自定义样式**: 支持完全自定义 CSS 样式

## 基础使用

```vue
<template>
  <!-- 最简单的使用 -->
  <TitleNew title="我的标题" />
  
  <!-- 带插槽内容 -->
  <TitleNew title="带操作的标题">
    <button>操作按钮</button>
  </TitleNew>
</template>

<script setup>
import TitleNew from '@/components/common/title/index-new.vue'
</script>
```

## 属性配置

### 基础属性

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `title` | `string` | `'标题'` | 标题文本内容 |
| `size` | `'tiny' \| 'small' \| 'normal' \| 'large' \| 'huge'` | `'normal'` | 标题大小 |
| `color` | `'primary' \| 'success' \| 'warning' \| 'error' \| 'info' \| string` | `'primary'` | 颜色主题或自定义颜色 |

### 样式控制

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `variant` | `'solid' \| 'outlined' \| 'text'` | `'solid'` | 样式变体 |
| `weight` | `'light' \| 'normal' \| 'medium' \| 'bold'` | `'normal'` | 字体权重 |
| `spacing` | `'tight' \| 'normal' \| 'loose'` | `'normal'` | 内边距大小 |

### 边框设置

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `borderPosition` | `'left' \| 'bottom' \| 'top' \| 'none'` | `'left'` | 边框位置 |
| `borderWidth` | `'thin' \| 'normal' \| 'thick'` | `'normal'` | 边框宽度 |

### 特殊选项

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `theme` | `'light' \| 'dark' \| 'auto'` | `'auto'` | 主题模式 |
| `uppercase` | `boolean` | `false` | 是否转为大写 |
| `centered` | `boolean` | `false` | 是否居中对齐 |
| `customStyles` | `Record<string, any>` | `{}` | 自定义样式对象 |

## 使用示例

### 不同尺寸

```vue
<template>
  <TitleNew title="微小标题" size="tiny" />
  <TitleNew title="小标题" size="small" />
  <TitleNew title="普通标题" size="normal" />
  <TitleNew title="大标题" size="large" />
  <TitleNew title="巨大标题" size="huge" />
</template>
```

### 颜色主题

```vue
<template>
  <TitleNew title="主要颜色" color="primary" />
  <TitleNew title="成功颜色" color="success" />
  <TitleNew title="警告颜色" color="warning" />
  <TitleNew title="错误颜色" color="error" />
  <TitleNew title="信息颜色" color="info" />
  <TitleNew title="自定义颜色" color="#9c27b0" />
</template>
```

### 样式变体

```vue
<template>
  <!-- 实心样式 -->
  <TitleNew title="实心样式" variant="solid" color="success" />
  
  <!-- 轮廓样式 -->
  <TitleNew title="轮廓样式" variant="outlined" color="warning" />
  
  <!-- 纯文本样式 -->
  <TitleNew title="文本样式" variant="text" color="error" />
</template>
```

### 边框控制

```vue
<template>
  <!-- 不同位置的边框 -->
  <TitleNew title="左边框" border-position="left" />
  <TitleNew title="底部边框" border-position="bottom" />
  <TitleNew title="顶部边框" border-position="top" />
  <TitleNew title="无边框" border-position="none" />
  
  <!-- 不同宽度的边框 -->
  <TitleNew title="细边框" border-width="thin" />
  <TitleNew title="普通边框" border-width="normal" />
  <TitleNew title="粗边框" border-width="thick" />
</template>
```

### 特殊效果

```vue
<template>
  <!-- 大写转换 -->
  <TitleNew title="uppercase title" uppercase />
  
  <!-- 居中对齐 -->
  <TitleNew title="居中标题" centered />
  
  <!-- 组合效果 -->
  <TitleNew 
    title="组合效果标题" 
    size="large"
    color="info"
    weight="bold"
    uppercase
    border-position="bottom"
    border-width="thick"
  />
</template>
```

### 自定义样式

```vue
<template>
  <TitleNew 
    title="完全自定义样式" 
    :custom-styles="{
      background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
      padding: '1rem',
      borderRadius: '8px',
      color: 'white',
      textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
      transform: 'rotate(-2deg)'
    }"
    border-position="none"
  />
</template>
```

## 主题适配

组件自动适配项目的CSS变量：

```css
:root {
  --j-frame-color: #18a058;           /* 主色调 */
  --j-font-size-tiny: 12px;           /* 微小字体 */
  --j-font-size-normal: 14px;         /* 普通字体 */
  --j-font-size-large: 20px;          /* 大字体 */
}
```

## 响应式设计

组件内置响应式支持，在小屏幕设备上会自动调整：

```css
@media (max-width: 768px) {
  /* large 和 huge 尺寸在移动设备上自动降级为 normal */
}
```

## TypeScript 支持

使用 TypeScript 时，可以导入类型定义：

```typescript
import type { TitleProps } from '@/components/common/title/types'

const titleConfig: TitleProps = {
  title: '我的标题',
  size: 'large',
  color: 'primary',
  variant: 'outlined'
}
```

## 最佳实践

### 1. 语义化使用
```vue
<!-- ✅ 推荐：根据内容重要性选择合适尺寸 -->
<TitleNew title="页面主标题" size="huge" weight="bold" />
<TitleNew title="章节标题" size="large" />
<TitleNew title="子标题" size="normal" />

<!-- ❌ 避免：过度使用大尺寸 -->
<TitleNew title="普通内容" size="huge" />
```

### 2. 颜色使用
```vue
<!-- ✅ 推荐：语义化颜色 -->
<TitleNew title="成功提示" color="success" />
<TitleNew title="警告信息" color="warning" />
<TitleNew title="错误提示" color="error" />

<!-- ✅ 推荐：自定义品牌色 -->
<TitleNew title="品牌标题" color="#your-brand-color" />
```

### 3. 性能优化
```vue
<!-- ✅ 推荐：复杂样式使用 customStyles -->
<TitleNew 
  title="复杂样式标题"
  :custom-styles="computedComplexStyles"
  border-position="none"
/>

<!-- ❌ 避免：频繁计算复杂样式 -->
<TitleNew :custom-styles="{ /* 复杂计算 */ }" />
```

## 迁移指南

从原始组件迁移到增强版组件：

```vue
<!-- 原始组件 -->
<Title title="我的标题" />

<!-- 增强版组件 - 保持向后兼容 -->
<TitleNew title="我的标题" />

<!-- 使用新功能 -->
<TitleNew 
  title="我的标题" 
  size="large" 
  color="success" 
  variant="outlined" 
/>
```

## 故障排除

### 常见问题

1. **样式不生效**
   - 检查是否正确导入了组件
   - 确认CSS变量是否正确定义
   - 检查自定义样式的语法

2. **颜色显示异常**
   - 确认使用的颜色值格式正确
   - 检查是否有其他样式覆盖

3. **响应式问题**
   - 确认父容器有正确的响应式设置
   - 检查CSS媒体查询是否正常工作

### 调试技巧

```vue
<template>
  <!-- 开启调试模式查看计算后的样式 -->
  <TitleNew 
    title="调试标题"
    :custom-styles="debugMode ? { border: '1px solid red' } : {}"
  />
</template>

<script setup>
const debugMode = import.meta.env.DEV
</script>
```

## 更新日志

- v2.0.0: 增强版组件发布，支持全面的风格化控制
- v1.0.0: 原始版本，基础功能

---

如有问题或建议，请联系开发团队 💬 