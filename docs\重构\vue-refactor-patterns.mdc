---
description: 
globs: 
alwaysApply: false
---
# Vue组件（新增）重构模式指南 🚀

## 核心重构原则

### 1. 文件命名规范 📁
- 如果是重构保留现有代码，新重构代码使用 `index-new.vue` 命名
- 组件目录结构保持扁平化，所有相关文件放在当前目录下[components,types,styles,hook]
- 
- Hook函数文件命名：`use[功能名].ts`
- 样式文件命名：`styles.less` 或 `[组件名].less`

- 子组件使用vue文件返回render函数的方式（tsx）
- 主页面中的n-data-table的columns一定要放到主页面
```
<template>

<!-- 这里没有 template，因为我们使用 JSX -->

</template>

<script lang="tsx">

import { defineComponent } from 'vue';

export default defineComponent({

name: 'MyComponent',

props: {

message: {

type: String,

required: true,

},

},

render() {

const dynamicClass = this.message.length > 5 ? 'long-message' : 'short-message';

return (

<div class={dynamicClass}>

<h1>This is a title</h1>

<p>Message: {this.message}</p>

</div>

);

},

});

</script>

<style scoped lang="less">
</style>
````@amsInventoryReport 

## 重构检查清单 ✅

### 代码质量
- [ ] 使用Setup语法糖替代Options API
- [ ] 子组件采用TSX render函数模式
- [ ] 组件拆分合理，职责单一
- [ ] Hook函数抽离业务逻辑
- [ ] TypeScript类型定义完整

### 文件组织
- [ ] 如果是重构新代码使用 `index-new.vue` 命名
- [ ] 相关文件放在当前目录下
- [ ] 样式文件独立管理
- [ ] 类型定义统一管理

### 性能优化
- [ ] 合理使用 computed 和 watch
- [ ] 避免不必要的响应式转换
- [ ] 组件懒加载（必要时）
- [ ] 防抖节流处理（用户交互）


### 用户体验
- [ ] 加载状态提示
- [ ] 错误处理和反馈
- [ ] 响应式设计支持
