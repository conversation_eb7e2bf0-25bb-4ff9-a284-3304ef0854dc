# withCacheRequest 函数修复说明

## 🚨 修复的Bug

### 1. 缓存键冲突风险
**问题**: 原来的缓存键生成方式 `${prefix}_${stableDataString}` 可能产生冲突
**修复**: 
- 使用哈希函数生成安全的缓存键
- 添加 `withCacheRequest_` 前缀便于统一管理
- 新格式: `withCacheRequest_${prefix}_${hash}`

### 2. sessionStorage 存储限制未处理
**问题**: sessionStorage 有大小限制，存储失败时没有处理
**修复**: 
- 实现存储降级机制：sessionStorage → localStorage
- 添加完整的异常处理
- 存储失败时给出明确的警告信息

### 3. 多余的数据校验逻辑
**问题**: 缓存命中时还要校验数据一致性，这是多余的
**修复**: 
- 移除了多余的数据校验逻辑
- 简化了缓存命中的处理流程
- 不再存储原始 data，减少存储空间

### 4. 错误处理不完整
**问题**: catch 块没有重新抛出错误，调用者无法正确处理失败
**修复**: 
- 在 catch 块中重新抛出错误 `throw error`
- 确保错误能正确传播给调用者

### 5. stableJsonStringify 函数局限性
**问题**: 
- 对嵌套对象没有递归处理键排序
- 对特殊值处理不完善
**修复**: 
- 实现递归键排序
- 过滤 undefined 值
- 改进数组和嵌套对象的处理

## 🆕 新增功能

### 1. 统一缓存前缀
所有 withCacheRequest 生成的缓存都添加了 `withCacheRequest_` 前缀，便于统一管理和清理。

### 2. 存储降级机制
```typescript
function safeStorageSet(key: string, value: string): boolean {
  try {
    sessionStorage.setItem(key, value)
    return true
  } catch (error) {
    console.warn(`[Cache] sessionStorage 存储失败，降级到 localStorage: ${error}`)
    try {
      localStorage.setItem(key, value)
      return true
    } catch (localError) {
      console.error(`[Cache] localStorage 存储也失败: ${localError}`)
      return false
    }
  }
}
```

### 3. 缓存清理工具
```typescript
// 清理所有 withCacheRequest 缓存
clearWithCacheRequestCache()

// 清理指定前缀的缓存
clearWithCacheRequestCache('queryOrg')
```

### 4. 安全的缓存键生成
```typescript
function generateCacheKey(prefix: string, dataString: string): string {
  // 使用哈希函数避免键过长和冲突
  let hash = 0
  const fullString = `${prefix}::${dataString}`
  for (let i = 0; i < fullString.length; i++) {
    const char = fullString.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return `withCacheRequest_${prefix}_${Math.abs(hash).toString(36)}`
}
```

## 📝 使用示例

### 基本使用（无变化）
```typescript
export function queryOrg(data: any) {
  return withCacheRequest('queryOrg', data, () =>
    request({
      url: '/hrm/hrmOrg/queryOrg',
      method: RequestType.POST,
      hideLoadingBar: true,
      data: data,
    })
  )
}
```

### 清理缓存
```typescript
import { clearWithCacheRequestCache } from '@/utils/request'

// 清理所有缓存
clearWithCacheRequestCache()

// 清理特定前缀的缓存
clearWithCacheRequestCache('queryOrg')
```

## 🔧 技术改进

1. **更好的错误处理**: 完整的异常捕获和错误传播
2. **存储容错**: sessionStorage 满了自动降级到 localStorage
3. **缓存管理**: 统一前缀便于批量清理
4. **性能优化**: 移除多余的数据校验，减少存储空间
5. **键安全性**: 使用哈希避免键冲突和过长问题

## ⚠️ 注意事项

1. 缓存键格式已改变，旧缓存会自然过期
2. 新版本会同时检查 sessionStorage 和 localStorage
3. 清理缓存时会同时清理两个存储和内存中的待处理请求
4. 错误处理更严格，确保调用者能正确处理失败情况

## 🧪 测试建议

1. 测试存储空间满时的降级机制
2. 测试缓存清理功能
3. 测试错误传播是否正确
4. 测试嵌套对象的键排序是否稳定
5. 测试并发请求的去重功能
