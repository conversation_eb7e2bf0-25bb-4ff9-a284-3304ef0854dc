<template>
  <div class="p-6 space-y-6">
    <n-card title="自适应数据表格测试" size="small">
      <template #header-extra>
        <n-space :size="8">
          <n-button size="small" @click="addTestData">
            添加数据
          </n-button>
          <n-button size="small" @click="clearData">
            清空数据
          </n-button>
          <n-button size="small" @click="toggleLoading">
            {{ loading ? '停止加载' : '开始加载' }}
          </n-button>
        </n-space>
      </template>

      <!-- 自适应数据表格 -->
      <adaptive-data-table
        :data="testData"
        :columns="testColumns"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        :checked-row-keys="checkedRowKeys"
        mobile-title="测试数据列表"
        :show-view-toggle="true"
        :show-mobile-config="true"
        :card-columns="2"
        :show-actions="true"
        @update:checked-row-keys="checkedRowKeys = $event"
        @row-click="handleRowClick"
        @update:columns="handleColumnsUpdate"
      >
        <!-- 操作按钮插槽 -->
        <template #actions="{ row }">
          <n-space :size="4">
            <n-button size="tiny" type="primary" @click.stop="handleEdit(row)">
              编辑
            </n-button>
            <n-popconfirm @positive-click="handleDelete(row)">
              <template #trigger>
                <n-button size="tiny" type="error" @click.stop>
                  删除
                </n-button>
              </template>
              确定要删除这条数据吗？
            </n-popconfirm>
          </n-space>
        </template>
      </adaptive-data-table>
    </n-card>

    <!-- 设备信息显示 -->
    <n-card title="设备信息" size="small">
      <n-descriptions :column="2" bordered>
        <n-descriptions-item label="设备类型">
          {{ isMobileDevice ? '移动端' : 'PC端' }}
        </n-descriptions-item>
        <n-descriptions-item label="屏幕宽度">
          {{ screenWidth }}px
        </n-descriptions-item>
        <n-descriptions-item label="数据条数">
          {{ testData.length }}
        </n-descriptions-item>
        <n-descriptions-item label="选中行数">
          {{ checkedRowKeys.length }}
        </n-descriptions-item>
      </n-descriptions>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, inject } from 'vue'
import { 
  NCard, 
  NSpace, 
  NButton, 
  NPopconfirm, 
  NDescriptions, 
  NDescriptionsItem,
  NTag,
  useMessage 
} from 'naive-ui'
import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'

// 移动端检测
const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice') || ref(false)
const message = useMessage()

// 响应式数据
const loading = ref(false)
const checkedRowKeys = ref<(string | number)[]>([])
const screenWidth = ref(window.innerWidth)

// 测试数据
const testData = ref([
  {
    id: 1,
    name: '张三',
    age: 28,
    department: '技术部',
    position: '前端工程师',
    salary: 15000,
    status: '在职',
    joinDate: '2023-01-15',
    email: '<EMAIL>',
    phone: '13800138001'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    department: '产品部',
    position: '产品经理',
    salary: 18000,
    status: '在职',
    joinDate: '2022-08-20',
    email: '<EMAIL>',
    phone: '13800138002'
  },
  {
    id: 3,
    name: '王五',
    age: 26,
    department: '设计部',
    position: 'UI设计师',
    salary: 12000,
    status: '试用期',
    joinDate: '2023-06-01',
    email: '<EMAIL>',
    phone: '13800138003'
  }
])

// 列配置
const testColumns = ref([
  {
    key: 'name',
    title: '姓名',
    width: 100,
    mobileTitle: true,  // 移动端主标题
    mobileOrder: 1,
    render: (row: any) => <span style="font-weight: 600;">{row.name}</span>
  },
  {
    key: 'position',
    title: '职位',
    width: 120,
    mobileSubtitle: true,  // 移动端副标题
    mobileOrder: 2
  },
  {
    key: 'status',
    title: '状态',
    width: 80,
    mobilePosition: 'header',  // 移动端头部显示
    mobileOrder: 3,
    render: (row: any) => {
      const type = row.status === '在职' ? 'success' : 'warning'
      return <NTag type={type} size="small">{row.status}</NTag>
    }
  },
  {
    key: 'age',
    title: '年龄',
    width: 80,
    mobileOrder: 4
  },
  {
    key: 'department',
    title: '部门',
    width: 100,
    mobileOrder: 5
  },
  {
    key: 'salary',
    title: '薪资',
    width: 100,
    mobileOrder: 6,
    render: (row: any) => `¥${row.salary.toLocaleString()}`
  },
  {
    key: 'joinDate',
    title: '入职日期',
    width: 120,
    mobilePosition: 'footer',  // 移动端底部显示
    mobileOrder: 7
  },
  {
    key: 'email',
    title: '邮箱',
    width: 180,
    mobileShow: false,  // 移动端隐藏
    mobileOrder: 8
  },
  {
    key: 'phone',
    title: '电话',
    width: 120,
    mobileOrder: 9
  }
])

// 分页配置
const pagination = computed(() => ({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [5, 10, 20, 50],
  showQuickJumper: true,
  total: testData.value.length
}))

// 监听屏幕尺寸变化
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', updateScreenWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenWidth)
})

// 事件处理函数
const addTestData = () => {
  const newId = Math.max(...testData.value.map(item => item.id)) + 1
  const newData = {
    id: newId,
    name: `用户${newId}`,
    age: Math.floor(Math.random() * 20) + 25,
    department: ['技术部', '产品部', '设计部', '运营部'][Math.floor(Math.random() * 4)],
    position: ['工程师', '经理', '专员', '主管'][Math.floor(Math.random() * 4)],
    salary: Math.floor(Math.random() * 10000) + 10000,
    status: Math.random() > 0.5 ? '在职' : '试用期',
    joinDate: '2023-12-01',
    email: `user${newId}@example.com`,
    phone: `1380013800${newId}`
  }
  testData.value.push(newData)
  message.success('添加数据成功')
}

const clearData = () => {
  testData.value = []
  checkedRowKeys.value = []
  message.info('数据已清空')
}

const toggleLoading = () => {
  loading.value = !loading.value
}

const handleRowClick = (row: any, index: number) => {
  message.info(`点击了第 ${index + 1} 行：${row.name}`)
}

const handleEdit = (row: any) => {
  message.info(`编辑用户：${row.name}`)
}

const handleDelete = (row: any) => {
  const index = testData.value.findIndex(item => item.id === row.id)
  if (index > -1) {
    testData.value.splice(index, 1)
    message.success(`删除用户：${row.name}`)
  }
}

const handleColumnsUpdate = (columns: any[]) => {
  testColumns.value = columns
  message.info('列配置已更新')
}
</script>

<style scoped lang="less">
// 测试页面样式
</style>
