# med-pms 医疗绩效管理系统架构图说明 📊

## 系统概述

med-pms（Medical Performance Management System）是一套专为医疗机构设计的综合性绩效管理平台，采用微服务架构，提供科学、公正、透明的绩效评价和奖金分配机制。

## 架构层次

### 1. 前端层 (Vue3 + Naive UI) 🎨
- **用户界面**：提供直观友好的操作界面
- **数据可视化**：丰富的图表和报表展示
- **交互操作**：响应式设计，支持多种设备

**技术栈：**
- Vue 3 + TypeScript
- Naive UI 组件库
- Vite 构建工具
- Pinia 状态管理

### 2. 网关层 (Spring Cloud Gateway) 🚪
- **路由转发**：统一入口，智能路由
- **负载均衡**：分布式请求处理
- **安全认证**：统一身份验证和授权

### 3. 注册中心 (Nacos) 🏢
- **服务发现**：自动服务注册与发现
- **配置管理**：集中化配置管理
- **健康检查**：实时监控服务状态

## 核心业务模块

### 1. 配置管理模块 ⚙️
**功能特性：**
- 科室数据配置：科室与HIS系统的数据映射
- 收费项目配置：收费项目与绩效计算关联
- 用户分组管理：灵活的用户权限分组
- 权限配置：多级权限控制体系

### 2. 绩效计算引擎 🧮
**核心能力：**
- 绩效计算模板：支持复杂计算公式
- 公式配置：灵活的公式定义和管理
- 跨模板引用：支持模板间数据引用
- 计算规则：多种计算方法和规则

**特色功能：**
- 支持4种人员计算方法
- 跨模板变量引用
- 批次管理和版本控制
- 实时计算和缓存优化

### 3. 数据ETL模块 📊
**处理流程：**
- **数据采集调度**：定时自动采集外部数据
- **批量处理**：高效的批量数据处理
- **Magic API集成**：动态API配置和调用
- **状态监控**：实时任务状态跟踪

**技术特点：**
- 支持两种数据类型：成本控制数据、绩效计算数据
- 线程池并行处理
- 失败任务重试机制
- 完整的审计日志

### 4. 报表分析模块 📈
**分析能力：**
- 月度绩效汇总：全面的绩效数据统计
- 成本管控报表：详细的成本分析
- 设备效益分析：医疗设备投资回报分析
- 数据可视化：多维度图表展示

## 数据存储层

### 1. pgsql 数据库 🗄️
- **业务数据存储**：核心业务数据持久化
- **配置信息**：系统配置和参数管理
- **计算结果**：绩效计算结果存储

### 2. Redis 缓存 ⚡
- **会话管理**：用户会话状态缓存
- **数据缓存**：热点数据快速访问
- **任务状态**：ETL任务状态缓存

### 3. Magic API 🔌
- **动态API配置**：无需重启的API配置
- **数据转换**：灵活的数据格式转换
- **外部系统集成**：与HIS、财务、人事系统集成

## 外部系统集成

### 集成系统 🔗
- **HIS系统**：医院信息系统数据获取
- **财务系统**：财务数据同步
- **人事系统**：人员信息管理

## 技术栈总览

### 后端技术栈 🔧
- Spring Boot 2.3.12.RELEASE
- Spring Cloud Hoxton.SR12
- MyBatis-Plus + Druid
- JDK 11

### 前端技术栈 🎨
- Vue 3 + TypeScript
- Naive UI 组件库
- Vite 构建工具
- Pinia 状态管理

## 系统特性

### 核心特性 📊
- **微服务架构**：高可用、可扩展
- **绩效计算引擎**：强大的计算能力
- **数据ETL处理**：自动化数据处理
- **动态API配置**：灵活的接口管理
- **多级权限控制**：精细化权限管理
- **实时数据监控**：全面的系统监控
- **可视化报表**：丰富的数据展示

### 业务功能 🚀
- **配置管理**：系统配置和参数管理
- **绩效计算**：智能绩效计算和分析
- **成本管控**：全面的成本控制
- **效益分析**：投资回报分析
- **数据采集**：自动化数据采集
- **报表分析**：多维度数据分析

## 数据流向

1. **前端** → **网关** → **业务模块**
2. **业务模块** → **数据库/缓存**
3. **ETL模块** → **Magic API** → **外部系统**
4. **计算引擎** → **Redis缓存** → **快速响应**

## 部署架构优势

- **高可用性**：微服务架构，单点故障不影响整体
- **可扩展性**：模块化设计，支持水平扩展
- **高性能**：Redis缓存 + 数据库读写分离
- **易维护**：清晰的分层架构，便于维护和升级
- **安全性**：多层安全防护，完善的权限控制

---

*该架构图展示了med-pms系统的完整技术架构和业务流程，为系统的开发、部署和维护提供了清晰的指导。*
