---
description:
globs:
alwaysApply: false
---
# j-upload 组件

## 📝 组件概述
`j-upload` 组件是一个文件上传组件，支持单文件和多文件上传、拖拽上传、文件预览和管理。组件封装了文件上传的复杂逻辑，提供简洁的接口和丰富的自定义选项。

## 🔧 Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| value/v-model | Array | [] | 绑定的文件列表 |
| action | String | '' | 上传接口地址 |
| headers | Object | {} | 上传请求头 |
| multiple | Boolean | false | 是否支持多文件上传 |
| accept | String | '' | 接受的文件类型，如 '.jpg,.png,.pdf' |
| maxSize | Number | 10 | 最大文件大小，单位MB |
| limit | Number | 5 | 最大上传文件数量 |
| showFileList | Boolean | true | 是否显示文件列表 |
| listType | String | 'text' | 文件列表展示类型，可选: 'text', 'picture', 'picture-card' |
| autoUpload | Boolean | true | 是否自动上传 |
| disabled | Boolean | false | 是否禁用 |
| drag | Boolean | false | 是否启用拖拽上传 |
| beforeUpload | Function | - | 上传前钩子，返回 false 则停止上传 |
| onSuccess | Function | - | 上传成功回调 |
| onError | Function | - | 上传失败回调 |
| onExceed | Function | - | 超出最大数量回调 |
| onRemove | Function | - | 文件移除回调 |
| customRequest | Function | - | 自定义上传方法 |
| bucket | String | 'core' | OSS bucket 名称 |
| fileCategory | String | 'common' | 文件分类 |

## 🎯 事件

| 事件名 | 参数 | 说明 |
| --- | --- | --- |
| update:value | (fileList: Array) | 文件列表更新事件 |
| change | (info: {file, fileList}) | 文件状态变化事件 |
| success | (file, response) | 上传成功事件 |
| error | (error, file) | 上传错误事件 |
| preview | (file) | 文件预览事件 |
| remove | (file) | 文件移除事件 |
| exceed | (files, fileList) | 超出最大数量事件 |

## 📋 插槽

| 插槽名 | 说明 |
| --- | --- |
| default | 默认插槽，自定义上传按钮内容 |
| tip | 提示说明插槽 |
| list | 自定义文件列表 |
| file | 自定义单个文件项的内容 |

## 🌟 使用示例

### 基础使用
```vue
<template>
  <j-upload
    v-model:value="fileList"
    action="/api/upload"
    :limit="5"
    accept=".jpg,.png,.pdf"
  >
    <n-button>点击上传</n-button>
    <template #tip>
      <div class="upload-tip">
        支持 jpg、png、pdf 格式，单个文件不超过 10MB
      </div>
    </template>
  </j-upload>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  
  const fileList = ref([])
</script>

<style scoped>
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}
</style>
```

### 图片上传
```vue
<template>
  <j-upload
    v-model:value="fileList"
    action="/api/upload"
    list-type="picture-card"
    :limit="9"
    accept="image/*"
    :headers="headers"
    @preview="handlePreview"
  >
    <div class="upload-trigger">
      <n-icon size="24"><add-outline /></n-icon>
      <div>上传图片</div>
    </div>
  </j-upload>
  
  <!-- 预览弹窗 -->
  <n-modal v-model:show="previewVisible" preset="card" style="width: 80%; max-width: 800px">
    <template #header>
      <div class="preview-header">
        <span>图片预览</span>
        <n-button circle quaternary @click="previewVisible = false">
          <template #icon>
            <n-icon><close-outline /></n-icon>
          </template>
        </n-button>
      </div>
    </template>
    <div class="preview-content">
      <img v-if="previewImage" :src="previewImage" class="preview-image" />
    </div>
  </n-modal>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { AddOutline, CloseOutline } from '@vicons/ionicons5'
  import { useUserStore } from '@/store'
  
  const userStore = useUserStore()
  const fileList = ref([])
  const previewVisible = ref(false)
  const previewImage = ref('')
  
  const headers = {
    Authorization: userStore.token
  }
  
  const handlePreview = (file) => {
    previewImage.value = file.url || file.thumbUrl
    previewVisible.value = true
  }
</script>

<style scoped>
.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
}
</style>
```

### 拖拽上传
```vue
<template>
  <j-upload
    v-model:value="fileList"
    action="/api/upload"
    :drag="true"
    multiple
    :limit="10"
    :before-upload="beforeUpload"
    @success="handleSuccess"
    @error="handleError"
  >
    <div class="upload-drag-area">
      <n-icon size="48" class="upload-icon"><cloud-upload-outline /></n-icon>
      <div class="upload-text">点击或拖拽文件到此区域上传</div>
      <div class="upload-hint">支持多个文件同时上传</div>
    </div>
  </j-upload>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { CloudUploadOutline } from '@vicons/ionicons5'
  import { useMessage } from 'naive-ui'
  
  const message = useMessage()
  const fileList = ref([])
  
  const beforeUpload = (file) => {
    const isLt10M = file.size / 1024 / 1024 < 10
    if (!isLt10M) {
      message.error('文件大小不能超过 10MB!')
      return false
    }
    return true
  }
  
  const handleSuccess = (file, response) => {
    message.success(`${file.name} 上传成功`)
  }
  
  const handleError = (error, file) => {
    message.error(`${file.name} 上传失败: ${error.message || '未知错误'}`)
  }
</script>

<style scoped>
.upload-drag-area {
  padding: 40px 0;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-drag-area:hover {
  border-color: #18a058;
}

.upload-icon {
  color: #999;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.upload-hint {
  font-size: 12px;
  color: #999;
}
</style>
```

### 自定义上传
```vue
<template>
  <j-upload
    v-model:value="fileList"
    :auto-upload="false"
    :custom-request="handleCustomUpload"
  >
    <n-button type="primary">选择文件</n-button>
    <template #tip>
      <div class="upload-actions">
        <n-button type="info" size="small" @click="handleBatchUpload" :disabled="!fileList.length">
          开始上传
        </n-button>
        <n-button type="error" size="small" @click="clearFiles" :disabled="!fileList.length">
          清空列表
        </n-button>
      </div>
    </template>
  </j-upload>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { useMessage } from 'naive-ui'
  import { uploadFile } from '@/api/common/upload'
  
  const message = useMessage()
  const fileList = ref([])
  
  const handleCustomUpload = ({ file, onProgress, onSuccess, onError }) => {
    // 自定义上传实现
    const formData = new FormData()
    formData.append('file', file.raw)
    
    return uploadFile(formData, {
      onUploadProgress: (e) => {
        const percentage = Math.round((e.loaded * 100) / e.total)
        onProgress({ percent: percentage })
      }
    })
      .then(res => {
        onSuccess(res.data)
        return res.data
      })
      .catch(err => {
        onError(err)
      })
  }
  
  const handleBatchUpload = () => {
    // 批量上传实现
    const uploadPromises = fileList.value
      .filter(file => !file.status || file.status === 'error')
      .map(file => file.upload())
    
    Promise.all(uploadPromises)
      .then(() => {
        message.success('所有文件上传完成')
      })
      .catch(error => {
        message.error('部分文件上传失败')
      })
  }
  
  const clearFiles = () => {
    fileList.value = []
  }
</script>

<style scoped>
.upload-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}
</style>
```

## 🔍 组件实现细节

`j-upload` 组件基于 HTML5 的 File API 和 XMLHttpRequest 实现文件上传功能。组件内部管理上传状态，提供了文件的选择、预览、上传和删除等功能。

对于上传逻辑，组件默认会自动处理文件上传，但也支持通过 `custom-request` 属性自定义上传实现。文件上传时，组件会发送请求到指定的 `action` 地址，并附带相应的请求头和参数。

组件支持多种展示方式：
- `list-type="text"`: 文本列表模式，显示文件名和状态
- `list-type="picture"`: 图片列表模式，显示文件缩略图和名称
- `list-type="picture-card"`: 卡片模式，适合图片上传展示

## ⚠️ 注意事项

1. 使用 `v-model:value` 绑定上传的文件列表
2. 如需要限制上传文件类型，可以使用 `accept` 属性
3. 上传前可以通过 `before-upload` 钩子进行文件验证
4. 对于大文件或自定义上传流程，可以设置 `:auto-upload="false"` 并使用 `custom-request`
5. 上传组件需要配合后端接口使用，确保 `action` 地址正确配置
