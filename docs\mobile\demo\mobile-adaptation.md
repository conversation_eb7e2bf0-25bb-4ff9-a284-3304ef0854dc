# 移动端适配指南 📱

本文档介绍了项目的移动端适配方案，包括设备检测、响应式布局、主题配置等功能。

## 🎯 适配概览

### 核心特性
- ✅ 自动设备检测（移动端/平板/桌面端）
- ✅ 响应式布局系统
- ✅ 动态主题配置
- ✅ 移动端专用组件优先加载
- ✅ 触摸友好的UI设计
- ✅ 安全区域适配

### 设备断点
```typescript
const BREAKPOINTS = {
  mobile: 768,    // 小于768px为移动端
  tablet: 1024,   // 768px-1024px为平板
  desktop: 1024   // 大于1024px为桌面端
}
```

## 🔧 核心工具

### 1. 设备检测工具 (`src/utils/device.ts`)

```typescript
import { 
  DeviceType, 
  isMobile, 
  isTablet, 
  isDesktop, 
  isTouchDevice,
  getDeviceType,
  responsiveListener 
} from '@/utils/device'

// 检测设备类型
const deviceType = getDeviceType() // 'mobile' | 'tablet' | 'desktop'

// 简单检测
const mobile = isMobile()     // boolean
const tablet = isTablet()     // boolean
const desktop = isDesktop()   // boolean
const touch = isTouchDevice() // boolean

// 响应式监听
responsiveListener.addListener((deviceType) => {
  console.log('设备类型变化:', deviceType)
})
```

### 2. 响应式主题配置 (`src/composables/useResponsiveTheme.ts`)

```typescript
import { useResponsiveTheme } from '@/composables/useResponsiveTheme'

const {
  currentDeviceType,
  themeOverrides,
  isMobile,
  isTablet,
  isDesktop,
  isTouchDevice,
  deviceClasses,
  componentSizes,
  spacingConfig,
  fontConfig
} = useResponsiveTheme()

// 在组件中使用
<n-config-provider :theme-overrides="themeOverrides">
  <YourComponent />
</n-config-provider>
```

## 📐 布局系统

### 1. 自动布局切换

主布局 (`src/views/layout/index.vue`) 会根据设备类型自动选择合适的布局：

- **移动端**: 使用 `MobileLayout` 组件（底部导航 + 顶部标题栏）
- **桌面端**: 使用传统的侧边栏布局

### 2. 移动端布局特性

移动端布局 (`src/views/layout/mobile.vue`) 包含：

- 🔝 **顶部标题栏**: 显示当前页面标题，支持返回按钮
- 🍞 **面包屑导航**: 可选的路径导航
- 📱 **主内容区域**: 支持页面转场动画
- 🔽 **底部导航栏**: 快速切换主要功能模块

```vue
<template>
  <MobileLayout>
    <template #header-actions>
      <!-- 自定义顶部右侧操作 -->
      <n-button>操作</n-button>
    </template>
  </MobileLayout>
</template>
```

## 🎨 TailwindCSS 配置

### 移动端专用断点

```css
/* 仅移动端 */
@media (max-width: 767px) {
  .mobile:block { display: block; }
}

/* 仅平板 */
@media (min-width: 768px) and (max-width: 1023px) {
  .tablet:block { display: block; }
}

/* 仅桌面端 */
@media (min-width: 1024px) {
  .desktop:block { display: block; }
}
```

### 触摸友好的尺寸

```css
/* 移动端触摸友好高度 */
.h-touch { height: 44px; }
.min-h-touch { min-height: 44px; }
.w-touch { width: 44px; }

/* 安全区域适配 */
.pt-safe-top { padding-top: env(safe-area-inset-top); }
.pb-safe-bottom { padding-bottom: env(safe-area-inset-bottom); }
```

## 🧩 组件适配

### 1. 移动端专用组件

创建移动端专用组件，路由系统会自动优先加载：

```
src/views/modules/
├── user/
│   ├── index.vue      # 桌面端组件
│   └── index-mob.vue  # 移动端专用组件（优先加载）
```

### 2. 响应式组件尺寸

```vue
<template>
  <div :class="deviceClasses">
    <n-button :size="componentSizes.button">
      响应式按钮
    </n-button>
    
    <n-input :size="componentSizes.input" />
    
    <n-data-table :size="componentSizes.table" />
  </div>
</template>

<script setup>
import { useResponsiveTheme } from '@/composables/useResponsiveTheme'

const { deviceClasses, componentSizes } = useResponsiveTheme()
</script>
```

## 📝 开发指南

### 1. 创建移动端页面

```vue
<!-- src/views/modules/example/index-mob.vue -->
<template>
  <div class="mobile-page p-4">
    <div class="px-4">
      <!-- 移动端优化的内容 -->
      <n-card class="touch-friendly">
        <h2 class="text-lg-mobile font-semibold mb-4">移动端页面</h2>
        
        <n-space vertical :size="spacingConfig.gap.md">
          <n-button 
            type="primary" 
            :size="componentSizes.button"
            class="w-full h-touch"
          >
            触摸友好按钮
          </n-button>
          
          <n-input 
            :size="componentSizes.input"
            placeholder="移动端输入框"
            class="h-touch"
          />
        </n-space>
      </n-card>
    </div>
  </div>
</template>

<script setup>
import { useResponsiveTheme } from '@/composables/useResponsiveTheme'

const { componentSizes, spacingConfig } = useResponsiveTheme()
</script>

<style scoped>
.touch-friendly {
  /* 移动端优化样式 */
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.mobile-page {
  /* 移动端页面样式 */
  min-height: calc(100vh - 120px); /* 减去顶部和底部导航高度 */
  -webkit-overflow-scrolling: touch;
}
</style>
```

### 2. 响应式样式编写

```vue
<template>
  <div class="responsive-container">
    <!-- 移动端显示 -->
    <div class="mobile:block tablet:hidden desktop:hidden">
      <MobileComponent />
    </div>
    
    <!-- 平板显示 -->
    <div class="mobile:hidden tablet:block desktop:hidden">
      <TabletComponent />
    </div>
    
    <!-- 桌面端显示 -->
    <div class="mobile:hidden tablet:hidden desktop:block">
      <DesktopComponent />
    </div>
  </div>
</template>

<style scoped>
.responsive-container {
  /* 基础样式 */
  @apply p-4;
  
  /* 移动端样式 */
  @apply mobile:p-2 mobile:text-base-mobile;
  
  /* 平板样式 */
  @apply tablet:p-3;
  
  /* 桌面端样式 */
  @apply desktop:p-6;
}
</style>
```

## 🚀 最佳实践

### 1. 性能优化
- 使用懒加载加载移动端专用组件
- 避免在移动端加载过大的资源
- 使用虚拟滚动处理长列表

### 2. 用户体验
- 确保触摸目标至少44px×44px
- 使用适当的触摸反馈
- 优化页面转场动画

### 3. 兼容性
- 测试不同设备和浏览器
- 考虑横竖屏切换
- 适配安全区域（刘海屏等）

## 🔍 调试工具

### 1. 设备模拟
```javascript
// 在浏览器控制台中模拟设备类型
window.__DEVICE_TYPE__ = 'mobile' // 'mobile' | 'tablet' | 'desktop'
location.reload()
```

### 2. 主题调试
```javascript
// 查看当前主题配置
console.log(window.__THEME_OVERRIDES__)
```

## 📚 相关文件

- `src/utils/device.ts` - 设备检测工具
- `src/composables/useResponsiveTheme.ts` - 响应式主题配置
- `src/types/comps/themeOverrides.ts` - 主题配置定义
- `src/views/layout/mobile.vue` - 移动端布局组件
- `tailwind.config.js` - TailwindCSS配置
- `src/router/index.ts` - 路由系统（支持移动端组件优先加载）
