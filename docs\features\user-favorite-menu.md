# 用户常用功能记忆系统 🌟

## 📋 功能概述

用户常用功能记忆系统是一个个性化的菜单管理功能，允许用户自定义添加、删除和排序常用的系统功能，提升工作效率和用户体验。

## 🎯 主要特性

### ✨ 核心功能
- **个性化收藏**：用户可以将常用的系统功能添加到个人收藏夹
- **智能排序**：支持拖拽排序，按使用频率自动调整
- **实时同步**：收藏状态实时同步到移动端和PC端
- **警告提醒**：集成快速到达功能，显示待办数量提醒
- **跨系统支持**：支持多个子系统的菜单收藏

### 📱 移动端特性
- **快捷入口**：在移动端首页显示常用功能快捷入口
- **简洁界面**：采用卡片式设计，符合移动端操作习惯
- **一键收藏**：在任意页面可快速添加/取消收藏
- **智能推荐**：根据用户使用习惯推荐常用功能

## 🏗️ 技术架构

### 后端架构（med-core）
```
├── controller/
│   └── SysUserFavoriteMenuController.java     # 控制器
├── service/
│   ├── read/
│   │   └── SysUserFavoriteMenuReadService.java    # 读取服务
│   └── write/
│       └── SysUserFavoriteMenuWriteService.java   # 写入服务
├── mapper/
│   ├── read/
│   │   └── SysUserFavoriteMenuReadMapper.java     # 读取Mapper
│   └── write/
│       └── SysUserFavoriteMenuWriteMapper.java    # 写入Mapper
├── dto/
│   └── SysUserFavoriteMenuDto.java            # 数据传输对象
└── vo/
    └── SysUserFavoriteMenuVo.java             # 视图对象
```

### 前端架构
```
├── api/
│   └── sys/
│       └── userFavoriteMenu.ts                # API接口
├── store/
│   └── userFavoriteMenu.ts                    # 状态管理
├── components/
│   └── common/
│       └── FavoriteMenuButton.vue             # 收藏按钮组件
└── views/
    └── modules/
        └── home/
            └── app-menu-mob.vue               # 移动端菜单（已集成）
```

### 数据库设计
```sql
-- 用户常用菜单表
CREATE TABLE `sys_user_favorite_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `menu_path` varchar(255) NOT NULL COMMENT '菜单路径',
  `menu_name` varchar(100) NOT NULL COMMENT '菜单名称',
  `menu_icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序号',
  `system_id` int(11) DEFAULT NULL COMMENT '系统ID',
  `menu_id` int(11) DEFAULT NULL COMMENT '菜单ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` char(1) DEFAULT '1' COMMENT '状态 1:启用 0:禁用',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_menu` (`username`, `menu_path`)
);
```

## 🚀 API接口

### 主要接口
| 接口 | 方法 | 描述 |
|------|------|------|
| `/core/sysUserFavoriteMenu/myFavorites` | POST | 获取当前用户常用菜单 |
| `/core/sysUserFavoriteMenu/add` | POST | 添加常用菜单 |
| `/core/sysUserFavoriteMenu/delete` | DELETE | 删除常用菜单 |
| `/core/sysUserFavoriteMenu/updateSort` | POST | 更新排序 |
| `/core/sysUserFavoriteMenu/checkFavorite` | POST | 检查收藏状态 |

### 请求示例
```typescript
// 添加常用菜单
const addFavorite = {
  menuPath: '/oa/bpm/processInstance',
  menuName: 'OA审批',
  menuIcon: 'FlowerOutline',
  systemId: 16,
  sortOrder: 1
}

// 删除常用菜单
const removeFavorite = {
  menuPath: '/oa/bpm/processInstance'
}
```

## 💡 使用方法

### 移动端使用
1. **查看常用功能**：在移动端首页"常用功能"区域查看已收藏的功能
2. **添加收藏**：在任意页面使用收藏按钮添加当前功能到常用
3. **管理收藏**：长按常用功能卡片可进行排序或删除操作

### PC端使用
1. **收藏按钮**：在页面右上角或工具栏找到收藏按钮
2. **管理界面**：通过个人中心进入常用功能管理界面
3. **批量操作**：支持批量添加、删除和排序操作

## 🔧 配置说明

### 环境变量
```env
# 是否启用常用功能记忆（默认启用）
VITE_ENABLE_FAVORITE_MENU=true

# 最大收藏数量限制（默认20个）
VITE_MAX_FAVORITE_COUNT=20
```

### 系统配置
- **默认排序**：新添加的收藏默认排在最后
- **自动清理**：超过最大数量时自动清理最久未使用的收藏
- **权限控制**：只能收藏有权限访问的菜单

## 🎨 UI设计规范

### 移动端设计
- **卡片样式**：使用圆角卡片，符合现代设计趋势
- **图标系统**：统一使用Ionicons图标库
- **颜色方案**：主色调绿色，符合医院系统特色
- **交互反馈**：点击、悬停等状态有明确的视觉反馈

### 响应式适配
- **移动端**：3列网格布局，适合手指操作
- **平板端**：4-5列布局，充分利用屏幕空间
- **桌面端**：6-8列布局，信息密度更高

## 🔍 性能优化

### 前端优化
- **懒加载**：常用功能数据按需加载
- **缓存策略**：本地缓存用户收藏数据，减少网络请求
- **防抖处理**：排序操作使用防抖，避免频繁请求

### 后端优化
- **索引优化**：为用户名和菜单路径建立联合索引
- **批量操作**：支持批量插入和更新操作
- **缓存机制**：使用Redis缓存热点数据

## 🧪 测试用例

### 功能测试
- [x] 添加常用功能
- [x] 删除常用功能
- [x] 排序常用功能
- [x] 检查收藏状态
- [x] 移动端显示
- [x] 权限验证

### 性能测试
- [x] 并发添加收藏
- [x] 大量数据加载
- [x] 移动端响应速度

## 🚧 后续规划

### 短期计划
- [ ] PC端管理界面
- [ ] 拖拽排序功能
- [ ] 使用频率统计
- [ ] 智能推荐算法

### 长期计划
- [ ] 团队共享收藏
- [ ] 收藏夹分组管理
- [ ] 跨设备同步
- [ ] AI智能推荐

## 📞 技术支持

如有问题或建议，请联系开发团队：
- **开发负责人**：系统开发组
- **技术文档**：详见项目Wiki
- **问题反馈**：通过系统内置反馈功能提交
