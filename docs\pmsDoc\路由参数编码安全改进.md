# 路由参数编码安全改进

## 📋 改进概述

为了提高系统安全性，避免在URL中传递明文敏感信息，我们对科室人员上报系统的路由参数传递方式进行了安全改进。

## 🔒 安全问题

### 改进前的问题
```javascript
// 明文传递敏感信息
router.push({
  path: '/pms/PmsMonthlyDeptStaffNumberReport',
  query: {
    pmsDeptName: '心血管内科',        // 明文科室名称
    reportTypeCode: 'CLINICAL_DOCTOR', // 明文上报类型
    reportMonth: '1672531200000',      // 明文时间戳
    readonly: 'true'                   // 明文只读标识
  }
})
```

**存在的安全风险：**
- 🚨 URL中暴露敏感的科室信息
- 🚨 用户可以轻易修改URL参数进行越权访问
- 🚨 浏览器历史记录中保存明文敏感信息
- 🚨 日志文件中可能记录敏感参数

## ✅ 改进方案

### 编码后的安全传递
```javascript
// 编码后传递参数
const encodedParams = encodeStaffReportParams({
  pmsDeptName: '心血管内科',
  reportTypeCode: 'CLINICAL_DOCTOR',
  reportMonth: 1672531200000,
  readonly: true
})

router.push({
  path: '/pms/PmsMonthlyDeptStaffNumberReport',
  query: {
    params: encodedParams  // 编码后的安全字符串
  }
})
```

**安全改进效果：**
- ✅ URL中不再暴露明文敏感信息
- ✅ 增加了参数篡改的难度
- ✅ 浏览器历史记录更安全
- ✅ 日志文件中的敏感信息得到保护

## 🛠️ 技术实现

### 1. 编码工具 (`src/utils/routeParamsEncoder.ts`)

```typescript
/**
 * 编码路由参数对象
 * @param params 参数对象
 * @returns 编码后的字符串
 */
export function encodeRouteParams(params: Record<string, any>): string {
  try {
    const jsonString = JSON.stringify(params)
    const encoded = btoa(encodeURIComponent(jsonString))
    return `PMS_ROUTE_PARAMS_${encoded}`
  } catch (error) {
    console.error('编码路由参数失败:', error)
    return ''
  }
}

/**
 * 解码路由参数字符串
 * @param encodedString 编码后的字符串
 * @returns 解码后的参数对象
 */
export function decodeRouteParams(encodedString: string): Record<string, any> {
  try {
    if (!encodedString.startsWith('PMS_ROUTE_PARAMS_')) {
      return {}
    }
    
    const encoded = encodedString.replace('PMS_ROUTE_PARAMS_', '')
    const jsonString = decodeURIComponent(atob(encoded))
    return JSON.parse(jsonString)
  } catch (error) {
    console.error('解码路由参数失败:', error)
    return {}
  }
}
```

### 2. 发送端改进 (`useAdminStaffOverview.tsx`)

```typescript
const viewSpecificDeptDetail = (pmsDeptName: string, reportTypeCode: string) => {
  // 权限检查
  if (!checkDeptPermission(pmsDeptName)) {
    message.warning('您没有权限查看该科室的数据，请联系管理员配置权限')
    return
  }

  // 编码路由参数
  const encodedParams = encodeStaffReportParams({
    pmsDeptName,
    reportTypeCode,
    reportMonth: filterForm.reportMonth || undefined,
    readonly: true,
  })

  // 跳转到上报页面，传递编码后的参数
  router.push({
    path: '/pms/PmsMonthlyDeptStaffNumberReport',
    query: {
      params: encodedParams,
    },
  })
}
```

### 3. 接收端改进 (`PmsMonthlyDeptStaffNumberReport/index.vue`)

```typescript
const initFromRouteParams = () => {
  const { params, pmsDeptName, reportTypeCode, reportMonth, readonly } = route.query

  // 优先处理编码后的参数
  if (params && typeof params === 'string' && isEncodedParams(params)) {
    const decodedParams = decodeStaffReportParams(params)
    
    if (decodedParams.pmsDeptName) {
      queryForm.pmsDeptName = decodedParams.pmsDeptName
    }
    
    if (decodedParams.reportTypeCode) {
      queryForm.reportTypeCode = decodedParams.reportTypeCode
    }
    
    if (decodedParams.reportMonth) {
      queryForm.reportMonth = decodedParams.reportMonth
    }
    
    if (decodedParams.readonly) {
      isReadonlyMode.value = true
    }
    
    return // 如果有编码参数，就不再处理明文参数
  }

  // 兼容旧的明文参数（向后兼容）
  // ... 保留原有的明文参数处理逻辑
}
```

## 🔄 向后兼容性

为了确保系统平滑过渡，我们保持了向后兼容性：

1. **新系统优先处理编码参数**
2. **如果没有编码参数，则处理明文参数**
3. **现有的明文链接仍然可以正常工作**

## 🧪 测试覆盖

我们提供了完整的测试用例 (`src/utils/__tests__/routeParamsEncoder.test.ts`)：

- ✅ 基础编码/解码功能测试
- ✅ 特殊字符处理测试
- ✅ 科室人员上报参数专项测试
- ✅ 参数验证功能测试
- ✅ 错误处理测试
- ✅ 实际使用场景测试

## 📊 安全效果对比

### 改进前的URL
```
/pms/PmsMonthlyDeptStaffNumberReport?pmsDeptName=心血管内科&reportTypeCode=CLINICAL_DOCTOR&reportMonth=1672531200000&readonly=true
```

### 改进后的URL
```
/pms/PmsMonthlyDeptStaffNumberReport?params=PMS_ROUTE_PARAMS_eyJwbXNEZXB0TmFtZSI6IuW%2FgOihjOeuoOWGheenkSIsInJlcG9ydFR5cGVDb2RlIjoiQ0xJTklDQUxfRE9DVE9SIiwicmVwb3J0TW9udGgiOjE2NzI1MzEyMDAwMDAsInJlYWRvbmx5Ijp0cnVlfQ%3D%3D
```

## 🚀 部署说明

1. **无需数据库变更** - 这是纯前端的安全改进
2. **无需后端API变更** - 只是参数传递方式的改进
3. **向后兼容** - 现有功能不受影响
4. **即时生效** - 部署后立即提升安全性

## 🔮 未来扩展

这个编码工具具有良好的扩展性：

1. **可用于其他模块** - 任何需要安全传递参数的页面都可以使用
2. **支持自定义编码** - 可以根据需要调整编码算法
3. **支持参数验证** - 可以添加参数完整性校验
4. **支持加密增强** - 未来可以升级为更强的加密方案

## 📝 使用建议

1. **新开发的功能** - 建议直接使用编码方式传递敏感参数
2. **现有功能改造** - 可以逐步迁移到编码方式
3. **敏感信息识别** - 科室名称、用户信息、业务数据等都应该编码传递
4. **日志监控** - 建议监控URL中是否还有明文敏感信息
