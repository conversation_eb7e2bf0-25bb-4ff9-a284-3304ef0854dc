# 资产卡片配置复制功能

## 📋 功能概述

为 `amsStockConfig` 模块添加了复制功能，允许用户快速复制现有的卡片配置来创建新的配置，提高配置效率。

## ✨ 功能特性

### 🎯 核心功能
- **一键复制**: 在操作列中点击复制按钮即可复制现有配置
- **智能命名**: 自动为新配置添加 "- 副本" 后缀
- **字段保持**: 保持原配置的字段选择和必填设置
- **加载状态**: 复制过程中显示加载状态，提升用户体验

### 🔧 技术实现
- **复制按钮**: 在表格操作列添加带图标的复制按钮
- **复制弹窗**: 美观的弹窗界面，显示原配置信息和输入新配置信息
- **数据处理**: 正确处理配置数据结构，确保复制的完整性
- **错误处理**: 完善的错误处理和用户提示

## 🚀 使用方法

### 1. 复制配置
1. 在资产卡片配置列表中找到要复制的配置
2. 点击该行的 "复制" 按钮（带复制图标）
3. 在弹出的复制弹窗中：
   - 查看原卡片名称和配置字段数
   - 输入新的卡片编码（必填）
   - 输入新的卡片名称（必填，默认为原名称 + "- 副本"）
4. 点击 "确认复制" 按钮完成复制

### 2. 复制弹窗信息
- **原卡片名称**: 显示被复制的原配置名称
- **配置字段数**: 显示将要复制的字段数量
- **新卡片编码**: 输入新配置的唯一编码
- **新卡片名称**: 输入新配置的显示名称

## 🎨 界面设计

### 复制按钮
- 位置：表格操作列
- 样式：小尺寸、信息色、次要按钮
- 图标：复制图标（CopyOutline）
- 文本：复制

### 复制弹窗
- 宽度：500px
- 标题：复制卡片配置
- 表单布局：左对齐标签，标签宽度100px
- 按钮：取消、确认复制（带加载状态）

## 🔄 数据流程

### 复制流程
1. **获取原配置**: 调用 `queryStockCfg` 获取原卡片的配置数据
2. **显示复制弹窗**: 展示原配置信息，预填新配置信息
3. **用户输入**: 用户输入新的卡片编码和名称
4. **数据处理**: 
   - 获取表格列定义 (`queryTableColumn`)
   - 映射原配置的字段设置到新配置
   - 构建保存数据结构
5. **保存新配置**: 调用 `save` 接口保存新配置
6. **刷新列表**: 复制成功后刷新配置列表

### 数据结构
```typescript
// 复制数据结构
const copyPayload = {
  list: tableColumns.map(column => ({
    ...column,
    mustl: existingConfig?.mustl || '0'
  })),
  flds: originalConfig.map(item => item.fld),
  cardName: newCardName,
  cardCode: newCardCode
}
```

## 🛡️ 错误处理

### 输入验证
- 卡片编码不能为空
- 卡片名称不能为空
- 输入框在加载时禁用

### 异常处理
- 网络请求失败时显示错误提示
- 复制过程中的异常会重置加载状态
- 完善的 try-catch 和 finally 处理

## 📝 代码示例

### 复制按钮渲染
```typescript
{
  title: '操作',
  key: 'action',
  width: 120,
  render: (row: any) => {
    return h('div', { style: 'display: flex; gap: 8px;' }, [
      h('n-button', {
        size: 'small',
        type: 'info',
        secondary: true,
        onClick: () => methods.copyCard(row),
      }, {
        default: () => '复制',
        icon: () => h(NIcon, null, { default: () => h(CopyOutline) }),
      }),
    ])
  },
}
```

### 复制方法
```typescript
copyCard: (row: any) => {
  queryStockCfg({ cardCode: row.cardCode }).then((res: IRes) => {
    if (res.code == 200) {
      data.copyData.value = res.data
      data.originalCardName.value = row.cardName
      data.copyForm.value = {
        cardName: row.cardName + ' - 副本',
        cardCode: '',
      }
      data.showCopyModal.value = true
    }
  })
}
```

## 🎯 用户体验优化

### 交互优化
- 复制按钮带图标，直观易懂
- 弹窗显示原配置信息，便于确认
- 自动预填新配置名称，减少输入
- 加载状态提示，避免重复操作

### 视觉优化
- 统一的按钮样式和间距
- 清晰的表单布局
- 合适的弹窗尺寸
- 一致的颜色主题

## 🔧 技术细节

### 依赖组件
- `@vicons/ionicons5`: 复制图标
- `naive-ui`: UI 组件库
- Vue 3 Composition API

### 状态管理
- `copyData`: 存储原配置数据
- `copyForm`: 存储新配置表单数据
- `originalCardName`: 存储原配置名称
- `copyLoading`: 控制加载状态
- `showCopyModal`: 控制弹窗显示

### API 接口
- `queryStockCfg`: 查询卡片配置
- `queryTableColumn`: 查询表格列定义
- `save`: 保存配置数据

## 📈 后续优化建议

1. **批量复制**: 支持选择多个配置进行批量复制
2. **复制预览**: 在复制前预览将要复制的字段列表
3. **复制历史**: 记录复制操作历史
4. **模板功能**: 将常用配置保存为模板
5. **导入导出**: 支持配置的导入导出功能

## 🎉 总结

复制功能的添加大大提升了资产卡片配置的管理效率，用户可以基于现有配置快速创建新配置，避免重复的字段选择和设置工作。功能设计注重用户体验，界面简洁直观，操作流程顺畅。
