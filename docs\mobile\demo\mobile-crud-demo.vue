<template>
  <j-container>
    <template #content>
      <div class="mobile-crud-demo">
        <n-space vertical :size="16">
          <!-- 演示说明 -->
          <n-card title="移动端CRUD卡片视图演示" size="small">
            <n-space vertical :size="8">
              <n-text>
                📱 本页面演示了CRUD组件的移动端卡片视图功能，包括：
              </n-text>
              <n-ul>
                <n-li>自动设备检测，移动端显示卡片视图</n-li>
                <n-li>支持字段配置，可设置标题、副标题等</n-li>
                <n-li>支持视图配置，可调整显示字段和顺序</n-li>
                <n-li>保持原有的增删改查功能</n-li>
              </n-ul>
              <n-space>
                <n-tag type="info">当前设备：{{ deviceType }}</n-tag>
                <n-tag :type="isMobile ? 'success' : 'warning'">
                  {{ isMobile ? '移动端视图' : '桌面端视图' }}
                </n-tag>
              </n-space>
            </n-space>
          </n-card>

          <!-- 控制面板 -->
          <n-card title="控制面板" size="small">
            <n-space vertical :size="12">
              <n-space>
                <n-button
                  @click="forceMobileView = !forceMobileView"
                  :type="forceMobileView ? 'primary' : 'default'"
                >
                  {{ forceMobileView ? '关闭' : '开启' }}强制移动端视图
                </n-button>
                <n-button @click="refreshData">刷新数据</n-button>
                <n-button @click="addSampleData">添加示例数据</n-button>
              </n-space>

              <!-- 卡片列数控制 -->
              <n-space align="center">
                <n-text>卡片列数：</n-text>
                <n-radio-group v-model:value="cardColumns" size="small">
                  <n-radio-button :value="1">1列</n-radio-button>
                  <n-radio-button :value="2">2列</n-radio-button>
                  <n-radio-button :value="3">3列</n-radio-button>
                </n-radio-group>
              </n-space>
            </n-space>
          </n-card>

          <!-- CRUD组件 -->
          <j-crud
            ref="crudRef"
            :columns="columns"
            :query-method="queryData"
            :add-method="addData"
            :update-method="updateData"
            :del-method="deleteData"
            :force-mobile-view="forceMobileView"
            :mobile-default-card-view="true"
            :enable-mobile-view-switch="true"
            :mobile-view-title="'用户管理'"
            :mobile-card-columns="cardColumns"
            name="用户"
            row-key="id"
          />
        </n-space>
      </div>
    </template>
  </j-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { CRUDColumnInterface } from '@/types/comps/crud'
import { useResponsiveTheme } from '@/composables/useResponsiveTheme'
import { ContainerValueType } from '@/types/enums/enums'

// 响应式主题
const { currentDeviceType, isMobile } = useResponsiveTheme()

// 响应式数据
const crudRef = ref()
const forceMobileView = ref(false)
const cardColumns = ref(2)
const mockData = ref<any[]>([])

// 计算属性
const deviceType = computed(() => {
  switch (currentDeviceType.value) {
    case 'mobile': return '移动端'
    case 'tablet': return '平板'
    case 'desktop': return '桌面端'
    default: return '未知'
  }
})

// 列配置 - 测试默认配置功能
const columns: CRUDColumnInterface[] = [
  {
    title: '用户名', // 第一个字段，会自动设为标题
    key: 'username',
    required: true,
  },
  {
    title: '姓名',
    key: 'name',
    required: true,
  },
  {
    title: '邮箱',
    key: 'email',
    type: ContainerValueType.INPUT,
  },
  {
    title: '手机号',
    key: 'phone',
  },
  {
    title: '状态',
    key: 'status',
    type: ContainerValueType.SELECT,
    selection: [
      { label: '正常', value: 'active' },
      { label: '禁用', value: 'disabled' },
      { label: '待审核', value: 'pending' }
    ],
    mobilePosition: 'header', // 在移动端显示在头部
    render: (row) => {
      const statusMap = {
        active: { text: '正常', type: 'success' },
        disabled: { text: '禁用', type: 'error' },
        pending: { text: '待审核', type: 'warning' }
      }
      const status = statusMap[row.status as keyof typeof statusMap] || { text: '未知', type: 'default' }
      return h('n-tag', { type: status.type, size: 'small' }, () => status.text)
    }
  },
  {
    title: '部门',
    key: 'department',
  },
  // 以下字段会被默认隐藏（超过6个字段）
  {
    title: '用户ID',
    key: 'id',
    width: 80,
  },
  {
    title: '创建时间',
    key: 'createTime',
    mobilePosition: 'footer',
  },
  {
    title: '最后登录',
    key: 'lastLogin',
    mobilePosition: 'footer',
  },
  {
    title: '备注',
    key: 'remark',
  }
]

// 生成模拟数据
const generateMockData = (count: number = 20) => {
  const departments = ['技术部', '产品部', '运营部', '市场部', '人事部']
  const statuses = ['active', 'disabled', 'pending']
  
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    username: `user${index + 1}`,
    name: `用户${index + 1}`,
    email: `user${index + 1}@example.com`,
    phone: `138${String(index + 1).padStart(8, '0')}`,
    status: statuses[index % statuses.length],
    department: departments[index % departments.length],
    createTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    lastLogin: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    remark: `这是用户${index + 1}的备注信息`,
  }))
}

// CRUD方法
const queryData = async (params: any) => {
  console.log('查询参数:', params)
  
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  return {
    code: 200,
    data: {
      records: mockData.value,
      total: mockData.value.length,
      pages: 1,
      current: 1,
      size: 20
    },
    message: '查询成功'
  }
}

const addData = async (data: any) => {
  console.log('新增数据:', data)
  
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const newItem = {
    ...data,
    id: Math.max(...mockData.value.map(item => item.id), 0) + 1,
    createTime: new Date().toLocaleDateString(),
    lastLogin: '-'
  }
  
  mockData.value.unshift(newItem)
  
  return {
    code: 200,
    data: newItem,
    message: '新增成功'
  }
}

const updateData = async (data: any) => {
  console.log('更新数据:', data)
  
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const index = mockData.value.findIndex(item => item.id === data.id)
  if (index !== -1) {
    mockData.value[index] = { ...mockData.value[index], ...data }
  }
  
  return {
    code: 200,
    data: data,
    message: '更新成功'
  }
}

const deleteData = async (row: any) => {
  console.log('删除数据:', row)
  
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const index = mockData.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    mockData.value.splice(index, 1)
  }
  
  return {
    code: 200,
    message: '删除成功'
  }
}

// 方法
const refreshData = () => {
  crudRef.value?.queryData()
}

const addSampleData = () => {
  const newData = generateMockData(5)
  mockData.value.push(...newData)
  refreshData()
}

// 生命周期
onMounted(() => {
  mockData.value = generateMockData()
})
</script>

<style scoped>
.mobile-crud-demo {
  padding: 16px;
}

@media (max-width: 768px) {
  .mobile-crud-demo {
    padding: 8px;
  }
}
</style>
