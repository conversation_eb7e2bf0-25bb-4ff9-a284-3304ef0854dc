# 项目分类筛选功能实现说明 📋

## 功能概述 🎯

在绩效管理系统的基础信息采集配置页面中，新增了项目分类筛选条件，支持根据不同的页面类型（Award/IAE）显示不同的分类选项。

## 实现内容 ✨

### 1. 后端接口扩展 🔧

#### IAE项目分类接口
- **文件**: `src/api/pms/clac/pmsBalanceReport/IaeBalanceConfigWeb.ts`
- **新增接口**: `getIaeClassificationOptions`
- **用途**: 获取IAE（成本控制）项目的分类选项

#### Award项目分类接口  
- **文件**: `src/api/pms/clac/PmsAwardCalcItemConfigWeb.ts`
- **新增接口**: `getAwardClassificationOptions`
- **用途**: 获取Award（绩效指标）项目的分类选项

### 2. 基础组件改造 🔨

#### 主组件修改
- **文件**: `src/components/common/pms/pmsBaseInformationCollectionConfig/index.vue`

**新增Props**:
```typescript
interface Props {
  // 项目分类选项
  classificationOptions?: Array<{ label: string; value: string }>
  // 获取分类标签颜色的函数
  getPmsClassTagColor?: (classification: string) => { 
    color: string; 
    textColor: string; 
    borderColor: string 
  }
}
```

**新增筛选条件**:
```vue
<NFormItem label="项目分类" path="classification">
  <NSelect 
    v-model:value="queryForm.classification" 
    clearable 
    :options="classificationOptions" 
    placeholder="请选择项目分类"
  />
</NFormItem>
```

**颜色函数外部化**:
- 创建 `getClassificationColor` 函数，优先使用外部传入的颜色函数
- 修改render函数和CardView组件传参

### 3. 使用页面更新 📄

#### IAE页面 (成本控制指标配置)
- **文件**: `src/views/modules/pms/pmsCalc/pmsIAEBalance/IaeBalanceConfig/index.vue`

**特性**:
- 自定义IAE项目分类颜色映射
- 支持接口获取分类选项，失败时使用默认选项
- 默认分类包括：医疗服务收入、变动成本等

#### Award页面 (绩效指标配置)
- **文件**: `src/views/modules/pms/pmsCalc/pmsInformationCollectionConfig/index.vue`

**特性**:
- 使用通用的绩效分类颜色函数
- 支持接口获取分类选项，失败时使用默认选项  
- 默认分类包括：服务效率指标、医疗质量安全管理指标等

## 技术实现细节 🛠️

### 1. 动态选项加载
```typescript
// 获取项目分类选项
const fetchClassificationOptions = async () => {
  try {
    const { code, data } = await getClassificationOptions()
    if (code === 200) {
      classificationOptions.value = data.map((item: string) => ({
        label: item,
        value: item,
      }))
    }
  } catch (error) {
    // 接口失败时使用默认选项
    classificationOptions.value = defaultOptions
  }
}
```

### 2. 颜色函数外部化
```typescript
// 创建颜色获取函数，优先使用外部传入的函数
const getClassificationColor = (classification: string) => {
  if (externalGetPmsClassTagColor) {
    return externalGetPmsClassTagColor(classification)
  }
  return getPmsClassTagColor(classification)
}
```

### 3. 表单选项动态配置
```typescript
formItemRender: () => {
  // 优先使用传入的分类选项，如果没有则使用store中的数据
  const options = classificationOptions.length > 0 
    ? classificationOptions 
    : [...awardCalcItemClassIncludeConstSet.value].map(item => ({
        label: item,
        value: item,
      }))
  
  return h(NSelect, {
    options,
    filterable: true,
  })
}
```

## 使用方式 📖

### 在页面中使用
```vue
<template>
  <BaseConfigComp
    pageType="iae"  <!-- 或 "award" -->
    :classificationOptions="classificationOptions"
    :getPmsClassTagColor="customColorFunction"
    <!-- 其他props -->
  />
</template>
```

### 自定义颜色函数
```typescript
const customColorFunction = (classification: string) => {
  const colorMap = {
    '分类1': { color: '#EFF8FF', textColor: '#4B89DC', borderColor: '#4B89DC' },
    '分类2': { color: '#F0F8F0', textColor: '#57A773', borderColor: '#57A773' },
  }
  return colorMap[classification] || defaultColor
}
```

## 兼容性说明 🔄

- **向后兼容**: 所有新增的props都是可选的，不传入时使用默认行为
- **渐进增强**: 可以只传入分类选项而不传入颜色函数，或反之
- **降级处理**: 接口失败时自动使用预设的默认选项

## 测试建议 🧪

1. **功能测试**: 验证筛选条件是否正常工作
2. **接口测试**: 测试分类选项接口的成功和失败场景
3. **UI测试**: 验证不同分类的颜色显示是否正确
4. **兼容性测试**: 确保现有页面不受影响

## 后续扩展 🚀

1. **缓存优化**: 可以考虑对分类选项进行缓存
2. **国际化**: 支持多语言的分类名称
3. **权限控制**: 根据用户权限显示不同的分类选项
4. **搜索优化**: 支持分类的模糊搜索功能
