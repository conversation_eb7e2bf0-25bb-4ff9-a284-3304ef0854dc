# 指标详情查看权限批量管理功能 - 完整实现

## 🎉 功能概述

为指标关联模板批量管理页面添加批量开启/关闭指标详情查看功能，提升用户操作效率。

## ✅ 已完成的实现

### 前端部分（100%完成）

#### 1. API接口
**文件：** `src/api/pms/clac/AwardCoefficientConfigWeb.ts`

```typescript
/**
 * 批量更新指标详情查看权限
 * @param param
 */
export function batchUpdateAllowViewItemDetail(param: Object) {
  return request({
    url: 'pms/pmsAwardCoefficientConfig/batchUpdateAllowViewItemDetail',
    method: RequestType.PUT,
    data: param,
  })
}
```

#### 2. 组件功能
**文件：** `src/views/modules/pms/pmsCalc/pmsAwardCalcDeptTemplateConfig/components/ItemTemplateRelationModal.vue`

**新增功能：**
- ✅ 导入新的API方法
- ✅ 批量开启权限函数：`handleBatchEnableViewDetail()`
- ✅ 批量关闭权限函数：`handleBatchDisableViewDetail()`
- ✅ 用户友好的提示信息和错误处理
- ✅ 操作完成后自动刷新数据

**UI界面：**
```vue
<NButton type="success" @click="handleBatchEnableViewDetail">批量开启详情查看</NButton>
<NButton type="warning" @click="handleBatchDisableViewDetail">批量关闭详情查看</NButton>
```

### 后端部分（95%完成）

#### 1. DTO更新 ✅
**文件：** `med-pms/src/main/java/com/jp/med/pms/modules/pmsCalc/dto/PmsItemTemplateRelationDto.java`

```java
/**
 * 是否允许查看指标详情 ('0'/'1')
 */
@ApiModelProperty(value = "是否允许查看指标详情")
private String allowViewItemDetail;
```

#### 2. 服务接口 ✅
**文件：** `med-pms/src/main/java/com/jp/med/pms/modules/pmsCalc/service/write/PmsAwardCoefficientConfigWriteService.java`

```java
/**
 * 批量更新指标详情查看权限
 * @param dto 指标与模板关联DTO，包含itemCode、templateIds和allowViewItemDetail
 */
void batchUpdateAllowViewItemDetail(PmsItemTemplateRelationDto dto);
```

#### 3. 服务实现 ✅
**文件：** `med-pms/src/main/java/com/jp/med/pms/modules/pmsCalcTemplate/service/write/impl/PmsAwardCoefficientConfigWriteServiceImpl.java`

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void batchUpdateAllowViewItemDetail(PmsItemTemplateRelationDto dto) {
    if (dto.getTemplateIds() == null || dto.getTemplateIds().isEmpty() || dto.getItemCode() == null) {
        return;
    }

    // 将String类型的allowViewItemDetail转换为Integer
    Integer allowViewItemDetailValue = "1".equals(dto.getAllowViewItemDetail()) ? 1 : 0;

    var updateWrapper = new UpdateWrapper<PmsAwardCoefficientConfigDto>()
            .eq("item_code", dto.getItemCode())
            .in("template_id", dto.getTemplateIds())
            .set("allow_view_item_detail", allowViewItemDetailValue);
    update(updateWrapper);
}
```

## ⏳ 待完成部分

### 后端控制器接口（5%）
**文件：** `med-pms/src/main/java/com/jp/med/pms/modules/pmsCalcTemplate/controller/PmsAwardCoefficientConfigController.java`

**需要添加：**
```java
/**
 * 批量更新指标详情查看权限
 */
@ApiOperation("批量更新指标详情查看权限")
@PutMapping("/batchUpdateAllowViewItemDetail")
public CommonResult<?> batchUpdateAllowViewItemDetail(@RequestBody PmsItemTemplateRelationDto dto) {
    pmsAwardCoefficientConfigWriteService.batchUpdateAllowViewItemDetail(dto);
    return CommonResult.success();
}
```

## 🚀 功能特点

1. **批量操作** - 支持同时对多个模板进行权限设置
2. **用户友好** - 提供清晰的操作按钮和反馈信息
3. **数据安全** - 使用事务确保数据一致性
4. **界面同步** - 操作完成后自动刷新数据
5. **错误处理** - 完善的错误提示和异常处理

## 📊 使用流程

1. **选择模板** → 在表格中勾选需要操作的模板
2. **执行操作** → 点击"批量开启详情查看"或"批量关闭详情查看"按钮
3. **系统处理** → 后端批量更新数据库中的`allow_view_item_detail`字段
4. **结果反馈** → 显示操作成功提示并自动刷新数据

## 🔄 API参数格式

```json
{
  "itemCode": "ITEM001",
  "templateIds": [1, 2, 3],
  "allowViewItemDetail": "1"
}
```

## 🎯 下一步

只需要后端开发人员在控制器中添加REST API接口即可完成整个功能！🎉
