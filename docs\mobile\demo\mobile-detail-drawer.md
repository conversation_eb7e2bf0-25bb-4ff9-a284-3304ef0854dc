# 移动端详情抽屉组件 📱

本文档介绍了移动端详情抽屉组件的使用方法，该组件为移动端CRUD卡片视图提供了详情查看功能。

## 🎯 功能特性

### 核心功能
- ✅ **全屏抽屉展示** - 使用n-drawer组件，提供沉浸式详情查看体验
- ✅ **字段动态渲染** - 支持多种字段类型和自定义渲染函数
- ✅ **操作按钮集成** - 内置操作按钮区域，支持自定义操作
- ✅ **响应式设计** - 适配不同屏幕尺寸
- ✅ **类型安全** - 完整的TypeScript类型定义

### 设计亮点
- **企业级UI** - 参考手术收入详情页面的设计风格
- **触摸友好** - 44px按钮高度，适合移动端操作
- **信息层次** - 清晰的字段标签和值展示
- **状态标识** - 支持标签、徽章等状态展示

## 🏗️ 组件架构

### 文件结构
```
src/components/common/crud/
├── mobileDetailDrawer.vue          # 详情抽屉组件
└── components/
    └── mobileCardView.vue          # 卡片视图组件（已集成）
```

### 类型定义
```typescript
// 字段配置接口
export interface DetailField {
  key: string                       // 字段键名
  label: string                     // 字段标签
  type?: 'text' | 'tag' | 'status' | 'date' | 'number'  // 字段类型
  icon?: Component                  // 字段图标
  render?: Component                // 自定义渲染函数
  highlight?: boolean               // 是否高亮显示
  formatter?: (value: any) => string // 值格式化函数
}

// 操作按钮接口
export interface DetailAction {
  key: string                       // 操作键名
  label: string                     // 操作标签
  type?: 'primary' | 'success' | 'warning' | 'error' | 'default'
  size?: 'small' | 'medium' | 'large'
  icon?: Component                  // 操作图标
  disabled?: boolean                // 是否禁用
  handler?: (data: any) => void     // 操作处理函数
}
```

## 📱 使用方法

### 1. 基础使用
```vue
<template>
  <mobile-detail-drawer
    v-model:show="showDrawer"
    title="手术收入"
    subtitle="MEDICAL_SERVICE_SURGERY"
    :data="selectedData"
    :fields="detailFields"
    :actions="detailActions"
    @action="handleAction"
  />
</template>

<script setup>
import { ref } from 'vue'
import MobileDetailDrawer from '@/components/common/crud/mobileDetailDrawer.vue'
import { CheckCircle, Upload, Download } from '@vicons/ionicons5'

const showDrawer = ref(false)
const selectedData = ref({
  projectName: '手术收入',
  projectCode: 'MEDICAL_SERVICE_SURGERY',
  reportSource: '门诊手术室手术费用统计',
  reportId: '2769',
  status: '已通过验证',
  configStatus: '配置完成待验证'
})

// 字段配置
const detailFields = ref([
  {
    key: 'projectName',
    label: '项目名称',
    type: 'text',
    icon: CheckCircle,
    highlight: true
  },
  {
    key: 'projectCode', 
    label: '项目编码',
    type: 'text'
  },
  {
    key: 'reportSource',
    label: '数据依赖卫宁报表',
    type: 'text',
    icon: Upload
  },
  {
    key: 'reportId',
    label: '数据依赖卫宁报表ID',
    type: 'number'
  },
  {
    key: 'status',
    label: '状态',
    type: 'tag'
  },
  {
    key: 'configStatus',
    label: '采集配置状态',
    type: 'status'
  }
])

// 操作按钮配置
const detailActions = ref([
  {
    key: 'validate',
    label: '完成',
    type: 'success',
    icon: CheckCircle
  },
  {
    key: 'download',
    label: '报表附件',
    type: 'default',
    icon: Download
  }
])

const handleAction = (action, data) => {
  console.log('操作:', action.key, '数据:', data)
}
</script>
```

### 2. 在CRUD组件中集成
```vue
<template>
  <mobile-card-view
    :data="tableData"
    :columns="columns"
    :enable-detail-drawer="true"
    :detail-fields="detailFields"
    :detail-actions="detailActions"
    @detail-action="handleDetailAction"
  />
</template>

<script setup>
// 自动集成详情抽屉功能
// 点击卡片时自动显示详情抽屉
</script>
```

## 🎨 样式设计

### 抽屉布局
```css
/* 全屏抽屉 */
.n-drawer {
  width: 100%;
}

/* 详情字段 */
.detail-field {
  padding: 16px;
  background: var(--n-color-target);
  border-radius: 8px;
  border: 1px solid var(--n-border-color);
}

/* 高亮字段 */
.detail-field.field-highlight {
  border-color: var(--n-color-primary);
  background: var(--n-color-primary-opacity);
}

/* 操作按钮 */
.action-btn {
  width: 100%;
  min-height: 44px;
  font-size: 16px;
  border-radius: 8px;
}
```

### 字段类型样式
- **文本字段** - 普通文本显示
- **标签字段** - 使用n-tag组件，自动识别状态颜色
- **状态字段** - 使用n-badge组件显示
- **日期字段** - 自动格式化为本地时间
- **数字字段** - 添加千分位分隔符

## 🔧 高级配置

### 1. 自定义渲染
```typescript
const detailFields = [
  {
    key: 'customField',
    label: '自定义字段',
    render: (value, row) => {
      return h('div', { class: 'custom-content' }, [
        h('span', '前缀: '),
        h('strong', value)
      ])
    }
  }
]
```

### 2. 字段格式化
```typescript
const detailFields = [
  {
    key: 'amount',
    label: '金额',
    type: 'number',
    formatter: (value) => `¥${value.toFixed(2)}`
  }
]
```

### 3. 条件显示
```typescript
// 组件内部会自动过滤空值字段
// 只显示有值的字段
const displayFields = computed(() => {
  return props.fields.filter(field => {
    const value = getFieldValue(field.key)
    return value !== null && value !== undefined && value !== ''
  })
})
```

## 📊 用户体验优化

### 1. 触摸友好设计
- **按钮尺寸** - 最小44px高度
- **间距设计** - 16px字段间距，8px内容间距
- **滚动优化** - 内容区域可滚动，头部固定

### 2. 视觉层次
- **标题层次** - 18px主标题，14px副标题
- **字段层次** - 14px标签，16px值
- **颜色层次** - 主色调、辅助色调、禁用色调

### 3. 交互反馈
- **加载状态** - 操作按钮loading状态
- **成功反馈** - 操作完成提示
- **错误处理** - 错误信息展示

## 🧪 测试用例

### 功能测试
```typescript
// 测试字段渲染
expect(wrapper.find('.field-label').text()).toBe('项目名称')
expect(wrapper.find('.field-value').text()).toBe('手术收入')

// 测试操作按钮
await wrapper.find('.action-btn').trigger('click')
expect(emitted('action')).toBeTruthy()

// 测试抽屉显示/隐藏
await wrapper.setProps({ show: true })
expect(wrapper.find('.n-drawer').isVisible()).toBe(true)
```

### 兼容性测试
- ✅ iOS Safari 兼容性良好
- ✅ Android Chrome 兼容性良好
- ✅ 各种屏幕尺寸适配正常
- ✅ 触摸操作响应灵敏

## 🎉 总结

移动端详情抽屉组件提供了：

1. **完整的详情展示方案** - 全屏抽屉 + 字段列表 + 操作按钮
2. **灵活的配置选项** - 支持多种字段类型和自定义渲染
3. **优秀的用户体验** - 触摸友好、视觉清晰、交互流畅
4. **无缝的集成方式** - 与CRUD卡片视图完美配合

这个组件显著提升了移动端数据详情查看的用户体验，让用户能够更方便地查看和操作数据详情。🚀

## 📚 相关文件

- `src/components/common/crud/mobileDetailDrawer.vue` - 详情抽屉组件
- `src/components/common/crud/components/mobileCardView.vue` - 卡片视图组件
- `docs/mobile-detail-drawer.md` - 本文档
