# BpmListTemplate 移动端列表模板使用指南

## 概述

`BpmListTemplate` 是一个专为移动端设计的通用列表模板组件，支持搜索、筛选、自动加载更多等功能。组件使用 Intersection Observer API 实现了基于元素可见性的智能自动加载功能。

## 功能特性

### 🚀 核心功能
- ✅ 搜索功能（支持快速搜索）
- ✅ 筛选功能（自定义筛选条件）
- ✅ 自动触底加载更多
- ✅ 手动加载更多（可选）
- ✅ 错误处理和重试机制
- ✅ 防抖机制优化性能
- ✅ 响应式设计

### 🎯 自动加载更多特性
- **智能检测**: 使用 Intersection Observer API 检测触发元素可见性
- **防抖处理**: 避免频繁触发加载请求
- **错误处理**: 加载失败时提供重试功能
- **状态管理**: 完整的加载状态提示
- **性能优化**: 可配置触发距离和防抖延迟

## Props 配置

```typescript
interface Props {
  // 基础数据
  list: any[]                           // 列表数据
  total: number                         // 数据总数
  loading: boolean                      // 加载状态
  queryParams: Record<string, any>      // 查询参数
  
  // 功能配置
  enableInfiniteScroll?: boolean        // 是否启用自动触底加载 (默认: true)
  getItemKey?: (item: any, index: number) => string | number  // 获取项目唯一键
  searchPlaceholder?: string            // 搜索框提示文本 (默认: '搜索...')
  
  // 性能优化配置
  loadTriggerDistance?: number          // 触发加载的距离底部距离 (默认: 100px)
  debounceDelay?: number               // 防抖延迟时间 (默认: 300ms)
}
```

## Events 事件

```typescript
interface Emits {
  query: []                            // 查询事件
  reset: []                           // 重置事件
  loadMore: []                        // 加载更多事件
  quickSearch: [keyword: string]       // 快速搜索事件
  loadError: [error: any]             // 加载错误事件
}
```

## 基础使用示例

```vue
<template>
  <BpmListTemplate
    :list="taskList"
    :total="total"
    :loading="loading"
    :queryParams="queryParams"
    :enableInfiniteScroll="true"
    :loadTriggerDistance="100"
    :debounceDelay="300"
    @query="handleQuery"
    @reset="handleReset"
    @loadMore="handleLoadMore"
    @quickSearch="handleQuickSearch"
    @loadError="handleLoadError"
  >
    <!-- 筛选条件插槽 -->
    <template #filters="{ queryParams }">
      <n-form-item label="状态">
        <n-select
          v-model:value="queryParams.status"
          :options="statusOptions"
          placeholder="请选择状态"
          clearable
        />
      </n-form-item>
      
      <n-form-item label="科室">
        <n-select
          v-model:value="queryParams.deptId"
          :options="deptOptions"
          placeholder="请选择科室"
          clearable
        />
      </n-form-item>
    </template>

    <!-- 卡片内容插槽 -->
    <template #card="{ item, index }">
      <div class="task-card" @click="handleCardClick(item)">
        <div class="card-header">
          <h3 class="task-title">{{ item.taskName }}</h3>
          <n-tag :type="getStatusType(item.status)" size="small">
            {{ getStatusText(item.status) }}
          </n-tag>
        </div>
        
        <div class="card-content">
          <div class="info-item">
            <span class="label">申请人:</span>
            <span class="value">{{ item.applicant }}</span>
          </div>
          <div class="info-item">
            <span class="label">申请时间:</span>
            <span class="value">{{ formatDate(item.createTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">科室:</span>
            <span class="value">{{ item.deptName }}</span>
          </div>
        </div>
        
        <div class="card-footer">
          <n-button size="small" @click.stop="handleApprove(item)">
            审批
          </n-button>
          <n-button size="small" quaternary @click.stop="handleView(item)">
            查看详情
          </n-button>
        </div>
      </div>
    </template>

    <!-- 额外操作按钮插槽 -->
    <template #actions>
      <n-button text @click="handleRefresh" class="action-btn">
        <template #icon>
          <n-icon size="20"><refresh-outline /></n-icon>
        </template>
        刷新
      </n-button>
    </template>
  </BpmListTemplate>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import BpmListTemplate from '@/components/mobile/BpmListTemplate.vue'
import { RefreshOutline } from '@vicons/ionicons5'

// 响应式数据
const taskList = ref([])
const total = ref(0)
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)

// 查询参数
const queryParams = reactive({
  status: '',
  deptId: '',
  keyword: ''
})

// 状态选项
const statusOptions = [
  { label: '待审批', value: 'pending' },
  { label: '已通过', value: 'approved' },
  { label: '已拒绝', value: 'rejected' }
]

// 科室选项
const deptOptions = ref([])

// 方法
const fetchData = async (isLoadMore = false) => {
  try {
    loading.value = true
    
    const params = {
      ...queryParams,
      pageNum: isLoadMore ? pageNum.value : 1,
      pageSize: pageSize.value
    }
    
    const response = await api.getTaskList(params)
    
    if (isLoadMore) {
      taskList.value.push(...response.data.list)
    } else {
      taskList.value = response.data.list
      pageNum.value = 1
    }
    
    total.value = response.data.total
    
  } catch (error) {
    console.error('获取数据失败:', error)
    $message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleQuery = () => {
  pageNum.value = 1
  fetchData()
}

const handleReset = () => {
  Object.keys(queryParams).forEach(key => {
    queryParams[key] = ''
  })
  pageNum.value = 1
  fetchData()
}

const handleLoadMore = () => {
  pageNum.value++
  fetchData(true)
}

const handleQuickSearch = (keyword: string) => {
  queryParams.keyword = keyword
  handleQuery()
}

const handleLoadError = (error: any) => {
  console.error('加载更多失败:', error)
  $message.error('加载更多失败')
}

const handleRefresh = () => {
  pageNum.value = 1
  fetchData()
}

// 卡片操作
const handleCardClick = (item: any) => {
  // 跳转到详情页
  $router.push(`/task/detail/${item.id}`)
}

const handleApprove = (item: any) => {
  // 审批操作
  $router.push(`/task/approve/${item.id}`)
}

const handleView = (item: any) => {
  // 查看详情
  $router.push(`/task/view/${item.id}`)
}

// 工具方法
const getStatusType = (status: string) => {
  const typeMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'error'
  }
  return typeMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    pending: '待审批',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return textMap[status] || '未知'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString()
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.task-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer transition-all;
}

.task-card:active {
  @apply bg-gray-50;
}

.card-header {
  @apply flex items-center justify-between mb-3;
}

.task-title {
  @apply text-base font-medium text-gray-900 flex-1 mr-2;
}

.card-content {
  @apply space-y-2 mb-3;
}

.info-item {
  @apply flex items-center;
}

.label {
  @apply text-sm text-gray-500 w-16 flex-shrink-0;
}

.value {
  @apply text-sm text-gray-900 flex-1;
}

.card-footer {
  @apply flex items-center justify-end gap-2 pt-2 border-t border-gray-100;
}
</style>
```

## 高级配置示例

### 禁用自动加载，使用手动加载

```vue
<template>
  <BmpListTemplate
    :list="list"
    :total="total"
    :loading="loading"
    :queryParams="queryParams"
    :enableInfiniteScroll="false"
    @loadMore="handleLoadMore"
  >
    <!-- 插槽内容 -->
  </BmpListTemplate>
</template>
```

### 自定义触发距离和防抖延迟

```vue
<template>
  <BmpListTemplate
    :list="list"
    :total="total"
    :loading="loading"
    :queryParams="queryParams"
    :loadTriggerDistance="200"
    :debounceDelay="500"
    @loadMore="handleLoadMore"
  >
    <!-- 插槽内容 -->
  </BmpListTemplate>
</template>
```

## 组件方法

通过 `ref` 可以调用组件的内部方法：

```vue
<template>
  <BmpListTemplate
    ref="listTemplateRef"
    :list="list"
    :total="total"
    :loading="loading"
    :queryParams="queryParams"
    @loadMore="handleLoadMore"
  >
    <!-- 插槽内容 -->
  </BmpListTemplate>
</template>

<script setup>
const listTemplateRef = ref()

// 停止加载更多状态
const stopLoading = () => {
  listTemplateRef.value?.stopLoadingMore()
}

// 重置加载状态
const resetState = () => {
  listTemplateRef.value?.resetLoadState()
}

// 重试加载
const retry = () => {
  listTemplateRef.value?.retryLoad()
}
</script>
```

## 最佳实践

### 1. 性能优化

```javascript
// 合理设置防抖延迟，避免频繁请求
const debounceDelay = 300

// 设置合适的触发距离，提前加载数据
const loadTriggerDistance = 100

// 合理的分页大小
const pageSize = 10
```

### 2. 错误处理

```javascript
const handleLoadError = (error) => {
  console.error('加载失败:', error)
  
  // 显示错误提示
  $message.error('网络异常，请检查网络连接')
  
  // 可以在这里添加错误上报逻辑
  reportError(error)
}
```

### 3. 状态管理

```javascript
// 在组件卸载前停止加载状态
onUnmounted(() => {
  listTemplateRef.value?.stopLoadingMore()
})

// 在数据重新加载时重置状态
const refreshData = () => {
  listTemplateRef.value?.resetLoadState()
  pageNum.value = 1
  fetchData()
}
```

## 注意事项

1. **数据一致性**: 确保 `list.length` 和 `total` 的值保持一致
2. **加载状态**: 正确管理 `loading` 状态，避免重复请求
3. **内存管理**: 长列表时注意内存使用，可考虑虚拟滚动
4. **网络异常**: 处理网络异常情况，提供重试机制
5. **用户体验**: 提供适当的加载提示和错误反馈

## 自动加载原理

组件使用 `Intersection Observer API` 监测触发元素的可见性：

```javascript
const observer = new IntersectionObserver(
  (entries) => {
    const entry = entries[0]
    if (entry.isIntersecting && hasMore.value && !loading.value && !loadingMore.value && !loadError.value) {
      debouncedLoadMore()
    }
  },
  {
    root: null, // 相对于视口
    rootMargin: `${loadTriggerDistance}px`, // 提前触发距离
    threshold: 0.1 // 当10%的目标元素可见时触发
  }
)
```

这种方式比传统的滚动监听更加高效，能够准确检测元素的可见性变化，并且不会阻塞主线程。 