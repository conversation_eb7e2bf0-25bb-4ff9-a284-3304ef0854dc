---
description: 
globs: 
alwaysApply: false
---
# J-CRUD 组件使用文档

## 概述 📋

`j-crud` 是一个功能完整的企业级 CRUD（增删改查）组件，基于 Vue3 + Naive UI 开发，提供了表格展示、数据操作、查询过滤、分页、Tab切换、导出等完整功能。

## 核心功能 ✨

- 🔍 **智能查询**: 支持精确查询、快速查询、防抖查询
- 📊 **数据表格**: 虚拟滚动、排序、筛选、汇总、树形结构
- ➕ **数据操作**: 新增、编辑、删除，支持模态框/抽屉模式
- 📑 **Tab切换**: 多标签页数据展示，带动画效果
- 📤 **数据导出**: Excel导出，模板下载
- 🔐 **权限控制**: 按钮级权限控制
- 🎨 **界面美化**: 动画过渡、响应式布局

## Props 配置 ⚙️

### 核心数据与方法 (Core Data & Methods)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `addMethod` | Function | - | 新增数据的方法 |
| `delMethod` | Function | - | 删除数据的方法 |
| `updateMethod` | Function | - | 更新数据的方法 |
| `queryMethod` | Function | - | 查询数据的方法 |
| `columns` | Array\<CRUDColumnInterface\> | [] | 表格列的定义 |
| `name` | String | '数据' | 组件名称，用于弹窗标题等 |

### 表格配置 (Table Configuration)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `cascade` | Boolean | true | 是否级联检查表中的行 |
| `checkedRowKeys` | Array\<string \| number\> | [] | 默认选中的行的key集合 |
| `dataTableSize` | 'small' \| 'medium' \| 'large' | 'medium' | 表格尺寸 |
| `defaultExpandAll` | Boolean | false | 默认是否展开所有树形节点 |
| `emptyDescription` | String | '暂无数据' | 表格为空时显示的描述文字 |
| `loadingDescription` | String | null | 表格加载状态的描述文字 |
| `noDataTableMode` | Boolean | false | 是否不显示数据表格 |
| `dataTableBordered` | Boolean | false | 是否显示数据表格的边框 |
| `dataTableStriped` | Boolean | false | 是否显示数据表格的斑马线 |
| `paging` | Boolean | true | 是否分页查询 |
| `defaultPageSize` | Number | 20 | 默认分页大小 |
| `pageSizes` | Array\<number\> | [10, 20, 30, 40, 100, 500, 5000] | 可配置的每页条数选项 |
| `rowClassName` | String \| Function | - | 表格行的类名 |
| `rowKey` | String | 'key' | 表格行数据的唯一标识符字段名 |
| `scrollX` | String \| Number | '' | 表格横向滚动的宽度 |
| `showSelectColumn` | Boolean | true | 是否显示选择列的控件 |
| `showSummary` | Boolean | false | 是否显示表格的合计行 |
| `singleLine` | Boolean | true | 是否不显示列之间的分割线 |
| `singleColumn` | Boolean | false | 是否显示单列模式 |
| `sorter` | Boolean | true | 是否开启表格数据排序功能 |
| `virtualScroll` | Boolean | false | 是否开启虚拟滚动 |

### 查询与表单配置 (Query & Form Configuration)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `showSaveSpin` | Boolean | false | 新增或编辑操作完成后是否显示保存加载动画 |
| `clearQueryData` | Array\<string\> | - | 跳转到其他页面时需要清除的查询条件字段名列表 |
| `defaultOpenAccurateQuery` | Boolean | false | 是否默认展开精确查询区域 |
| `hideEmptyFormItems` | Boolean | false | 隐藏查询表单中的空表单项 |
| `labelWidth` | String | 'auto' | 表单项标签的宽度 |
| `queryForm` | Object | {} | 查询表单的数据对象 |
| `quickQueryConfig` | Object | {} | 快速查询配置 |
| `showReloadFilter` | Boolean | true | 是否显示刷新过滤器的按钮 |

#### quickQueryConfig 配置说明

```javascript
{
  enable: false,        // 是否启用快速查询
  excludeKey: []        // 排除某些字段的变化不触发快速查询
}
```

### UI与交互控制 (UI & Interaction Control)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `addButtonAlias` | String | '新增' | 新增按钮的别名 |
| `dontRowClickClassNameList` | Array | [] | 指定某些类名的元素被点击时不触发行点击事件 |
| `extTableButtons` | Array\<VNode\> | - | 拓展的表格操作按钮 |
| `showConAddButton` | Boolean | true | 是否显示新增按钮（条件控制） |
| `showAddButton` | Boolean | false | 是否显示行内的新增按钮 |
| `showOperationButton` | Boolean | true | 是否显示行内的操作按钮 |
| `tableRowClick` | Boolean | false | 表格行是否可点击 |

### 导出功能 (Export Functionality)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `exportConfig` | Object | - | 表格数据导出的配置项 |
| `showExport` | Boolean | false | 是否显示导出按钮 |
| `showExportExcelTemplate` | Boolean | true | 是否显示导出Excel模板文件的按钮 |

### Tab页配置 (Tab Configuration)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `defaultCheckTab` | String \| Number | '' | 默认选中的Tab的名称或索引 |
| `enableRouteTab` | Boolean | false | 是否检查路由参数 'crudTabName' 来设置初始 Tab |
| `tabs` | Array\<JTab\> | - | Tab页签的配置数组 |
| `useTabSlot` | Boolean | false | 是否使用Tab标题的插槽来自定义显示 |

### 树形结构配置 (Tree Structure Configuration)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `treeNode` | Object | - | 树形结构配置，指定id、parentId字段等 |

### 弹窗/抽屉配置 (Modal/Drawer Configuration)

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `addOrEditCallback` | Function | - | 新增或编辑操作的回调函数 |
| `addOrEditModalAfter` | Function | - | 新增或编辑弹窗/抽屉打开后执行的回调 |
| `addOrEditShowMode` | 'modal' \| 'drawer' | 'modal' | 新增或编辑弹窗/抽屉的显示模式 |
| `addOrEditToFormData` | Boolean | false | 新增或编辑时是否将数据转换为FormData格式提交 |
| `customDrawerHeight` | String | '80%' | 自定义抽屉的高度 |
| `customDrawerWidth` | String | '40%' | 自定义抽屉的宽度 |
| `customFormStyle` | Object | { width: '40%', height: '100%' } | 自定义表单（Modal模式下）的样式 |

## 事件 (Events) 📡

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:queryForm` | queryForm | 查询表单数据更新 |
| `queryAfter` | queryForm | 查询开始后触发 |
| `queryComplete` | originData | 查询完成后触发 |
| `add` | row | 新增按钮点击时触发 |
| `edit` | row | 编辑按钮点击时触发 |
| `addOrEditClose` | - | 新增/编辑弹窗关闭时触发 |
| `success` | actionForm | 操作成功时触发 |
| `rowClick` | row | 表格行点击时触发 |
| `update:checked-row-keys` | rowKeys | 选中行变化时触发 |
| `update:filters` | filters, sourceColumn | 筛选条件变化时触发 |

## 方法 (Methods) 🔧

### 查询相关

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `queryData` | - | - | 执行查询操作 |
| `reload` | - | - | 刷新表格数据 |
| `refresh` | - | - | 刷新表格数据（别名） |
| `accurateQuery` | flag: boolean | - | 控制精确查询区域的展开/收起 |
| `getQueryParam` | - | Object | 获取当前查询参数 |

### 表单相关

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `addClick` | - | - | 新增按钮点击处理 |
| `showAddModal` | data: any | - | 打开新增弹窗并携带数据 |
| `onClose` | - | - | 关闭弹窗/抽屉 |
| `confirmActionFormClick` | - | - | 确认表单提交 |
| `resetActionFormClick` | - | - | 重置表单数据 |

### Tab相关

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `tabChange` | name: string \| number, query?: boolean, resetPage?: boolean | - | 切换Tab页签 |

### 其他

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `initFormItems` | - | - | 初始化表单项 |
| `adjustHeight` | exeQuery?: boolean | - | 调整组件高度 |
| `updateColumns` | columns: any | - | 更新表格列配置 |

## 列配置 (Column Configuration) 📊

### CRUDColumnInterface 接口

```typescript
interface CRUDColumnInterface {
  key: string                    // 列的唯一标识
  title: string                  // 列标题
  realTitle?: string             // 真实标题（用于表单）
  realKey?: string               // 真实字段名
  type?: ContainerValueType      // 组件类型
  width?: number                 // 列宽度
  minWidth?: number              // 最小宽度
  maxWidth?: number              // 最大宽度
  fixed?: 'left' | 'right'       // 固定列位置
  align?: 'left' | 'center' | 'right'  // 对齐方式
  
  // 显示控制
  show?: boolean                 // 是否在表单中显示
  dynamicShow?: boolean          // 动态显示控制
  tableColumnShow?: boolean      // 是否在表格中显示
  tableColumnHidden?: boolean    // 是否隐藏列
  hide?: boolean                 // 是否隐藏
  
  // 表单配置
  required?: boolean             // 是否必填
  disabled?: boolean             // 是否禁用
  placeholder?: string           // 占位符
  defaultValue?: any             // 默认值
  multiple?: boolean             // 是否多选
  clearable?: boolean            // 是否可清空
  
  // 数据类型
  dataType?: string              // 数据类型
  inputType?: string             // 输入类型
  
  // 字典和选项
  dictType?: string              // 字典类型
  selection?: Array<Option>      // 选项数据
  
  // 渲染相关
  render?: Function              // 自定义渲染函数
  tagRender?: boolean            // 是否使用标签渲染
  tagRenderHandler?: Function    // 标签渲染处理器
  
  // 验证
  validator?: Function           // 自定义验证器
  feedback?: Function            // 反馈函数
  feedbackText?: string          // 反馈文本
  validationStatus?: ValidateTypes  // 验证状态
  
  // 排序和筛选
  sorter?: boolean | 'default'   // 是否支持排序
  filter?: boolean               // 是否支持筛选
  resizable?: boolean            // 是否可调整大小
  
  // 汇总
  summary?: boolean              // 是否参与汇总
  summaryFormat?: Function       // 汇总格式化函数
  summaryFractionDigits?: number // 汇总小数位数
  rowDotSummaryFn?: Function     // 行级汇总控制函数
  
  // 回调函数
  callBack?: Function            // 值变化回调
  selectChange?: Function        // 选择变化回调
  
  // 树形结构
  children?: Array<CRUDColumnInterface>  // 子列
}
```

### 常用列类型 (ContainerValueType)

```typescript
enum ContainerValueType {
  INPUT = 'input',                    // 输入框
  SELECT = 'select',                  // 下拉选择
  TREE_SELECT = 'treeSelect',         // 树形选择
  DATE = 'date',                      // 日期选择
  DATE_TIME = 'datetime',             // 日期时间选择
  DATE_RANGE = 'dateRange',           // 日期范围选择
  DATE_TIME_RANGE = 'datetimeRange',  // 日期时间范围选择
  TEXTAREA = 'textarea',              // 文本域
  NUMBER = 'number',                  // 数字输入
  SWITCH = 'switch',                  // 开关
  CHECKBOX = 'checkbox',              // 复选框
  RADIO = 'radio',                    // 单选框
  UPLOAD = 'upload',                  // 文件上传
  CASCADER = 'cascader',              // 级联选择
}
```

## 基础使用示例 💡

### 简单的CRUD表格

```vue
<template>
  <j-crud
    ref="crudRef"
    :columns="columns"
    :queryMethod="queryMethod"
    :addMethod="addMethod"
    :updateMethod="updateMethod"
    :delMethod="delMethod"
    name="用户管理"
    @queryComplete="onQueryComplete"
  />
</template>

<script setup>
import { ref } from 'vue'
import { ContainerValueType } from '@jtypes'

const crudRef = ref()

// 定义表格列
const columns = ref([
  {
    key: 'id',
    title: 'ID',
    width: 80,
    tableColumnShow: true,
    show: false
  },
  {
    key: 'name',
    title: '姓名',
    type: ContainerValueType.INPUT,
    required: true,
    width: 120
  },
  {
    key: 'email',
    title: '邮箱',
    type: ContainerValueType.INPUT,
    width: 200
  },
  {
    key: 'status',
    title: '状态',
    type: ContainerValueType.SELECT,
    dictType: 'user_status',
    tagRender: true,
    width: 100
  },
  {
    key: 'createTime',
    title: '创建时间',
    type: ContainerValueType.DATE_TIME,
    width: 180,
    show: false
  }
])

// API 方法
const queryMethod = async (params) => {
  // 查询接口调用
  return await api.getUserList(params)
}

const addMethod = async (data) => {
  // 新增接口调用
  return await api.addUser(data)
}

const updateMethod = async (data) => {
  // 更新接口调用
  return await api.updateUser(data)
}

const delMethod = async (row) => {
  // 删除接口调用
  return await api.deleteUser(row.id)
}

const onQueryComplete = (data) => {
  console.log('查询完成，数据：', data)
}
</script>
```

### 带Tab的CRUD组件

```vue
<template>
  <j-crud
    :columns="currentColumns"
    :tabs="tabs"
    :queryMethod="queryMethod"
    :addMethod="addMethod"
    :updateMethod="updateMethod"
    :delMethod="delMethod"
    :enableRouteTab="true"
    name="订单管理"
  />
</template>

<script setup>
import { ref, computed } from 'vue'

const tabs = ref([
  {
    name: 'all',
    tab: '全部订单',
    columns: baseColumns.value,
    tabChange: async (tab) => {
      // Tab切换时的处理逻辑
      console.log('切换到全部订单')
    }
  },
  {
    name: 'pending',
    tab: '待处理',
    useBadge: true,
    badge: 0,
    columns: baseColumns.value.filter(col => col.key !== 'status')
  },
  {
    name: 'completed',
    tab: '已完成',
    columns: baseColumns.value
  }
])

const baseColumns = ref([
  {
    key: 'orderNo',
    title: '订单号',
    type: ContainerValueType.INPUT,
    width: 150
  },
  {
    key: 'customerName',
    title: '客户名称',
    type: ContainerValueType.INPUT,
    width: 120
  },
  {
    key: 'amount',
    title: '金额',
    type: ContainerValueType.NUMBER,
    width: 100,
    summary: true,
    summaryFormat: (value) => `￥${value.toFixed(2)}`
  },
  {
    key: 'status',
    title: '状态',
    type: ContainerValueType.SELECT,
    dictType: 'order_status',
    tagRender: true,
    width: 100
  }
])

// 当前激活的列配置
const currentColumns = computed(() => {
  const activeTab = tabs.value.find(tab => tab.name === currentTabName.value)
  return activeTab?.columns || baseColumns.value
})
</script>
```

### 树形结构CRUD

```vue
<template>
  <j-crud
    :columns="columns"
    :queryMethod="queryMethod"
    :addMethod="addMethod"
    :updateMethod="updateMethod"
    :delMethod="delMethod"
    :treeNode="treeConfig"
    :defaultExpandAll="true"
    name="部门管理"
  />
</template>

<script setup>
const treeConfig = {
  id: 'id',
  parentId: 'parentId'
}

const columns = ref([
  {
    key: 'name',
    title: '部门名称',
    type: ContainerValueType.INPUT,
    required: true,
    width: 200
  },
  {
    key: 'parentId',
    title: '上级部门',
    type: ContainerValueType.TREE_SELECT,
    width: 150,
    show: true
  },
  {
    key: 'sort',
    title: '排序',
    type: ContainerValueType.NUMBER,
    width: 80
  }
])
</script>
```

### 自定义操作按钮

```vue
<template>
  <j-crud
    :columns="columns"
    :queryMethod="queryMethod"
    :extTableButtons="extButtons"
    name="商品管理"
  />
</template>

<script setup>
import { h } from 'vue'
import { NButton, NPopover } from 'naive-ui'

const extButtons = [
  h(NPopover, { style: 'padding: 5px', showArrow: false }, {
    trigger: () => h(NButton, {
      size: 'small',
      type: 'warning',
      onClick: (e) => {
        e.stopPropagation()
        handleCustomAction(row)
      }
    }, { default: () => '自定义操作' }),
    default: () => '执行自定义操作'
  })
]

const handleCustomAction = (row) => {
  console.log('执行自定义操作', row)
  // 自定义操作逻辑
}
</script>
```

## 高级配置 🚀

### 快速查询配置

```vue
<template>
  <j-crud
    :columns="columns"
    :queryMethod="queryMethod"
    :quickQueryConfig="quickQueryConfig"
    v-model:queryForm="queryForm"
  />
</template>

<script setup>
const quickQueryConfig = ref({
  enable: true,
  excludeKey: ['pageNum', 'pageSize'] // 排除分页参数
})

const queryForm = ref({
  name: '',
  status: null,
  dateRange: null
})
</script>
```

### 导出配置

```vue
<template>
  <j-crud
    :columns="columns"
    :queryMethod="queryMethod"
    :showExport="true"
    :exportConfig="exportConfig"
  />
</template>

<script setup>
const exportConfig = ref({
  excelName: '用户数据导出',
  dontExport: ['id'], // 不导出的字段
  isComplex: false,   // 是否复杂导出
  exportFunc: queryMethod, // 导出数据获取方法
})
</script>
```

### 表单验证

```vue
<script setup>
const columns = ref([
  {
    key: 'email',
    title: '邮箱',
    type: ContainerValueType.INPUT,
    required: true,
    validator: (rule, value) => {
      if (!value) {
        return new Error('请输入邮箱')
      }
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return new Error('邮箱格式不正确')
      }
      return true
    }
  }
])
</script>
```

## 样式定制 🎨

### CSS 变量

```css
/* 自定义表格样式 */
.j-crud {
  --crud-border-radius: 8px;
  --crud-shadow: 0 2px 8px rgba(0,0,0,0.1);
  --crud-header-bg: #fafafa;
}

/* 动画效果调整 */
.table-fade-enter-active {
  transition: all 0.3s ease;
}

.table-fade-leave-active {
  transition: all 0.2s ease;
}
```

### 自定义渲染

```vue
<script setup>
const columns = ref([
  {
    key: 'avatar',
    title: '头像',
    width: 80,
    render: (row) => {
      return h('img', {
        src: row.avatar,
        style: 'width: 32px; height: 32px; border-radius: 50%'
      })
    }
  },
  {
    key: 'status',
    title: '状态',
    tagRender: true,
    tagRenderHandler: (value) => {
      const statusMap = {
        1: 'success',
        2: 'warning', 
        3: 'error'
      }
      return statusMap[value] || 'default'
    }
  }
])
</script>
```

## 最佳实践 💡

### 1. 性能优化

- 大数据量时启用虚拟滚动
- 合理配置分页大小
- 使用防抖查询避免频繁请求

```vue
<j-crud
  :virtualScroll="true"
  :defaultPageSize="50"
  :quickQueryConfig="{ enable: true }"
/>
```

### 2. 用户体验

- 配置合适的加载状态描述
- 使用动画过渡效果
- 提供清晰的操作反馈

```vue
<j-crud
  :loadingDescription="'数据加载中...'"
  :emptyDescription="'暂无相关数据'"
  addButtonAlias="创建新用户"
/>
```

### 3. 权限控制

- 利用系统权限控制按钮显示
- 设置行级操作权限

```vue
<script setup>
const columns = ref([
  {
    key: 'actions',
    title: '操作',
    render: (row) => {
      if (!row.canEdit) {
        return h('span', '无权限')
      }
      return h(NButton, { onClick: () => editRow(row) }, '编辑')
    }
  }
])
</script>
```

### 4. 错误处理

- 统一错误处理
- 友好的错误提示

```vue
<script setup>
const queryMethod = async (params) => {
  try {
    return await api.getUserList(params)
  } catch (error) {
    window.$message.error('数据加载失败，请重试')
    throw error
  }
}
</script>
```

## 注意事项 ⚠️

1. **数据格式**: 确保后端返回的数据格式符合组件要求
2. **权限控制**: 正确配置页面按钮权限
3. **内存管理**: 大量数据时注意内存使用
4. **响应式**: 注意在小屏幕设备上的显示效果
5. **浏览器兼容**: 确保目标浏览器支持所使用的特性

## 常见问题 ❓

### Q: 表格数据不显示怎么办？
A: 检查以下几点：
- `queryMethod` 是否正确返回数据
- 数据格式是否符合要求（分页模式需要 `{ total, records }` 格式）
- `columns` 配置是否正确

### Q: 新增/编辑弹窗不显示怎么办？
A: 检查：
- 是否配置了 `addMethod` 和 `updateMethod`
- 列配置中是否有 `show: true` 的字段
- 权限配置是否正确

### Q: Tab切换没有动画效果怎么办？
A: 确保：
- 正确引入了组件样式
- 没有覆盖动画相关的CSS类
- 浏览器支持CSS transition

---

📖 **更多示例和详细配置请参考项目中的具体实现代码。**