# PMS 绩效管理系统用户操作手册

## 📖 系统概述

PMS（Performance Management System）绩效管理系统是一套专为医院设计的综合性绩效管理平台，旨在提供科学、公正、透明的绩效评价和奖金分配机制。

### 🎯 系统主要功能
- **配置管理**：科室数据配置、收费项目配置、资产收费配置等
- **成本管控**：成本数据采集、医疗服务收入统计、成本管控报表等
- **效益分析**：设备效益报表分析
- **绩效配置**：绩效计算模板、信息采集配置、奖励系数配置等
- **绩效数据处理**：绩效信息采集、科室月度绩效计算、月度绩效确认申请等
- **绩效查询分析**：科室人数统计、管理岗位绩效、月度绩效汇总等
- **数据自动化采集**：绩效数据仓库、脚本编辑、自动化采集配置等

---

## 🏗️ 系统架构

### 菜单结构树

```
绩效管理系统 (40000)
├── 配置管理 (40001)
│   ├── 科室数据配置 (40002)
│   ├── 收费项目配置 (40003)
│   ├── 资产收费配置 (40004)
│   ├── 医嘱项目配置 (40005)
│   ├── 用户分组 (580)
│   ├── 用户配置 (581)
│   ├── 自定义用户配置 (582)
│   └── 科室人员上报配置 (583)
│       ├── 人数上报配置 (584)
│       ├── 人数计算规则配置 (585)
│       ├── 上报科室类型配置 (586)
│       └── 上报人员类型配置 (587)
├── 成本管控 (40006)
│   ├── 成本管控采集配置 (40007)
│   ├── 成本管控数据采集 (40008)
│   ├── 医疗服务收入 (40009)
│   ├── 成本管控报表 (40010)
│   ├── 成本管控报表1 (40011)
│   └── 成本管控报表配置 (579)
├── 效益分析 (40012)
│   └── 设备效益报表 (40013)
├── 绩效配置与模板 (40015)
│   ├── 绩效计算科室模版配置 (40016)
│   ├── 绩效信息采集配置 (40017)
│   ├── 绩效奖计算项目配置 (40018)
│   ├── 奖励系数配置 (40019)
│   ├── 采集科室配置 (40020)
│   └── 科室每月人数上报配置 (40021)
├── 绩效数据处理 (40022)
│   ├── 绩效信息采集 (40023)
│   ├── 科室月度绩效计算 (40024)
│   ├── 月度绩效确认申请 (40025)
│   ├── 科室每月人数上报 (40026)
│   └── 每月上报任务 (40027)
├── 绩效查询与分析 (40028)
│   ├── 每月上报看板 (40030)
│   ├── 月度绩效汇总 (40029)
│   ├── 科室每月人数 (40031)
│   └── 管理岗位统计绩效 (40032)
└── 数据自动化采集 (40033)
    ├── 绩效数据仓库 (40034)
    └── 脚本编辑 (40035)
```

---

## 🛠️ 一、配置管理模块

### 1.1 科室数据配置

**路径**：`pms/pmsConfig/orgDataMapConfig`

**功能说明**：配置科室代码映射关系，建立各系统间科室数据的对应关系。

#### 操作步骤：

1. **基础查询**
   - 选择来源系统标识（如：HIS、HRP等）
   - 输入绩效科室名称进行筛选
   - 输入来源科室代码或名称
   - 选择是否有效状态

2. **新增科室映射**
   - 点击"新增"按钮
   - 填写来源系统标识
   - 输入来源科室代码和名称
   - 设置对应的绩效科室名称
   - 关联HRM系统ID
   - 设置激活状态

3. **科室层级关系查看**
   - 点击"查看科室层级图"按钮
   - 系统会展示科室的层级关系树状图
   - 支持图形化查看科室关系网络

#### 重要提示：
⚠️ 科室映射配置是绩效计算的基础，请确保映射关系准确无误。

### 1.2 收费项目配置

**路径**：`pms/pmsConfig/itemChargeCfg`

**功能说明**：配置医院收费项目与绩效计算的关联关系。

#### 主要功能：
- 收费项目编码管理
- 收费项目分类设置
- 绩效系数配置
- 有效期管理

### 1.3 资产收费配置

**路径**：`pms/pmsConfig/assetChargeCfg`

**功能说明**：配置医院固定资产的收费标准和绩效分配规则。

### 1.4 用户分组管理

**路径**：`pms/pmsConfig/userGroup`

**功能说明**：管理PMS系统的用户分组，控制用户权限范围。

#### 配置字段：
- **分组名称**：用户组的显示名称
- **HRP科室ID**：关联HRM系统的科室标识
- **绩效科室名称**：对应的绩效科室
- **激活状态**：控制分组是否生效

---

## 💰 二、成本管控模块

### 2.1 成本管控采集配置

**路径**：`pms/pmsCalc/pmsIAEBalance/IaeBalanceConfig`

**功能说明**：配置成本管控数据的采集规则和计算方式。

#### 配置项目：
- 成本科目设置
- 收入科目配置
- 分摊规则定义
- 计算周期设定

### 2.2 成本管控数据采集

**路径**：`pms/pmsCalc/pmsIAEBalance/IaeBalanceCollection`

**功能说明**：执行成本管控数据的采集和录入。

#### 操作流程：
1. **选择采集期间**
   - 设置数据采集的起止时间
   - 选择采集范围（科室、项目）

2. **数据采集执行**
   - 启动自动采集程序
   - 监控采集进度
   - 处理异常数据

3. **数据验证确认**
   - 检查采集数据的完整性
   - 验证数据的准确性
   - 确认数据入库

### 2.3 医疗服务收入

**路径**：`pms/pmsCalc/pmsIAEBalanceReport/medicalServiceRevenue`

**功能说明**：统计和分析医疗服务产生的收入数据。

#### 报表功能：
- 按科室统计收入
- 按服务项目分类收入
- 收入趋势分析
- 收入结构分析

### 2.4 成本管控报表

**路径**：`pms/pmsCalc/pmsIAEBalanceReport`

**功能说明**：生成各类成本管控分析报表。

#### 报表类型：
- 科室成本分析报表
- 收支平衡分析报表
- 成本效益分析报表
- 预算执行情况报表

---

## 📊 三、效益分析模块

### 3.1 设备效益报表

**路径**：`pms/pmsInseqiEfft`

**功能说明**：分析医疗设备的使用效益和投资回报。

#### 分析维度：
- 设备使用率统计
- 设备收入贡献分析
- 设备成本效益计算
- 设备投资回报率

#### 操作指南：
1. **选择分析设备**
   - 按设备类型筛选
   - 按科室筛选设备
   - 按设备价值范围筛选

2. **设置分析周期**
   - 选择统计的起止日期
   - 设置分析的时间粒度（月、季、年）

3. **生成效益报表**
   - 查看设备使用情况
   - 分析收入贡献度
   - 计算投资回报率

---

## ⚙️ 四、绩效配置与模板模块

### 4.1 绩效计算科室模版配置

**路径**：`pms/pmsCalc/pmsAwardCalcDeptTemplateConfig`

**功能说明**：为不同科室配置专属的绩效计算模板。

#### 配置要素：
- **模板名称**：便于识别的模板名称
- **适用科室**：指定模板适用的科室范围
- **计算规则**：定义绩效计算的具体规则
- **权重配置**：设置各项指标的权重比例
- **生效时间**：模板的有效期设置

#### 操作步骤：
1. **创建新模板**
   - 点击"新增模板"
   - 输入模板基本信息
   - 选择适用科室类型

2. **配置计算规则**
   - 添加绩效指标项目
   - 设置指标权重
   - 定义计算公式

3. **模板测试验证**
   - 使用历史数据测试
   - 验证计算结果合理性
   - 调整配置参数

### 4.2 绩效信息采集配置

**路径**：`pms/pmsCalc/pmsInformationCollectionConfig`

**功能说明**：配置绩效数据的采集规则和采集周期。

#### 配置内容：
- **采集项目**：定义需要采集的绩效指标
- **采集方式**：设置自动采集或手工录入
- **采集周期**：设定数据采集的频率
- **数据源配置**：指定数据来源系统
- **质量校验规则**：设置数据质量检查标准

### 4.3 绩效奖计算项目配置

**路径**：`pms/pmsCalc/pmsAwardCalcItemConfig`

**功能说明**：配置参与绩效奖金计算的具体项目和规则。

#### 项目类型：
- **工作量指标**：门诊量、住院量、手术量等
- **质量指标**：患者满意度、医疗质量评分等
- **效率指标**：床位周转率、平均住院日等
- **成本指标**：科室成本控制情况
- **创新指标**：科研成果、技术创新等

### 4.4 奖励系数配置

**路径**：`pms/pmsCalc/pmsAwardCoefficientConfig`

**功能说明**：配置不同情况下的奖励系数调整规则。

#### 系数类型：
- **岗位系数**：不同岗位的基础系数
- **职称系数**：按职称等级设定的系数
- **风险系数**：高风险科室的补偿系数
- **难度系数**：工作难度对应的系数
- **季节性系数**：考虑季节性变化的系数

---

## 📈 五、绩效数据处理模块

### 5.1 绩效信息采集

**路径**：`pms/pmsCalc/pmsInformationCollection`

**功能说明**：系统核心功能之一，负责收集各科室的绩效数据。

#### 主要特性：
- **多频率采集**：支持日报、周报、月报等不同频率
- **多渠道采集**：Excel导入、API接口、手工录入
- **实时监控**：采集进度实时跟踪
- **质量控制**：数据完整性和准确性校验

#### 操作流程：

1. **筛选采集项目**
   ```
   左侧筛选面板：
   - 全部项目/按项目名称筛选
   - 按采集频率筛选（日报/周报/月报）
   - 搜索功能：快速定位特定项目
   ```

2. **查看采集详情**
   - 选择具体的绩效项目
   - 查看采集元数据信息
   - 监控采集进度和状态

3. **数据上报操作**
   - 手动新增采集信息
   - Excel模板下载和上传
   - 数据校验和确认

#### 采集状态说明：
- 🟢 **已完成**：数据采集完成并通过校验
- 🟡 **进行中**：正在采集数据
- 🔴 **待处理**：等待数据上报
- ⚪ **已超期**：超过规定时间未完成采集

### 5.2 科室月度绩效计算

**路径**：`pms/pmsCalc/monthlyPerformance`

**功能说明**：系统的核心计算模块，执行科室月度绩效的综合计算。

#### 界面布局：

```
┌─────────────────────────────────────────────────────────────┐
│                    顶部查询区域                               │
│  [月份选择] [查询] [查看所有科室绩效] [科室绩效总览]           │
├─────────────────────────────────────────────────────────────┤
│  左侧科室列表  │              右侧内容区域                    │
│               │  ┌─────────────────────────────────────┐     │
│  - 科室A      │  │        标签页切换区域               │     │
│  - 科室B      │  │ [数据总览][汇总统计][自定义指标]..  │     │
│  - 科室C      │  ├─────────────────────────────────────┤     │
│               │  │                                     │     │
│               │  │        绩效数据表格                 │     │
│               │  │                                     │     │
│               │  └─────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

#### 核心功能：

1. **月度数据查询**
   - 选择计算月份
   - 系统自动加载该月的绩效数据
   - 显示科室计算状态

2. **科室绩效计算**
   - 选择具体科室
   - 查看科室的绩效项目详情
   - 执行绩效计算
   - 查看计算结果

3. **多维度数据展示**
   - **数据总览**：显示科室的所有绩效项目数据
   - **汇总统计**：提供科室绩效的统计摘要
   - **自定义指标**：支持自定义绩效指标的录入和计算
   - **辅助信息**：显示计算相关的辅助信息

4. **批量操作功能**
   - 批量计算科室绩效
   - 批量导出Excel报表
   - 批量数据校验

#### 计算状态监控：
- 📊 **计算进度条**：实时显示计算完成情况
- 🎯 **错误提示**：高亮显示计算错误的项目
- ✅ **完成标识**：标记已完成计算的科室

### 5.3 月度绩效确认申请

**路径**：`pms/pmsCalc/monthlyPerformanceApply`

**功能说明**：提供绩效数据的审核确认流程。

#### 申请流程：
1. **数据准备**
   - 完成月度绩效计算
   - 数据质量检查
   - 生成汇总报表

2. **提交申请**
   - 填写申请表单
   - 上传支撑材料
   - 提交审核流程

3. **审核跟踪**
   - 查看审核状态
   - 处理审核意见
   - 确认最终结果

### 5.4 科室每月人数上报

**路径**：`pms/PmsMonthlyDeptStaffNumberReport`

**功能说明**：科室人员编制数据的月度上报功能。

#### 上报类型：
- **临床医生**：各级别医生人数统计
- **临床护理**：护理人员人数统计  
- **医技人员**：医技科室人员统计
- **行政后勤**：管理和后勤人员统计

#### 操作步骤：

1. **选择上报科室**
   ```
   权限说明：
   - 管理员：可选择任意科室进行上报
   - 普通用户：只能上报本科室数据
   ```

2. **选择上报类型和月份**
   - 从下拉列表选择上报类型
   - 选择上报的月份
   - 系统自动加载配置规则

3. **填写人员数据**
   - 按照人员类型分类填写
   - 支持小数点（如：2.5人）
   - 系统自动校验数据合理性

4. **提交和确认**
   - 数据校验通过后提交
   - 生成上报记录
   - 支持数据修改和重新提交

### 5.5 每月上报任务

**路径**：`pms/PmsMonthlyReportTask`

**功能说明**：统一管理所有的月度数据上报任务。

#### 任务类型：
- **绩效指标上报**：各类绩效数据上报任务
- **成本管控上报**：成本相关数据上报
- **人员数据上报**：人员编制数据上报
- **设备数据上报**：设备使用数据上报

---

## 🔍 六、绩效查询与分析模块

### 6.1 每月上报看板

**路径**：`pms/PmsMonthlyReportTaskBoard`

**功能说明**：提供可视化的月度上报任务监控看板。

#### 看板特性：

1. **实时统计展示**
   ```
   ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
   │   绩效指标统计  │ │  成本控制统计   │ │  自动采集统计   │
   │                │ │                │ │                │
   │  已上报: 45/60 │ │  已上报: 23/30 │ │  运行中: 15/20 │
   │  完成率: 75%   │ │  完成率: 77%   │ │  成功率: 95%   │
   └─────────────────┘ └─────────────────┘ └─────────────────┘
   ```

2. **多维度筛选**
   - 按月份筛选任务
   - 按科室筛选数据
   - 按上报状态筛选
   - 按指标类型筛选

3. **任务操作功能**
   - **查看详情**：查看具体的上报数据
   - **催促上报**：发送上报提醒
   - **催促记录**：查看催促历史
   - **数据重导**：重新导入数据

4. **进度可视化**
   - 进度条显示完成情况
   - 颜色编码区分状态
   - 图表展示趋势分析

#### 催促功能：
- **单个催促**：针对特定科室或指标进行催促
- **批量催促**：批量发送催促通知
- **自定义消息**：编辑催促提醒内容
- **催促记录**：追踪催促历史和响应情况

### 6.2 月度绩效汇总

**路径**：`pms/pmsCalc/monthlyPerformanceSummary`

**功能说明**：生成月度绩效的综合汇总分析报告。

#### 汇总维度：
- **按科室汇总**：各科室绩效排名和对比
- **按指标汇总**：各绩效指标的整体情况
- **按时间汇总**：不同时期的绩效趋势
- **按岗位汇总**：不同岗位的绩效分布

#### 分析图表：
- 📊 **柱状图**：科室绩效对比
- 📈 **折线图**：绩效趋势分析  
- 🥧 **饼图**：绩效构成分析
- 📋 **表格**：详细数据展示

### 6.3 科室每月人数

**路径**：`pms/PmsMonthlyDeptStaffNumber`

**功能说明**：查询和分析各科室的月度人员配置情况。

#### 查询功能：
- 按科室查询人员配置
- 按月份查询人员变化
- 按人员类型查询分布
- 支持导出Excel分析

### 6.4 管理岗位统计绩效

**路径**：`pms/pmsCalc/managerPerformance`

**功能说明**：专门针对管理岗位的绩效统计和分析。

#### 统计内容：
- **管理效能指标**：管理目标完成情况
- **团队绩效指标**：所管理团队的整体绩效
- **创新管理指标**：管理创新和改进情况
- **成本控制指标**：管理岗位的成本控制成效

---

## 🤖 七、数据自动化采集模块

### 7.1 绩效数据仓库

**路径**：`pms/pmsCalc/pmsDataETL`

**功能说明**：负责绩效数据的提取、转换和加载（ETL）处理。

#### 核心功能：

1. **数据采集配置**
   - 配置数据源连接
   - 设置采集规则
   - 定义数据映射关系
   - 设置采集调度

2. **ETL任务管理**
   - 创建ETL任务
   - 任务调度设置
   - 任务执行监控
   - 异常处理机制

3. **数据质量监控**
   - 数据完整性检查
   - 数据准确性验证
   - 异常数据报警
   - 数据质量报告

#### 操作界面：

```
┌─────────────────────────────────────────────────────────────┐
│                      控制台看板                              │
├─────────────────────────────────────────────────────────────┤
│  任务统计    │  运行状态    │  数据质量    │  系统资源      │
│  ────────   │  ────────   │  ────────   │  ────────     │
│  总任务:50   │  运行中:5    │  质量分:95   │  CPU:45%      │
│  成功:45     │  等待中:10   │  异常:2      │  内存:60%     │
│  失败:5      │  已完成:35   │  修复:8      │  磁盘:30%     │
└─────────────────────────────────────────────────────────────┘
```

### 7.2 脚本编辑

**路径**：`pms/ms`

**功能说明**：提供数据处理脚本的在线编辑和管理功能。

#### 支持的脚本类型：
- **SQL脚本**：数据查询和处理
- **Python脚本**：复杂数据分析
- **存储过程**：数据库存储过程管理
- **调度脚本**：任务调度配置

#### 编辑器功能：
- 🖥️ **语法高亮**：支持多种语言的语法高亮
- 🔍 **代码补全**：智能代码提示和补全
- 🧪 **脚本测试**：在线测试脚本执行
- 📝 **版本管理**：脚本版本历史记录
- 📊 **执行监控**：脚本执行状态监控

---

## 🎮 八、系统操作指南

### 8.1 新用户快速入门

#### 第一步：系统登录和权限确认
1. 使用分配的账号密码登录系统
2. 检查分配的功能模块权限
3. 熟悉系统主界面布局

#### 第二步：基础配置检查
1. 确认科室数据配置正确
2. 检查用户分组设置
3. 验证相关配置项目

#### 第三步：熟悉常用功能
1. 学习绩效数据查询方法
2. 掌握数据上报流程
3. 了解报表生成操作

### 8.2 日常操作流程

#### 月度绩效管理标准流程：

```mermaid
graph TD
    A[月初：配置检查] --> B[数据采集启动]
    B --> C[各科室数据上报]
    C --> D[数据质量检查]
    D --> E[绩效计算执行]
    E --> F[结果审核确认]
    F --> G[报表生成发布]
    G --> H[月度总结分析]
```

#### 每日运维检查：
- ✅ 检查数据采集任务状态
- ✅ 监控系统运行性能
- ✅ 处理异常数据报警
- ✅ 更新配置参数
- ✅ 备份重要数据

### 8.3 常见问题解决

#### 数据上报问题：
**问题**：Excel导入失败
**解决方案**：
1. 检查Excel模板格式是否正确
2. 验证数据格式和数值范围
3. 确认必填字段是否完整
4. 检查科室权限设置

**问题**：计算结果异常
**解决方案**：
1. 检查基础数据完整性
2. 验证计算模板配置
3. 确认系数配置正确
4. 联系系统管理员核查

#### 权限相关问题：
**问题**：无法访问某个功能模块
**解决方案**：
1. 确认用户角色权限
2. 检查科室分组设置
3. 联系管理员调整权限

**问题**：只能查看本科室数据
**解决方案**：
1. 确认是否需要跨科室权限
2. 向管理员申请权限调整
3. 使用管理员账号进行操作

### 8.4 数据安全注意事项

#### 数据保护措施：
- 🔒 **访问控制**：严格的用户权限管理
- 🛡️ **数据加密**：敏感数据加密存储
- 📱 **操作审计**：完整的操作日志记录
- 💾 **数据备份**：定期数据备份机制
- 🚨 **异常监控**：实时安全监控报警

#### 用户使用规范：
- 不得泄露账号密码
- 不得越权访问数据
- 及时退出系统登录
- 发现异常立即报告
- 定期修改登录密码

---

## 📞 九、技术支持与联系方式

### 9.1 支持服务

#### 技术支持范围：
- 系统功能使用指导
- 数据配置问题解决
- 系统异常故障处理
- 新功能培训服务
- 性能优化建议

#### 支持方式：
- 📱 **热线电话**：400-XXX-XXXX
- 📧 **邮件支持**：<EMAIL>
- 💬 **在线客服**：系统内置客服系统
- 📋 **工单系统**：提交详细问题描述
- 🎓 **培训服务**：定期用户培训

### 9.2 系统更新说明

#### 更新频率：
- **主要版本**：每季度更新
- **功能更新**：每月更新
- **安全补丁**：及时更新
- **紧急修复**：24小时内更新

#### 更新内容通知：
- 系统内消息通知
- 邮件更新说明
- 培训材料更新
- 操作手册修订

---

## 📚 十、附录

### 10.1 术语解释

| 术语 | 英文 | 解释 |
|------|------|------|
| PMS | Performance Management System | 绩效管理系统 |
| ETL | Extract Transform Load | 数据提取转换加载 |
| HIS | Hospital Information System | 医院信息系统 |
| HRP | Hospital Resource Planning | 医院资源计划系统 |
| IAE | Income And Expenditure | 收支平衡 |

### 10.2 系统配置参数

#### 重要配置项：
- **数据保留期限**：默认保留3年历史数据
- **计算精度**：金额计算精确到分
- **超时设置**：数据采集超时时间30分钟
- **并发限制**：同时在线用户数限制500人
- **文件大小**：Excel文件上传限制10MB

### 10.3 系统限制说明

#### 性能限制：
- 单次查询数据量：最大50000条记录
- 并发计算任务：最大10个科室同时计算
- 文件导出大小：最大100MB
- 数据保留时间：系统数据保留36个月

#### 功能限制：
- 历史数据修改需要特殊权限
- 已确认的绩效数据不可随意修改
- 关键配置变更需要审批流程
- 系统维护期间部分功能不可用

---

## 🔄 版本更新记录

| 版本 | 更新日期 | 更新内容 | 负责人 |
|------|----------|----------|--------|
| v1.0 | 2024-12-19 | 初始版本创建，包含所有核心功能说明 | 系统管理员 |

---

*本手册将根据系统更新持续维护和完善，如有疑问请及时联系技术支持团队。*

**🎯 祝您使用愉快！** 