<template>
  <div class="mobile-crud-test">
    <n-card title="移动端CRUD测试页面">
      <n-space vertical>
        <n-alert type="info">
          当前设备类型: {{ isMobileDevice ? '移动端' : '桌面端' }}
        </n-alert>
        
        <n-space>
          <n-button @click="toggleDeviceMode">
            切换到{{ isMobileDevice ? '桌面端' : '移动端' }}模式
          </n-button>
          <n-button @click="showModalMode = 'modal'; testModal = true">
            测试模态框
          </n-button>
          <n-button @click="showModalMode = 'drawer'; testModal = true">
            测试抽屉
          </n-button>
        </n-space>

        <!-- CRUD组件测试 -->
        <j-crud
          :columns="columns"
          :add-method="addMethod"
          :update-method="updateMethod"
          :del-method="delMethod"
          :query-method="queryMethod"
          :add-or-edit-show-mode="showModalMode"
          :force-mobile-view="forceMobile"
          name="测试数据"
          add-button-alias="新增测试"
        />
      </n-space>
    </n-card>

    <!-- 独立测试移动端组件 -->
    <MobileModal
      v-if="testModal && showModalMode === 'modal'"
      v-model:show-modal="testModal"
      title="测试移动端模态框"
      :action-form="testForm"
      :action-form-items="testFormItems"
      :action-form-rules="testFormRules"
      :add-or-edit="true"
      label-width="auto"
      name="测试"
      :edit-flag="false"
      @close="testModal = false"
      @confirmActionFormClick="handleConfirm"
      @resetActionFormClick="handleReset"
    />

    <MobileDrawer
      v-if="testModal && showModalMode === 'drawer'"
      v-model:show-modal="testModal"
      title="测试移动端抽屉"
      :action-form="testForm"
      :action-form-items="testFormItems"
      :action-form-rules="testFormRules"
      :add-or-edit="true"
      label-width="auto"
      name="测试"
      :edit-flag="false"
      placement="bottom"
      @close="testModal = false"
      @confirmActionFormClick="handleConfirm"
      @resetActionFormClick="handleReset"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { isMobile } from '@/utils/device'
import MobileModal from '@/components/common/crud/components/mobileModal.vue'
import MobileDrawer from '@/components/common/crud/components/mobileDrawer.vue'
import { ContainerValueType } from '@/types/common/jtypes'

// 响应式数据
const forceMobile = ref(false)
const testModal = ref(false)
const showModalMode = ref<'modal' | 'drawer'>('modal')

// 设备检测
const isMobileDevice = computed(() => {
  return forceMobile.value || isMobile()
})

// 测试表单数据
const testForm = reactive({
  name: '',
  email: '',
  phone: '',
  address: '',
  remark: ''
})

// 测试表单项
const testFormItems = ref([
  {
    title: '姓名',
    key: 'name',
    type: ContainerValueType.INPUT,
    required: true,
    placeholder: '请输入姓名'
  },
  {
    title: '邮箱',
    key: 'email',
    type: ContainerValueType.INPUT,
    required: true,
    placeholder: '请输入邮箱'
  },
  {
    title: '电话',
    key: 'phone',
    type: ContainerValueType.INPUT,
    placeholder: '请输入电话'
  },
  {
    title: '地址',
    key: 'address',
    type: ContainerValueType.INPUT,
    placeholder: '请输入地址'
  },
  {
    title: '备注',
    key: 'remark',
    type: ContainerValueType.TEXTAREA,
    placeholder: '请输入备注'
  }
])

// 测试表单规则
const testFormRules = ref({
  name: {
    required: true,
    message: '请输入姓名',
    trigger: ['blur', 'input']
  },
  email: {
    required: true,
    message: '请输入邮箱',
    trigger: ['blur', 'input']
  }
})

// CRUD列定义
const columns = ref([
  {
    title: '姓名',
    key: 'name',
    type: ContainerValueType.INPUT,
    required: true
  },
  {
    title: '邮箱',
    key: 'email',
    type: ContainerValueType.INPUT,
    required: true
  },
  {
    title: '电话',
    key: 'phone',
    type: ContainerValueType.INPUT
  },
  {
    title: '状态',
    key: 'status',
    type: ContainerValueType.SELECT,
    selection: [
      { label: '启用', value: '1' },
      { label: '禁用', value: '0' }
    ]
  }
])

// 模拟数据
const mockData = ref([
  { id: 1, name: '张三', email: '<EMAIL>', phone: '13800138001', status: '1' },
  { id: 2, name: '李四', email: '<EMAIL>', phone: '13800138002', status: '0' },
  { id: 3, name: '王五', email: '<EMAIL>', phone: '13800138003', status: '1' },
  { id: 4, name: '赵六', email: '<EMAIL>', phone: '13800138004', status: '1' }
])

// 方法
const toggleDeviceMode = () => {
  forceMobile.value = !forceMobile.value
}

const handleConfirm = () => {
  console.log('确认提交:', testForm)
  window.$message.success('提交成功')
  testModal.value = false
}

const handleReset = () => {
  Object.keys(testForm).forEach(key => {
    testForm[key] = ''
  })
  window.$message.info('表单已重置')
}

// CRUD方法
const queryMethod = (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: {
          records: mockData.value,
          total: mockData.value.length,
          pages: 1
        }
      })
    }, 500)
  })
}

const addMethod = (data: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = Math.max(...mockData.value.map(item => item.id)) + 1
      mockData.value.push({ ...data, id: newId })
      resolve({ code: 200, message: '新增成功' })
    }, 500)
  })
}

const updateMethod = (data: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockData.value.findIndex(item => item.id === data.id)
      if (index !== -1) {
        mockData.value[index] = { ...data }
      }
      resolve({ code: 200, message: '修改成功' })
    }, 500)
  })
}

const delMethod = (data: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockData.value.findIndex(item => item.id === data.id)
      if (index !== -1) {
        mockData.value.splice(index, 1)
        window.$message.success(`已删除用户: ${data.name}`)
      }
      resolve({ code: 200, message: '删除成功' })
    }, 500)
  })
}
</script>

<style lang="less" scoped>
.mobile-crud-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}
</style>
