# 绩效系统架构与计算流程图说明 📊

## 系统概述 🎯

med-pms（Medical Performance Management System）是一套专为医疗机构设计的综合性绩效管理平台，采用微服务架构，提供科学、公正、透明的绩效评价和奖金分配机制。

## 系统架构图 🏗️

### 1. 前端层 (Vue3 + Naive UI) 🎨
- **用户界面**：提供直观友好的操作界面
- **数据可视化**：丰富的图表和报表展示
- **交互操作**：响应式设计，支持多种设备

**技术栈：**
- Vue 3 + TypeScript
- Naive UI 组件库
- Vite 构建工具
- Pinia 状态管理

### 2. 网关层 (Spring Cloud Gateway) 🚪
- **路由转发**：统一入口，智能路由
- **负载均衡**：分布式请求处理
- **安全认证**：统一身份验证和授权

### 3. 注册中心 (Nacos) 🏢
- **服务发现**：自动服务注册与发现
- **配置管理**：集中化配置管理
- **健康检查**：实时监控服务状态

## 核心业务模块 🧩

### 1. 配置管理模块 ⚙️
**功能特性：**
- 科室数据配置：科室与HIS系统的数据映射
- 收费项目配置：收费项目与绩效计算关联
- 用户分组管理：灵活的用户权限分组
- 权限配置：多级权限控制体系

### 2. 绩效计算引擎 🧮
**核心能力：**
- 绩效计算模板：支持复杂计算公式
- 公式配置：灵活的公式定义和管理
- 跨模板引用：支持模板间数据引用
- 计算规则：多种计算方法和规则

**特色功能：**
- 支持4种人员计算方法
- 跨模板变量引用
- 批次管理和版本控制
- 实时计算和缓存优化

### 3. 数据ETL模块 📊
**处理流程：**
- **数据采集调度**：定时自动采集外部数据
- **批量处理**：高效的批量数据处理
- **Magic API集成**：动态API配置和调用
- **状态监控**：实时任务状态跟踪

### 4. 报表分析模块 📈
**分析能力：**
- 月度绩效汇总：全面的绩效数据统计
- 成本管控报表：详细的成本分析
- 设备效益分析：医疗设备投资回报分析
- 数据可视化：多维度图表展示

## 绩效计算流程详解 🔄

### 步骤1: 初始化计算上下文 🚀
- **获取模板配置**：从数据库加载绩效计算模板
- **构建变量上下文**：初始化计算所需的变量环境
- **加载公式映射**：建立公式与变量的映射关系

### 步骤2: 构建依赖图 🕸️
- **分析项目依赖关系**：识别绩效项目间的依赖关系
- **拓扑排序**：确定无循环依赖的计算顺序
- **确定计算顺序**：按依赖关系排序计算项目

### 步骤3: 处理常量和固定值 📋
- **加载常量配置**：从配置表加载系统常量
- **处理目标值**：设置各项目的目标值
- **设置奖励系数**：配置绩效奖励系数

### 步骤4: 执行公式计算 🧠
- **JavaScript引擎**：使用JavaScript引擎执行复杂公式
- **公式解析**：解析公式中的变量和表达式
- **变量替换**：将变量替换为实际数值进行计算

### 步骤5: 缓存计算结果 💾
- **更新变量上下文**：将计算结果更新到变量上下文
- **写入全局缓存**：将结果写入Redis缓存
- **支持跨模板引用**：为其他模板提供数据引用

## 核心组件详解 🔧

### PmsAwardCalcEngineService - 绩效计算引擎服务
- `initContext()` - 初始化计算上下文
- `calculatePerformanceScores()` - 执行绩效得分计算
- 支持跨模板变量引用
- 处理复杂公式计算

### PmsCalculationDependencyService - 依赖关系服务
- `buildDependencyGraph()` - 构建项目依赖图
- `topologicalSort()` - 拓扑排序确定计算顺序
- 处理循环依赖检测

### PmsVariableService - 变量处理服务
- `buildFormulaVariablesMap()` - 构建公式变量映射
- `processConstantsAndFixedValues()` - 处理常量和固定值
- `buildAllowedVariables()` - 构建允许变量列表

### PmsFormulaEvaluationService - 公式计算服务
- JavaScript引擎执行公式
- 变量上下文注入
- 公式解析和计算

### GlobalCalcCacheService - 全局缓存服务
- 跨模板计算结果缓存
- 支持模板间数据引用
- 提高计算性能

## 支持的公式变量类型 📝

| 变量类型 | 格式 | 说明 |
|---------|------|------|
| 奖励系数 | `${itemName.xxx}` | 绩效项目的奖励系数 |
| 目标值 | `${targetValue.xxx}` | 绩效项目的目标值 |
| 常量 | `${const.xxx}` | 系统配置的常量值 |
| 项目分数 | `${itemName.default}` | 绩效项目数量分数 |
| 引用项目 | `${calculated.xxx}` | 引用其他绩效项目的计算结果 |
| 指标合计 | `${performanceSummary.xxx}` | 一级指标应发金额合计 |
| 跨模板引用 | `${template[模板ID].<变量标识>.<变量类型>}` | 引用其他模板的计算结果 |

## 公式示例 💡

### 1. 简单计算
```javascript
${cybls.default}*${cybls.coefficient}
```

### 2. 复杂计算
```javascript
8*${cybls.default}+(${pjzyr.targetValue}-${pjzyr.default})*4*${cybls.default}
```

### 3. 依赖汇总的计算
```javascript
(${performanceSummary.按量计算绩效合计}*0.4)/230*${yljxzlkh40.default}
```

### 4. 使用常量的计算
```javascript
(${const.doctor_number}*100)+(${hckzzb.targetValue}-${hckzzb.default})*10*(${const.doctor_number}*100)
```

### 5. 跨模板引用计算
```javascript
${cybls.default}*${template[123].itemX.calculated}
```

## 技术栈总览 🛠️

### 后端技术栈
- Spring Boot 2.3.12.RELEASE
- Spring Cloud Hoxton.SR12
- MyBatis-Plus + Druid
- JDK 11
- PostgreSQL + Redis

### 前端技术栈
- Vue 3 + TypeScript
- Naive UI 组件库
- Vite 构建工具
- Pinia 状态管理

## 系统特性 ✨

### 核心特性
- **微服务架构**：高可用、可扩展
- **绩效计算引擎**：强大的计算能力
- **数据ETL处理**：自动化数据处理
- **动态API配置**：灵活的接口管理
- **多级权限控制**：精细化权限管理
- **实时数据监控**：全面的系统监控
- **可视化报表**：丰富的数据展示

### 业务功能
- **配置管理**：系统配置和参数管理
- **绩效计算**：智能绩效计算和分析
- **成本管控**：全面的成本控制
- **效益分析**：投资回报分析
- **数据采集**：自动化数据采集
- **报表分析**：多维度数据分析

---

*该架构图和流程说明展示了med-pms系统的完整技术架构和绩效计算业务流程，为系统的开发、部署和维护提供了清晰的指导。*
