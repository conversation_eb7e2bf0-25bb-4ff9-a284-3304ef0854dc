---
description:
globs:
alwaysApply: false
---
# Vue Hooks 模式最佳实践

## 📝 Hooks 介绍
Hooks 是 Vue 3 Composition API 的一种应用模式，用于提取和复用有状态的组件逻辑。在本项目中，Hooks 被广泛用于分离复杂组件的业务逻辑，使组件保持清晰和易于维护。

## 🧩 项目中 Hooks 的组织方式

### 目录结构
```
src/
  views/
    modules/
      ams/
        amsAssetAdjustment/
          outBorrowing/
            hooks/             # 模块特定的 hooks 目录
              useBorrow.js     # 借用相关逻辑
              useReturn.js     # 归还相关逻辑
  hooks/                       # 全局通用 hooks 目录
    useTable.js
    useForm.js
    useAuth.js
```

### 命名规范
- 所有 Hooks 文件名均使用 `useXxx` 格式命名
- Hooks 名称应当清晰表达其功能，如 `useBorrowHooks`, `useReturnHooks`
- 多单词 Hooks 使用驼峰命名法

## 🔄 标准 Hooks 模式

### 基本结构
```javascript
import { ref, reactive, computed, onMounted } from 'vue'
import { useUserStore, useSysStore } from '@/store'
import { apiFunction1, apiFunction2 } from './api'

export default function useFeatureName(props = {}) {
  // 1. 引入全局状态
  const userStore = useUserStore()
  const sysStore = useSysStore()
  
  // 2. 定义响应式状态
  const dataState = ref([])
  const queryForm = reactive({
    field1: '',
    field2: ''
  })
  
  // 3. 定义计算属性
  const computedValue = computed(() => {
    return dataState.value.filter(item => item.active)
  })
  
  // 4. 方法定义
  const fetchData = async () => {
    // 数据获取逻辑
  }
  
  const handleEvent = () => {
    // 事件处理逻辑
  }
  
  // 5. 生命周期钩子
  onMounted(() => {
    fetchData()
  })
  
  // 6. 返回暴露的状态和方法
  return {
    queryForm,
    dataState,
    computedValue,
    fetchData,
    handleEvent
  }
}
```

### Hook 间通信
```javascript
// useFeatureA.js
export default function useFeatureA() {
  const stateA = ref('value')
  const changeState = (newValue) => {
    stateA.value = newValue
  }
  return { stateA, changeState }
}

// useFeatureB.js
import useFeatureA from './useFeatureA'

export default function useFeatureB() {
  const { stateA, changeState } = useFeatureA()
  
  const doSomething = () => {
    // 使用 featureA 的状态和方法
    changeState('new value')
  }
  
  return { doSomething }
}

// 在组件中使用
const { stateA } = useFeatureA()
const { doSomething } = useFeatureB()
```

## 🌟 常见 Hooks 类型

### 状态管理型 Hooks
封装特定功能模块的状态和操作：

```javascript
export default function useBorrowHooks() {
  const queryForm = reactive({
    borrowHospital: '',
    applicantOrgId: null,
    borrowTimeSearch: null,
    returnTimeSearch: null,
    // 其他查询参数
  })
  
  const crudRef = ref(null)
  const tabs = ref([
    { name: '申请中', value: 'applying' },
    { name: '借用中', value: 'borrowing' },
    { name: '已归还', value: 'returned' }
  ])
  
  // 其他状态和方法
  
  return {
    queryForm,
    crudRef,
    tabs,
    // 其他暴露的内容
  }
}
```

### 生命周期型 Hooks
处理组件生命周期相关的逻辑：

```javascript
export default function useInitialization() {
  onMounted(() => {
    // 初始化逻辑
  })
  
  onBeforeUnmount(() => {
    // 清理逻辑
  })
  
  return {
    // 可能需要暴露的内容
  }
}
```

### API 调用型 Hooks
封装 API 调用和数据处理逻辑：

```javascript
export default function useDataFetching() {
  const loading = ref(false)
  const data = ref([])
  const error = ref(null)
  
  const fetchData = async (params) => {
    loading.value = true
    error.value = null
    try {
      const result = await apiFunction(params)
      data.value = result.data
    } catch (err) {
      error.value = err
    } finally {
      loading.value = false
    }
  }
  
  return {
    loading,
    data,
    error,
    fetchData
  }
}
```

### 事件处理型 Hooks
封装事件处理逻辑：

```javascript
export default function useFormEvents() {
  const formRef = ref(null)
  
  const submitForm = () => {
    formRef.value?.validate((errors) => {
      if (!errors) {
        // 提交表单逻辑
      }
    })
  }
  
  const resetForm = () => {
    formRef.value?.restoreValidation()
  }
  
  return {
    formRef,
    submitForm,
    resetForm
  }
}
```

## ⚠️ 注意事项

1. **避免循环依赖**：互相依赖的 Hooks 可能导致循环引用问题，设计时需仔细规划依赖关系

2. **参数传递**：Hook 可以接收参数，但应避免过多参数导致不清晰，可考虑使用对象参数

3. **合理拆分**：功能过于复杂的 Hook 应考虑拆分为多个小的 Hook

4. **状态共享**：多个组件间需要共享状态时，考虑使用 Pinia store 而非 Hook

5. **副作用清理**：在 Hook 中注册的事件监听器、定时器等应在组件卸载时清理

6. **TypeScript 支持**：尽量为 Hook 提供类型定义，提高代码健壮性
