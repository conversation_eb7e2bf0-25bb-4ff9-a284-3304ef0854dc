<template>
  <div class="test-page">
    <n-space vertical :size="24">
      <!-- 页面标题 -->
      <n-card title="自适应数据表格测试页面" size="small">
        <n-text depth="3">
          这是一个测试页面，用于验证自适应数据表格组件的功能。
          请在不同设备上测试以查看PC端和移动端的不同表现。
        </n-text>
      </n-card>

      <!-- 设备信息 -->
      <n-card title="当前设备信息" size="small">
        <n-descriptions :column="2" size="small">
          <n-descriptions-item label="是否移动设备">
            <n-tag :type="isMobileDevice ? 'success' : 'info'">
              {{ isMobileDevice ? '是' : '否' }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="屏幕宽度">
            {{ screenWidth }}px
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 测试数据表格 -->
      <n-card title="测试数据表格" size="small">
        <template #header-extra>
          <n-space :size="8">
            <n-button size="small" @click="addTestData">
              添加数据
            </n-button>
            <n-button size="small" @click="clearData">
              清空数据
            </n-button>
          </n-space>
        </template>

        <adaptive-data-table
          :data="testData"
          :columns="testColumns"
          :loading="loading"
          row-key="id"
          mobile-title="测试数据"
          :show-view-toggle="true"
          :show-mobile-config="true"
          :card-columns="2"
          :show-actions="true"
          @row-click="handleRowClick"
        >
          <template #actions="{ row }">
            <n-button size="tiny" type="primary" @click.stop="handleEdit(row)">
              编辑
            </n-button>
            <n-button size="tiny" type="error" @click.stop="handleDelete(row)">
              删除
            </n-button>
          </template>
        </adaptive-data-table>
      </n-card>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, onMounted, onUnmounted } from 'vue'
import { h } from 'vue'
import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'

// 移动端检测
const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')

// 响应式数据
const loading = ref(false)
const screenWidth = ref(window.innerWidth)

// 测试数据
const testData = ref([
  {
    id: 1,
    name: '张三',
    age: 25,
    email: '<EMAIL>',
    department: '技术部',
    position: '前端工程师',
    status: '在职',
    joinDate: '2023-01-15',
    score: 95
  },
  {
    id: 2,
    name: '李四',
    age: 30,
    email: '<EMAIL>',
    department: '产品部',
    position: '产品经理',
    status: '在职',
    joinDate: '2022-08-20',
    score: 88
  },
  {
    id: 3,
    name: '王五',
    age: 28,
    email: '<EMAIL>',
    department: '设计部',
    position: 'UI设计师',
    status: '离职',
    joinDate: '2023-03-10',
    score: 92
  }
])

// 测试列配置
const testColumns = ref([
  {
    key: 'name',
    title: '姓名',
    width: 100,
    mobileTitle: true,
    mobileOrder: 1
  },
  {
    key: 'position',
    title: '职位',
    width: 120,
    mobileSubtitle: true,
    mobileOrder: 2
  },
  {
    key: 'department',
    title: '部门',
    width: 100,
    mobilePosition: 'header',
    mobileOrder: 3,
    render: (row: any) => {
      const colors: any = {
        '技术部': 'success',
        '产品部': 'info',
        '设计部': 'warning'
      }
      return h('n-tag', { 
        type: colors[row.department] || 'default', 
        size: 'small' 
      }, row.department)
    }
  },
  {
    key: 'status',
    title: '状态',
    width: 80,
    mobilePosition: 'header',
    mobileOrder: 4,
    render: (row: any) => {
      return h('n-tag', {
        type: row.status === '在职' ? 'success' : 'error',
        size: 'small'
      }, row.status)
    }
  },
  {
    key: 'age',
    title: '年龄',
    width: 80,
    mobileOrder: 5
  },
  {
    key: 'email',
    title: '邮箱',
    width: 180,
    mobileOrder: 6
  },
  {
    key: 'score',
    title: '评分',
    width: 80,
    mobileOrder: 7,
    render: (row: any) => {
      const color = row.score >= 90 ? 'success' : row.score >= 80 ? 'warning' : 'error'
      return h('n-tag', { type: color, size: 'small' }, `${row.score}分`)
    }
  },
  {
    key: 'joinDate',
    title: '入职日期',
    width: 120,
    mobilePosition: 'footer',
    mobileOrder: 8
  }
])

// 方法：添加测试数据
const addTestData = () => {
  const names = ['赵六', '钱七', '孙八', '周九']
  const departments = ['技术部', '产品部', '设计部', '运营部']
  const positions = ['工程师', '经理', '设计师', '专员']
  const statuses = ['在职', '离职']
  
  const newId = Math.max(...testData.value.map(item => item.id)) + 1
  const randomName = names[Math.floor(Math.random() * names.length)]
  const randomDept = departments[Math.floor(Math.random() * departments.length)]
  const randomPos = positions[Math.floor(Math.random() * positions.length)]
  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
  
  testData.value.push({
    id: newId,
    name: randomName,
    age: Math.floor(Math.random() * 20) + 25,
    email: `${randomName.toLowerCase()}@test.com`,
    department: randomDept,
    position: randomPos,
    status: randomStatus,
    joinDate: '2024-01-01',
    score: Math.floor(Math.random() * 40) + 60
  })
  
  window.$message?.success('添加测试数据成功')
}

// 方法：清空数据
const clearData = () => {
  testData.value = []
  window.$message?.info('数据已清空')
}

// 方法：处理行点击
const handleRowClick = (row: any) => {
  console.log('行点击:', row)
  window.$message?.info(`点击了 ${row.name} 的数据`)
}

// 方法：处理编辑
const handleEdit = (row: any) => {
  console.log('编辑:', row)
  window.$message?.info(`编辑 ${row.name}`)
}

// 方法：处理删除
const handleDelete = (row: any) => {
  const index = testData.value.findIndex(item => item.id === row.id)
  if (index > -1) {
    testData.value.splice(index, 1)
    window.$message?.success(`删除 ${row.name} 成功`)
  }
}

// 更新屏幕宽度
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', updateScreenWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenWidth)
})
</script>

<style scoped>
@reference "tailwindcss";

.test-page {
  @apply p-6 max-w-6xl mx-auto;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .test-page {
    @apply p-4;
  }
}
</style>
