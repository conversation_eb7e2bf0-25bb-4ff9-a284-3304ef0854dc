# 移动端视口管理解决方案 📱

## 🎯 问题背景

移动端浏览器的底部导航栏（地址栏、工具栏等）会动态显示和隐藏，导致页面内容被遮挡或布局错乱。传统的 `100vh` 在移动端存在以下问题：

1. **iOS Safari**: 地址栏和底部工具栏会影响视口高度
2. **Android Chrome**: 底部导航栏在滚动时会隐藏/显示
3. **安全区域**: iPhone X 等设备的刘海屏和 Home 指示器
4. **动态变化**: 浏览器 UI 的显示/隐藏会改变可视区域

## 🔧 解决方案

### 1. 视口管理工具 (`src/utils/viewport.ts`)

核心功能：
- 🔍 **实时监测**：监听视口大小变化和设备方向变化
- 📏 **动态计算**：获取真实可视高度，而非固定的 100vh
- 🛡️ **安全区域**：自动处理 iOS 设备的安全区域
- 🎨 **CSS 变量**：动态更新 CSS 自定义属性

```typescript
// 核心API
import { viewportManager, getViewportInfo, getDynamicVH } from '@/utils/viewport'

// 获取当前视口信息
const info = getViewportInfo()
console.log(info.visualHeight) // 真实可视高度

// 获取动态视口高度（替代100vh）
const dynamicHeight = getDynamicVH() // "812px"
```

### 2. Vue 组合式函数 (`src/composables/useViewport.ts`)

提供响应式的视口管理：

```vue
<script setup>
import { useMobileLayout } from '@/composables/useViewport'

const { 
  viewportInfo,
  isMobile,
  dynamicVH,
  safeContentHeight,
  layoutStyle,
  mainStyle 
} = useMobileLayout()
</script>

<template>
  <div class="mobile-layout" :style="layoutStyle">
    <div class="main-content" :style="mainStyle">
      <!-- 内容区域 -->
    </div>
  </div>
</template>
```

### 3. HTML 视口配置

更新 `index.html` 的 viewport meta 标签：

```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover"/>
```

关键点：
- `viewport-fit=cover`：支持 iOS 设备的安全区域

## 📐 CSS 变量系统

系统自动注入以下 CSS 自定义属性：

```css
:root {
  /* 动态视口高度 */
  --dynamic-vh: 812px;          /* 替代 100vh */
  --window-height: 896px;       /* 窗口高度 */
  --visual-height: 812px;       /* 可视高度 */
  
  /* 安全区域 */
  --safe-area-top: 44px;        /* 顶部安全区域 */
  --safe-area-bottom: 34px;     /* 底部安全区域 */
  --safe-area-left: 0px;        /* 左侧安全区域 */
  --safe-area-right: 0px;       /* 右侧安全区域 */
  
  /* 布局高度 */
  --header-height: 60px;        /* 顶部导航栏高度 */
  --bottom-nav-height: 70px;    /* 底部导航栏高度 */
  --safe-content-height: 648px; /* 安全内容区域高度 */
  --available-height: 718px;    /* 可用内容高度 */
}
```

## 🎨 使用示例

### 1. 基础布局

```vue
<template>
  <div class="mobile-layout">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
      <h1>页面标题</h1>
    </header>
    
    <!-- 主内容区域 -->
    <main class="mobile-main">
      <router-view />
    </main>
    
    <!-- 底部导航栏 -->
    <nav class="mobile-bottom-nav">
      <button>首页</button>
      <button>我的</button>
    </nav>
  </div>
</template>

<style>
.mobile-layout {
  height: var(--dynamic-vh, 100vh); /* 使用动态高度 */
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  padding-top: var(--safe-area-top);
  z-index: 1000;
}

.mobile-main {
  flex: 1;
  overflow-y: auto;
  margin-top: calc(var(--header-height) + var(--safe-area-top));
  margin-bottom: calc(var(--bottom-nav-height) + var(--safe-area-bottom));
  min-height: var(--safe-content-height);
}

.mobile-bottom-nav {
  position: fixed;
  bottom: var(--safe-area-bottom);
  left: 0;
  right: 0;
  height: var(--bottom-nav-height);
  padding-bottom: var(--safe-area-bottom);
  z-index: 1000;
}
</style>
```

### 2. 响应式组件

```vue
<script setup>
import { useViewport } from '@/composables/useViewport'

const { 
  isMobile, 
  safeContentHeight, 
  getHeightCSS 
} = useViewport({
  headerHeight: 80,
  bottomNavHeight: 60
})

// 获取不同配置的高度
const fullHeight = getHeightCSS(false, false)  // 完整高度
const mainHeight = getHeightCSS(true, false)   // 排除顶部
const contentHeight = getHeightCSS(true, true) // 排除顶部和底部
</script>

<template>
  <div v-if="isMobile" class="mobile-container">
    <div 
      class="content-area"
      :style="{ height: contentHeight }"
    >
      <!-- 内容 -->
    </div>
  </div>
</template>
```

### 3. 动态适配

```vue
<script setup>
import { useMobileLayout } from '@/composables/useViewport'

const { 
  viewportInfo,
  cssVars,
  layoutStyle,
  headerStyle,
  mainStyle,
  bottomNavStyle 
} = useMobileLayout()

// 监听视口变化
watch(viewportInfo, (info) => {
  if (info) {
    console.log('视口更新:', {
      可视高度: info.visualHeight,
      是否移动端: info.isMobile,
      安全区域: info.safeArea
    })
  }
})
</script>

<template>
  <div class="mobile-layout" :style="layoutStyle">
    <header class="mobile-header" :style="headerStyle">
      <!-- 顶部内容 -->
    </header>
    
    <main class="mobile-main" :style="mainStyle">
      <!-- 主要内容 -->
    </main>
    
    <nav class="mobile-bottom-nav" :style="bottomNavStyle">
      <!-- 底部导航 -->
    </nav>
  </div>
</template>
```

## 🔍 调试工具

开启调试模式查看视口信息：

```typescript
const { viewportInfo } = useViewport({ debug: true })

// 控制台输出：
// 🔍 视口信息更新: { visualHeight: 812, windowHeight: 896, isMobile: true, ... }
// 🎨 CSS变量已更新: { --dynamic-vh: "812px", --safe-area-top: "44px", ... }
```

## 📱 设备兼容性

| 设备类型 | 支持情况 | 特殊处理 |
|---------|---------|---------|
| iPhone (iOS 11+) | ✅ 完全支持 | 安全区域自动适配 |
| Android Chrome | ✅ 完全支持 | 动态导航栏处理 |
| Android 原生浏览器 | ✅ 基础支持 | 降级到估算高度 |
| iPad | ✅ 完全支持 | 平板模式适配 |
| 其他移动浏览器 | ⚠️ 部分支持 | 基础功能可用 |

## 🚀 性能优化

1. **防抖处理**：视口变化事件使用防抖，避免频繁更新
2. **缓存机制**：CSS 变量更新前检查是否有变化
3. **懒加载**：只在移动端设备上启用视口管理
4. **内存管理**：组件卸载时自动清理监听器

## 📋 最佳实践

1. **优先使用 CSS 变量**：`var(--dynamic-vh, 100vh)` 作为降级方案
2. **避免硬编码高度**：使用动态计算的高度值
3. **测试多种设备**：在不同设备和浏览器上测试效果
4. **渐进增强**：确保在不支持的浏览器上有基础功能
5. **性能监控**：监控视口变化频率，避免性能问题

## 🔧 故障排除

### 问题1：底部内容仍被遮挡
```css
/* 确保使用了正确的安全区域 */
.content {
  padding-bottom: calc(var(--bottom-nav-height) + var(--safe-area-bottom));
}
```

### 问题2：iOS 设备显示异常
```html
<!-- 确保 viewport meta 标签正确 -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover"/>
```

### 问题3：Android 设备高度计算错误
```typescript
// 检查是否正确监听了 visualViewport
if (window.visualViewport) {
  window.visualViewport.addEventListener('resize', handleResize)
}
```

这套解决方案能够有效解决移动端浏览器底部导航栏遮挡问题，提供流畅的用户体验。🎉
