# 指标详情数据生成工具使用指南

## 概述

本工具基于建造者模式设计，并结合Lombok简化代码，用于在Java后端生成符合前端`IndicatorDetailModal`组件要求的指标详情数据。工具提供了灵活的API和自动生成功能来构建单表格和多标签页的数据展示。

## ✨ 新增功能

### 🚀 自动生成工具
- **智能列推断**: 根据数据内容自动生成列配置
- **多种渲染类型**: 自动识别状态、金额、日期等类型  
- **中文标题映射**: 自动将英文字段名转换为中文标题
- **一键生成**: 直接传入Map数据即可生成完整的指标详情

## 核心组件

### 1. 数据实体类

- **`IndicatorDetail`**: 主要数据实体，包含数据源、标签页、列配置等信息
- **`TabData`**: 标签页数据实体，用于多标签页展示
- **`ColumnConfig`**: 列配置实体，定义表格列的属性
- **`ColumnRenderType`**: 列渲染类型枚举，支持多种显示样式

### 2. 建造者类

- **`IndicatorDetailBuilder`**: 主要建造者，支持链式调用构建指标详情
- **`TabBuilder`**: 标签页建造者，构建单个标签页
- **`ColumnBuilder`**: 列配置建造者，构建表格列

### 3. 工厂类

- **`IndicatorDetailFactory`**: 提供常用指标类型的预制模板
- **`ColumnFactory`**: 提供常用列类型的快速构建方法

### 4. 自动生成工具

- **`IndicatorDetailAutoBuilder`**: 自动生成工具，根据Map数据智能生成列配置和指标详情

## 基本使用方法

### 🌟 自动生成模式（推荐）

```java
// 📊 自动生成单表格 - 只需传入数据，自动生成列配置
List<Map<String, Object>> data = Arrays.asList(
    Map.of("name", "张三", "empCode", "E001", "amount", "1000.50", "status", "在岗"),
    Map.of("name", "李四", "empCode", "E002", "amount", "1200.00", "status", "请假")
);

IndicatorDetail detail = IndicatorDetailAutoBuilder.createFromSingleData("HRM系统", data);
```

```java
// 📊 自动生成多标签页 - 传入Map<科室名称, 数据列表>
Map<String, List<Map<String, Object>>> deptData = new HashMap<>();
deptData.put("心内科", cardiacNurses);
deptData.put("呼吸科", respiratoryNurses);

IndicatorDetail detail = IndicatorDetailAutoBuilder.createFromMultiTabData("HRM系统", deptData);
```

### 传统建造者模式

```java
// 基本单表格
IndicatorDetail detail = IndicatorDetailBuilder.create()
    .dataSource("HIS系统")
    .addColumn("日期", "date", 120)
    .addColumn("数量", "count", 100)
    .addRow(IndicatorDetailFactory.createRow(
        "date", "2024-01-01",
        "count", "25"
    ))
    .build();
```

### 多标签页模式

```java
// 多标签页展示
IndicatorDetail detail = IndicatorDetailBuilder.create()
    .dataSource("多系统数据")
    .addTab(
        IndicatorDetailBuilder.TabBuilder.create("summary", "汇总")
            .addColumn("项目", "item", 150)
            .addColumn("数值", "value", 100)
            .addRow(createRow("item", "总数", "value", "100"))
    )
    .addTab(
        IndicatorDetailBuilder.TabBuilder.create("detail", "明细")
            .addColumn("姓名", "name", 100)
            .addColumn("科室", "dept", 120)
            .addRow(createRow("name", "张三", "dept", "内科"))
    )
    .build();
```

## 高级功能

### 列渲染类型

支持多种列渲染类型，对应前端组件的特殊显示：

```java
// 状态标签列
IndicatorDetailBuilder.ColumnBuilder.create("状态", "status")
    .renderType(ColumnRenderType.TAG)
    .renderConfig("colorMap", Map.of(
        "正常", "success",
        "异常", "error",
        "警告", "warning"
    ))

// 金额列（右对齐，带格式化）
IndicatorDetailBuilder.ColumnBuilder.create("金额", "amount")
    .align("right")
    .renderType(ColumnRenderType.NUMBER)
    .renderConfig("precision", 2)
    .renderConfig("format", "currency")

// 日期列
IndicatorDetailBuilder.ColumnBuilder.create("日期", "date")
    .renderType(ColumnRenderType.DATE)
    .renderConfig("format", "YYYY-MM-DD")
```

### 快速列配置

使用`ColumnFactory`快速创建常用列类型：

```java
// 状态列
ColumnFactory.statusColumn("审核状态", "auditStatus", 
    Map.of("通过", "success", "拒绝", "error"))

// 金额列
ColumnFactory.amountColumn("奖励金额", "rewardAmount")

// 百分比列
ColumnFactory.percentageColumn("完成率", "completionRate")

// 日期列
ColumnFactory.dateColumn("创建时间", "createTime")
```

### 预制模板使用

工厂类提供了常用指标的预制模板：

```java
// CMI值奖励指标详情
IndicatorDetail cmiDetail = IndicatorDetailFactory.createCMIRewardDetail(
    deptCMI, hospitalAvgCMI, rewardCoefficient, caseDetails
);

// 人员统计详情
IndicatorDetail staffDetail = IndicatorDetailFactory.createStaffCountDetail(
    "护士", summaryData, detailData
);

// 出院病例统计
IndicatorDetail dischargeDetail = IndicatorDetailFactory.createDischargeCountDetail(
    dailyData
);
```

## 实际应用示例

### 示例1: CMI值奖励指标

```java
public IndicatorDetail getCMIRewardIndicatorDetail() {
    BigDecimal deptCMI = new BigDecimal("1.25");
    BigDecimal hospitalAvgCMI = new BigDecimal("1.10");
    BigDecimal rewardCoefficient = new BigDecimal("50");
    
    // 病例明细数据
    List<Map<String, Object>> caseDetails = Arrays.asList(
        IndicatorDetailFactory.createRow(
            "inpatientNo", "H2024001",
            "patientName", "张**",
            "mainDiagnosis", "急性心肌梗死",
            "drgGroup", "DRG_CV01",
            "cmiValue", "2.15",
            "complexityLevel", "高"
        )
    );
    
    return IndicatorDetailFactory.createCMIRewardDetail(
        deptCMI, hospitalAvgCMI, rewardCoefficient, caseDetails
    );
}
```

### 示例2: 自定义复杂指标

```java
public IndicatorDetail getCustomIndicatorDetail() {
    return IndicatorDetailBuilder.create()
        .dataSource("多系统整合数据")
        .metadata("reportDate", "2024-01")
        .metadata("department", "心血管内科")
        
        // 质量指标标签页
        .addTab(
            IndicatorDetailBuilder.TabBuilder.create("quality", "质量指标")
                .addColumn("指标名称", "indicatorName", 150)
                .addColumn(
                    IndicatorDetailBuilder.ColumnBuilder.create("目标值", "targetValue")
                        .width(100)
                        .align("right")
                        .renderType(ColumnRenderType.NUMBER)
                        .renderConfig("precision", 2)
                )
                .addColumn(
                    ColumnFactory.statusColumn("达标状态", "status", 
                        Map.of("达标", "success", "未达标", "error"))
                )
                .addRow(createRow(
                    "indicatorName", "平均住院日",
                    "targetValue", "8.5",
                    "actualValue", "7.8",
                    "status", "达标"
                ))
        )
        
        // 效率指标标签页
        .addTab(
            IndicatorDetailBuilder.TabBuilder.create("efficiency", "效率指标")
                .addColumn("指标名称", "indicatorName", 150)
                .addColumn(ColumnFactory.percentageColumn("完成率", "completionRate"))
                .addColumn(ColumnFactory.dateColumn("更新时间", "updateTime"))
                .addRow(createRow(
                    "indicatorName", "手术及时率",
                    "completionRate", "95.5",
                    "updateTime", "2024-01-15"
                ))
        )
        .build();
}
```

## 最佳实践

### 1. 数据脱敏

对于敏感信息，使用工厂提供的脱敏方法：

```java
// 姓名脱敏
String maskedName = IndicatorDetailFactory.maskName("张三丰");  // 张**丰

// 电话脱敏  
String maskedPhone = IndicatorDetailFactory.maskPhone("13812345678");  // 138****5678
```

### 2. 性能优化

- 对于大量数据，考虑分页处理
- 使用`dataTransformer`进行服务端数据预处理
- 合理设置列宽度避免前端渲染问题

### 3. 错误处理

```java
public IndicatorDetail getIndicatorDetailSafely(String indicatorName) {
    try {
        return getIndicatorDetailByName(indicatorName);
    } catch (Exception e) {
        log.error("获取指标详情失败: {}", indicatorName, e);
        return createDefaultIndicatorDetail(indicatorName);
    }
}
```

### 4. 代码复用

- 抽取常用的列配置为公共方法
- 建立指标类型与构建方法的映射关系
- 使用工厂模式封装复杂的业务逻辑

## 前端接口对接

生成的`IndicatorDetail`对象可直接作为API响应返回给前端：

```java
@RestController
@RequestMapping("/api/pms/indicator")
public class IndicatorDetailController {
    
    @Autowired
    private IndicatorDetailService indicatorDetailService;
    
    @GetMapping("/detail/{indicatorName}")
    public IndicatorDetail getIndicatorDetail(@PathVariable String indicatorName) {
        return indicatorDetailService.getIndicatorDetailByName(indicatorName);
    }
}
```

前端调用后，数据会自动适配`IndicatorDetailModal`组件的展示需求。

## 扩展指南

### 添加新的渲染类型

1. 在`ColumnRenderType`枚举中添加新类型
2. 在前端组件中实现对应的渲染逻辑
3. 在`ColumnFactory`中添加快速构建方法

### 添加新的预制模板

1. 在`IndicatorDetailFactory`中添加静态方法
2. 使用建造者模式构建标准结构
3. 提供必要的文档和示例

这套工具设计既保证了灵活性，又提供了易用性，能够满足各种指标详情展示的需求。 