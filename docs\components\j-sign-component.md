---
description:
globs:
alwaysApply: false
---
# j-sign 组件

## 📝 组件概述
`j-sign` 组件是一个签名确认组件，用于在业务流程中进行签字确认操作。它支持密码验证、签名绘制等功能，常用于审批流程、电子签名等场景。

## 🔧 Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| placeholder | String | '请输入密码' | 密码输入框的占位提示文字 |
| width | Number | 300 | 签名区域宽度 |
| height | Number | 200 | 签名区域高度 |
| lineWidth | Number | 2 | 签名线条宽度 |
| lineColor | String | '#000000' | 签名线条颜色 |
| background | String | '#ffffff' | 签名区域背景色 |
| showClear | Boolean | true | 是否显示清除按钮 |
| showSubmit | Boolean | true | 是否显示提交按钮 |
| needPassword | Boolean | true | 是否需要密码验证 |
| passwordLabel | String | '签名密码' | 密码输入框标签文字 |

## 🎯 事件

| 事件名 | 参数 | 说明 |
| --- | --- | --- |
| done | (result: {password: string, signImageData: string}) | 签名完成事件，返回密码和签名图像数据 |
| cancel | - | 取消签名事件 |
| error | (error: Error) | 签名过程中发生错误事件 |
| clear | - | 清除签名事件 |

## 📋 插槽

| 插槽名 | 说明 |
| --- | --- |
| default | 默认插槽，可自定义签名区域上方内容 |
| footer | 自定义底部按钮区域 |
| passwordInput | 自定义密码输入区域 |

## 🌟 使用示例

### 基础使用
```vue
<template>
  <j-modal v-model:show="showSignModal" title="签字确认" width="20%" :show-btn="false">
    <template #content>
      <j-sign @done="signDone" placeholder="请输入个人签章密码" />
    </template>
  </j-modal>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  
  const showSignModal = ref(false)
  
  const signDone = (result) => {
    const { password, signImageData } = result
    // 处理签名完成事件，例如提交到服务器
    console.log('密码:', password)
    console.log('签名图像数据:', signImageData)
    showSignModal.value = false
  }
  
  const openSignModal = () => {
    showSignModal.value = true
  }
</script>
```

### 自定义签名区域
```vue
<template>
  <j-sign 
    @done="handleSignDone" 
    @cancel="handleSignCancel"
    :width="400"
    :height="300"
    lineColor="#1890ff"
    :lineWidth="3"
    background="#f0f0f0"
  >
    <div class="sign-title">请在下方区域签名</div>
  </j-sign>
</template>

<script lang="tsx" setup>
  const handleSignDone = (result) => {
    // 处理签名完成事件
  }
  
  const handleSignCancel = () => {
    // 处理取消签名事件
  }
</script>

<style scoped>
.sign-title {
  text-align: center;
  margin-bottom: 10px;
  font-weight: bold;
}
</style>
```

### 业务流程中的使用
```vue
<template>
  <div>
    <n-button @click="handleApprove">审批通过</n-button>
    
    <j-modal v-model:show="showSignModal" title="审批确认" width="30%" :show-btn="false">
      <template #content>
        <div class="approval-info">
          <p>您正在审批单号: {{ approvalNo }}</p>
          <p>申请人: {{ applicantName }}</p>
        </div>
        <j-sign 
          @done="handleSignDone" 
          placeholder="请输入您的审批密码"
          passwordLabel="审批密码"
        />
      </template>
    </j-modal>
  </div>
</template>

<script lang="tsx" setup>
  import { ref } from 'vue'
  import { approveWorkflow } from './api'
  
  const approvalNo = ref('*********')
  const applicantName = ref('张三')
  const showSignModal = ref(false)
  
  const handleApprove = () => {
    showSignModal.value = true
  }
  
  const handleSignDone = async (result) => {
    try {
      await approveWorkflow({
        approvalNo: approvalNo.value,
        password: result.password,
        signImage: result.signImageData
      })
      // 处理成功
      showSignModal.value = false
    } catch (error) {
      // 处理错误
    }
  }
</script>

<style scoped>
.approval-info {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}
</style>
```

## 🔍 组件实现细节

`j-sign` 组件使用 HTML5 Canvas 实现签名绘制功能，支持触控设备和鼠标输入。组件内部管理签名状态，提供清除和重新签名的功能。

对于密码验证，组件默认使用 naive-ui 的 `n-input` 组件，支持密码输入和验证。签名完成后，组件将密码和签名图像数据（Base64 格式）一起通过 `done` 事件返回。

## ⚠️ 注意事项

1. 签名图像数据为 Base64 格式，可以直接用于图像显示或上传到服务器
2. 密码验证通常需要与后端接口配合使用，确保安全性
3. 在移动设备上使用时，需要注意签名区域的大小和触控体验
4. 签名组件通常与弹窗组件（j-modal）配合使用，形成完整的签名流程
