# MobileDetailDrawer 移动端详情抽屉组件使用指南

## 概述

`MobileDetailDrawer` 是一个专为移动端设计的详情展示抽屉组件，参考了 `AdaptiveDataTable` 的卡片渲染方式，但针对详情信息进行了优化，每行只显示一个字段，提供更好的移动端阅读体验。

## 特性

- 🎨 **现代卡片设计**：每个字段独立卡片显示，清晰易读
- 📱 **移动端优化**：专为移动设备设计的布局和交互
- 🔍 **智能搜索**：支持字段标签和内容的实时搜索过滤
- 🎯 **字段类型支持**：支持文本、标签、状态、日期、数字等多种字段类型
- ⚡ **自定义渲染**：支持自定义组件渲染和格式化函数
- 🔧 **操作按钮**：内置操作按钮区域，支持多种按钮类型
- 🌙 **主题适配**：支持亮色/暗色主题自动切换
- 💡 **高亮字段**：支持重要字段高亮显示
- ✨ **搜索高亮**：匹配的字段会自动高亮显示

## 基本用法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <n-button @click="showDetail = true">查看详情</n-button>
    
    <!-- 详情抽屉 -->
    <MobileDetailDrawer
      v-model:show="showDetail"
      :title="'用户详情'"
      :subtitle="'用户基本信息'"
      :data="userData"
      :fields="detailFields"
      :actions="detailActions"
      :searchable="true"
      @action="handleAction"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MobileDetailDrawer from '@/components/common/crud/mobileDetailDrawer.vue'
import type { DetailField, DetailAction } from '@/components/common/crud/mobileDetailDrawer.vue'
import { UserOutline, PhoneOutline, MailOutline } from '@vicons/ionicons5'

const showDetail = ref(false)

// 示例数据
const userData = ref({
  id: '001',
  name: '张三',
  phone: '13800138000',
  email: '<EMAIL>',
  status: '正常',
  createTime: '2024-01-15 10:30:00',
  loginCount: 156,
  department: '技术部',
  role: '高级工程师'
})

// 字段配置
const detailFields: DetailField[] = [
  {
    key: 'name',
    label: '姓名',
    type: 'text',
    icon: UserOutline,
    highlight: true // 高亮显示
  },
  {
    key: 'phone',
    label: '手机号',
    type: 'text',
    icon: PhoneOutline
  },
  {
    key: 'email',
    label: '邮箱',
    type: 'text',
    icon: MailOutline
  },
  {
    key: 'status',
    label: '状态',
    type: 'tag'
  },
  {
    key: 'createTime',
    label: '创建时间',
    type: 'date'
  },
  {
    key: 'loginCount',
    label: '登录次数',
    type: 'number'
  },
  {
    key: 'department',
    label: '部门',
    type: 'text'
  },
  {
    key: 'role',
    label: '职位',
    type: 'text'
  }
]

// 操作按钮配置
const detailActions: DetailAction[] = [
  {
    key: 'edit',
    label: '编辑',
    type: 'primary',
    icon: EditOutline
  },
  {
    key: 'delete',
    label: '删除',
    type: 'error',
    icon: TrashOutline
  }
]

// 处理操作按钮点击
const handleAction = (action: DetailAction, data: any) => {
  console.log('操作:', action.key, '数据:', data)
  
  switch (action.key) {
    case 'edit':
      // 处理编辑逻辑
      break
    case 'delete':
      // 处理删除逻辑
      break
  }
}
</script>
```

## 字段类型

### 基本类型

```typescript
// 文本类型
{
  key: 'name',
  label: '姓名',
  type: 'text'
}

// 标签类型 - 自动根据内容判断颜色
{
  key: 'status',
  label: '状态',
  type: 'tag'
}

// 日期类型 - 自动格式化
{
  key: 'createTime',
  label: '创建时间',
  type: 'date'
}

// 数字类型 - 自动添加千分位分隔符
{
  key: 'amount',
  label: '金额',
  type: 'number'
}
```

### 自定义渲染

```typescript
// 使用自定义组件
{
  key: 'avatar',
  label: '头像',
  render: defineComponent({
    props: ['value', 'data', 'field'],
    setup(props) {
      return () => (
        <n-avatar src={props.value} size="medium" />
      )
    }
  })
}

// 使用格式化函数
{
  key: 'salary',
  label: '薪资',
  formatter: (value) => `¥${value?.toLocaleString() || 0}`
}
```

## 搜索功能

### 启用搜索

```vue
<MobileDetailDrawer
  :searchable="true"
  :fields="detailFields"
  :data="userData"
/>
```

### 搜索特性

- **实时搜索**：输入关键词即时过滤字段
- **多维度匹配**：同时搜索字段标签和字段值
- **搜索统计**：显示匹配字段数量
- **高亮显示**：匹配的字段会自动高亮
- **大小写不敏感**：搜索时忽略大小写
- **清空功能**：支持一键清空搜索内容

### 搜索样式

搜索匹配的字段会应用特殊样式：
- 橙色边框和背景色
- 字段标签和值的颜色变化
- 微妙的阴影效果
- 暗色主题下的适配

## API 接口

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| show | boolean | false | 是否显示抽屉 |
| title | string | '详情' | 抽屉标题 |
| subtitle | string | '' | 副标题 |
| data | Record<string, any> | {} | 详情数据 |
| fields | DetailField[] | [] | 字段配置列表 |
| actions | DetailAction[] | [] | 操作按钮配置 |
| searchable | boolean | true | 是否启用搜索功能 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:show | (show: boolean) | 抽屉显示状态变化 |
| action | (action: DetailAction, data: any) | 操作按钮点击 |

### DetailField 接口

```typescript
interface DetailField {
  key: string                           // 字段键名
  label: string                         // 字段标签
  type?: 'text' | 'tag' | 'status' | 'date' | 'number'  // 字段类型
  icon?: Component                      // 字段图标
  render?: Component                    // 自定义渲染组件
  highlight?: boolean                   // 是否高亮显示
  formatter?: (value: any) => string    // 自定义格式化函数
}
```

### DetailAction 接口

```typescript
interface DetailAction {
  key: string                           // 操作键名
  label: string                         // 操作标签
  type?: 'primary' | 'success' | 'warning' | 'error' | 'default'  // 按钮类型
  size?: 'small' | 'medium' | 'large'   // 按钮大小
  icon?: Component                      // 按钮图标
  disabled?: boolean                    // 是否禁用
  handler?: (data: any) => void         // 点击处理函数
}
```

## 样式定制

组件支持通过 CSS 变量和类名进行样式定制：

```less
// 自定义卡片样式
.detail-field-card {
  // 修改卡片背景色
  background: #f8f9fa;
  
  // 修改边框样式
  border: 2px solid #dee2e6;
  border-radius: 12px;
  
  // 修改内边距
  padding: 16px 20px;
}

// 自定义高亮字段样式
.detail-field-card.highlight-field {
  border-color: #007bff;
  background: #e3f2fd;
}
```

## 最佳实践

1. **字段排序**：将重要字段放在前面，使用 `highlight: true` 突出显示
2. **图标使用**：为关键字段添加合适的图标，提升用户体验
3. **操作按钮**：危险操作（如删除）使用 `type: 'error'`
4. **数据验证**：在显示前确保数据的完整性和正确性
5. **响应式设计**：组件已内置响应式支持，无需额外处理
6. **搜索优化**：
   - 对于字段较多的详情页面，建议启用搜索功能
   - 确保字段标签清晰明确，便于搜索
   - 重要字段可以使用 `highlight: true` 即使在搜索时也能突出显示
7. **性能考虑**：
   - 搜索功能使用计算属性，性能优化良好
   - 大量字段时搜索可以显著提升用户体验

## 与 AdaptiveDataTable 的区别

- **布局方式**：每行只显示一个字段，适合详情展示
- **卡片设计**：更大的内边距和间距，提供更好的阅读体验
- **字段类型**：支持更多详情展示相关的字段类型
- **操作区域**：独立的操作按钮区域，支持多种操作类型
