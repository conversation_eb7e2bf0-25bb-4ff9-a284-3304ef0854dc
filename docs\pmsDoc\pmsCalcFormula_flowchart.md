# pmsCalcFormula 组件数据流图

```mermaid
flowchart TD
    A[父组件] -->|props.show| B(pmsCalcFormula组件)
    A -->|props.variables| B
    A -->|props.formulaVariablesMap| B
    A -->|v-model:item| B

    B -->|emit update:show| A
    B -->|emit update:variables| A
    B -->|emit update:formulaVariablesMap| A

    subgraph pmsCalcFormula组件内部
        B --> C[computed属性]
        C -->|showFormulaConfiguration| D[模态框显示控制]
        C -->|variablesComputed| E[公式变量]
        C -->|formulaVariablesMapComputed| F[公式变量映射]

        B --> G[defineModel code]
        G --> H[公式编辑器]
        H -->|v-model| G

        E --> I[getResult计算]
        F --> I
        G --> I
        I --> J[result显示]
        I -->|错误处理| K[formulaErrorMessage]
    end

    B -->|v-model:code| A
```

## 数据流说明

1. **父组件传入**:

   - show: 控制模态框显示
   - variables: 公式变量列表
   - formulaVariablesMap: 公式变量映射
   - item: 当前配置项名称(双向绑定)

2. **组件输出**:

   - 更新后的 show 状态
   - 更新后的 variables 数据
   - 更新后的 formulaVariablesMap
   - 编辑后的公式 code(双向绑定)

3. **内部处理**:
   - 通过 computed 属性管理响应式数据
   - 公式编辑器与 code 双向绑定
   - getResult 方法处理公式计算
   - 显示计算结果和错误信息
