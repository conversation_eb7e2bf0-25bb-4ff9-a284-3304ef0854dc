# PMS绩效管理系统用户手册 📊

## 目录
- [系统概述](#系统概述)
- [功能模块介绍](#功能模块介绍)
- [用户操作指南](#用户操作指南)
- [常见问题解答](#常见问题解答)
- [技术支持](#技术支持)

## 系统概述

PMS（Performance Management System）绩效管理系统是一套专为医疗机构设计的综合性绩效管理平台，旨在帮助医院实现科学化、规范化的绩效管理。系统涵盖了从基础配置到绩效计算、从数据采集到报表分析的完整业务流程。

### 系统特点
- 🎯 **全流程管理**：覆盖绩效管理的全生命周期
- 📈 **智能计算**：支持复杂的绩效计算公式和规则
- 📊 **可视化分析**：提供丰富的图表和报表功能
- 🔧 **灵活配置**：支持多种配置方式，适应不同需求
- 🔒 **权限控制**：完善的用户权限管理体系

## 功能模块介绍

### 1. 配置管理 ⚙️

#### 1.1 科室数据配置
**路径**：`pms/pmsConfig/orgDataMapConfig`
**功能**：配置科室与HIS系统的数据映射关系

**操作步骤**：
1. 进入科室数据配置页面
2. 选择需要配置的科室
3. 设置与HIS系统的映射关系
4. 保存配置

#### 1.2 收费项目配置
**路径**：`pms/pmsConfig/itemChargeCfg`
**功能**：配置收费项目与绩效计算的关联关系

#### 1.3 资产收费配置
**路径**：`pms/pmsConfig/assetChargeCfg`
**功能**：配置医疗设备资产的收费标准

#### 1.4 医嘱项目配置
**路径**：`pms/pmsConfig/pmsDrordCfg`
**功能**：配置医嘱项目的绩效计算规则

#### 1.5 用户配置管理
- **用户分组**：`pms/pmsConfig/userGroup`
- **用户配置**：`pms/pmsConfig/userCustomConfig`
- **自定义用户配置**：`pms/pmsConfig/userConfigRelation`

### 2. 绩效配置与模板 📋

#### 2.1 绩效计算科室模版配置
**路径**：`pms/pmsCalc/pmsAwardCalcDeptTemplateConfig`
**功能**：为不同科室配置专属的绩效计算模板

**配置要点**：
- 选择适用的科室类型
- 设置计算周期
- 配置绩效指标权重
- 定义计算公式

#### 2.2 绩效信息采集配置
**路径**：`pms/pmsCalc/pmsInformationCollectionConfig`
**功能**：配置绩效数据的自动采集规则

#### 2.3 绩效奖计算项目配置
**路径**：`pms/pmsCalc/pmsAwardCalcItemConfig`
**功能**：配置具体的绩效计算项目和规则

#### 2.4 奖励系数配置
**路径**：`pms/pmsCalc/pmsAwardCoefficientConfig`
**功能**：设置各类奖励的系数参数

#### 2.5 采集科室配置
**路径**：`pms/pmsCalc/pmsHrmOrgMap`
**功能**：配置参与绩效计算的科室范围

### 3. 绩效数据处理 🔄

#### 3.1 绩效信息采集
**路径**：`pms/pmsCalc/pmsInformationCollection`
**功能**：执行绩效数据的采集任务

**操作流程**：
1. 选择采集时间范围
2. 选择采集科室
3. 启动采集任务
4. 监控采集进度
5. 查看采集结果

#### 3.2 科室月度绩效计算
**路径**：`pms/pmsCalc/monthlyPerformance`
**功能**：执行月度绩效计算

**计算步骤**：
1. **选择计算月份**：在页面顶部选择需要计算的月份
2. **选择科室**：从左侧科室列表中选择要计算的科室
3. **查看计算条件**：确认计算参数和配置
4. **执行计算**：点击"计算绩效"按钮
5. **查看结果**：在右侧查看计算结果和详细数据
6. **导出报表**：可导出Excel格式的计算结果

**功能特色**：
- 支持单科室和批量计算
- 实时显示计算进度
- 提供详细的计算日志
- 支持计算结果的预览和修正

#### 3.3 月度绩效确认申请
**路径**：`pms/pmsCalc/monthlyPerformanceApply`
**功能**：提交绩效计算结果的审核申请

#### 3.4 科室每月人数上报
**路径**：`pms/PmsMonthlyDeptStaffNumberReport`
**功能**：各科室上报月度人员数量

### 4. 成本管控 💰

#### 4.1 成本管控采集配置
**路径**：`pms/pmsCalc/pmsIAEBalance/IaeBalanceConfig`
**功能**：配置成本数据的采集规则

#### 4.2 成本管控数据采集
**路径**：`pms/pmsCalc/pmsIAEBalance/IaeBalanceCollection`
**功能**：执行成本数据采集

#### 4.3 医疗服务收入
**路径**：`pms/pmsCalc/pmsIAEBalanceReport/medicalServiceRevenue`
**功能**：查看医疗服务收入报表

#### 4.4 成本管控报表
**路径**：`pms/pmsCalc/pmsIAEBalanceReport`
**功能**：生成和查看成本管控相关报表

### 5. 绩效查询与分析 📈

#### 5.1 科室每月人数
**路径**：`pms/PmsMonthlyDeptStaffNumber`
**功能**：查询各科室的月度人员统计

#### 5.2 管理岗位统计绩效
**路径**：`pms/pmsCalc/managerPerformance`
**功能**：查看管理岗位的绩效统计

#### 5.3 月度绩效汇总
**路径**：`pms/pmsCalc/monthlyPerformanceSummary`
**功能**：查看全院月度绩效汇总数据

#### 5.4 每月上报看板
**路径**：`pms/PmsMonthlyReportTaskBoard`
**功能**：监控各科室的数据上报情况

### 6. 效益分析 📊

#### 6.1 设备效益报表
**路径**：`pms/pmsInseqiEfft`
**功能**：分析医疗设备的投入产出效益

### 7. 数据自动化采集 🤖

#### 7.1 绩效数据仓库
**路径**：`pms/pmsCalc/pmsDataETL`
**功能**：管理绩效数据的ETL流程

**主要功能**：
- 数据抽取（Extract）
- 数据转换（Transform）
- 数据加载（Load）
- 任务调度管理
- 执行状态监控

#### 7.2 脚本编辑
**路径**：`pms/ms`
**功能**：编辑和管理数据处理脚本

## 用户操作指南

### 首次使用系统

1. **登录系统**
   - 使用管理员提供的账号密码登录
   - 首次登录需要修改密码

2. **熟悉界面**
   - 左侧为功能菜单
   - 右侧为操作区域
   - 顶部为快捷操作栏

3. **基础配置**
   - 先完成科室数据配置
   - 设置收费项目配置
   - 配置用户权限

### 月度绩效计算流程

1. **数据准备**
   - 确保基础配置完成
   - 检查数据采集是否正常
   - 验证科室人数上报

2. **执行计算**
   - 进入月度绩效计算页面
   - 选择计算月份和科室
   - 启动计算任务
   - 监控计算进度

3. **结果确认**
   - 查看计算结果
   - 核对关键数据
   - 导出计算报表
   - 提交审核申请

### 报表查看与导出

1. **选择报表类型**
   - 根据需要选择相应的报表模块
   - 设置查询条件

2. **查看报表数据**
   - 在线浏览报表内容
   - 使用筛选和排序功能

3. **导出报表**
   - 选择导出格式（Excel/PDF）
   - 下载到本地

## 常见问题解答

### Q1：绩效计算失败怎么办？
**A1**：
1. 检查基础配置是否完整
2. 确认数据采集是否成功
3. 查看计算日志中的错误信息
4. 联系技术支持

### Q2：如何修改绩效计算公式？
**A2**：
1. 进入绩效奖计算项目配置
2. 找到对应的计算项目
3. 修改公式内容
4. 保存并测试

### Q3：数据采集失败的原因？
**A3**：
1. 检查网络连接
2. 确认数据源系统状态
3. 验证采集配置是否正确
4. 查看采集日志

### Q4：如何设置用户权限？
**A4**：
1. 进入用户配置管理
2. 创建用户分组
3. 分配功能权限
4. 关联用户到分组

## 技术支持

### 联系方式
- **技术支持热线**：400-XXX-XXXX
- **邮箱支持**：<EMAIL>
- **在线客服**：系统内置客服功能

### 系统维护时间
- **日常维护**：每日 02:00-04:00
- **月度维护**：每月第一个周日 02:00-06:00

### 版本更新
系统会定期发布更新版本，包含新功能和问题修复。更新通知会通过系统消息推送给用户。

## 详细操作说明

### 月度绩效计算详细流程

#### 界面布局说明
月度绩效计算页面采用左右分栏设计：

**左侧区域**：
- 科室搜索框：支持按科室名称快速搜索
- 科室列表：显示所有可计算的科室，支持按类型筛选
- 计算进度：实时显示各科室的计算状态

**右侧区域**：
- 查询条件：月份选择、计算参数设置
- 操作面板：计算、导出、查看等功能按钮
- 数据展示：绩效数据表格、统计图表
- 标签页：绩效数据、合计统计、自定义指标、侧边信息、使用文档

#### 计算操作步骤

1. **选择计算月份**
   ```
   在右侧"查询条件"区域选择要计算的月份
   系统支持选择当前月份及之前的月份
   ```

2. **选择科室**
   ```
   在左侧科室列表中点击要计算的科室
   可以使用搜索框快速定位科室
   支持按科室类型（医生、护士、医技、行政）筛选
   ```

3. **查看计算配置**
   ```
   选择科室后，右侧会显示该科室的计算模板信息
   包括：模板名称、计算项目、公式配置等
   ```

4. **执行计算**
   ```
   点击"计算绩效"按钮启动计算
   系统会显示计算进度条
   计算完成后自动刷新结果数据
   ```

5. **查看计算结果**
   ```
   在"绩效数据"标签页查看详细计算结果
   在"合计统计"标签页查看汇总数据
   在"自定义指标"标签页查看特殊指标
   ```

6. **导出数据**
   ```
   点击"导出Excel"按钮
   选择导出范围（当前科室/全部科室）
   系统生成Excel文件并提供下载链接
   ```

### 数据采集操作指南

#### 绩效信息采集

1. **配置采集规则**
   - 进入"绩效信息采集配置"页面
   - 设置采集数据源（HIS、LIS、PACS等）
   - 配置采集频率和时间窗口
   - 设置数据验证规则

2. **执行采集任务**
   - 进入"绩效信息采集"页面
   - 选择采集时间范围
   - 选择采集科室范围
   - 点击"开始采集"按钮

3. **监控采集进度**
   - 实时查看采集进度条
   - 查看采集日志信息
   - 处理采集异常情况

#### 成本数据采集

1. **配置成本采集**
   - 设置成本数据源
   - 配置成本分摊规则
   - 设置采集周期

2. **执行成本采集**
   - 选择采集月份
   - 启动采集任务
   - 验证采集结果

### 报表管理操作

#### 生成月度绩效报表

1. **选择报表类型**
   - 科室绩效明细表
   - 全院绩效汇总表
   - 绩效对比分析表

2. **设置报表参数**
   - 选择统计月份
   - 选择科室范围
   - 设置对比基准

3. **生成和导出**
   - 点击"生成报表"
   - 在线预览报表内容
   - 导出为Excel或PDF格式

#### 成本管控报表

1. **收入成本分析**
   - 查看各科室收入构成
   - 分析成本结构
   - 计算收支比例

2. **设备效益分析**
   - 查看设备使用率
   - 分析设备收益
   - 评估投资回报

### 系统配置管理

#### 科室数据配置

1. **新增科室映射**
   ```
   1. 点击"新增"按钮
   2. 选择PMS系统中的科室
   3. 选择对应的HIS科室
   4. 设置映射关系
   5. 保存配置
   ```

2. **修改映射关系**
   ```
   1. 在列表中找到要修改的映射
   2. 点击"编辑"按钮
   3. 修改映射信息
   4. 保存更改
   ```

#### 绩效模板配置

1. **创建新模板**
   ```
   1. 进入"绩效计算科室模版配置"
   2. 点击"新增模板"
   3. 设置模板基本信息
   4. 配置计算项目
   5. 设置计算公式
   6. 保存模板
   ```

2. **配置计算公式**
   ```
   支持的公式类型：
   - 固定值：${itemName.xxx}
   - 目标值：${targetValue.xxx}
   - 常量：${const.xxx}
   - 绩效项目：${calculated.xxx}
   - 跨模板引用：${template[模板ID].<变量>.<类型>}
   ```

### 权限管理

#### 用户分组管理

1. **创建用户分组**
   - 设置分组名称
   - 配置分组权限
   - 分配功能模块

2. **分配用户权限**
   - 将用户加入相应分组
   - 设置个人特殊权限
   - 配置数据访问范围

#### 数据权限控制

1. **科室数据权限**
   - 设置用户可访问的科室范围
   - 配置数据查看权限
   - 设置操作权限

2. **功能权限控制**
   - 配置菜单访问权限
   - 设置操作按钮权限
   - 控制数据导出权限

## 系统维护与故障处理

### 日常维护检查

1. **数据完整性检查**
   - 检查采集数据是否完整
   - 验证计算结果准确性
   - 确认报表数据一致性

2. **系统性能监控**
   - 监控系统响应时间
   - 检查数据库性能
   - 观察内存使用情况

### 常见故障处理

#### 计算失败处理

1. **检查数据源**
   ```
   - 确认HIS系统连接正常
   - 检查数据采集是否成功
   - 验证基础配置完整性
   ```

2. **查看错误日志**
   ```
   - 进入系统日志页面
   - 查找计算相关错误信息
   - 分析错误原因
   ```

3. **重新计算**
   ```
   - 修复发现的问题
   - 清除错误数据
   - 重新执行计算任务
   ```

#### 数据采集异常

1. **网络连接问题**
   ```
   - 检查网络连接状态
   - 测试数据源系统访问
   - 确认防火墙设置
   ```

2. **数据格式问题**
   ```
   - 检查数据源格式变化
   - 更新采集配置
   - 调整数据转换规则
   ```

### 备份与恢复

#### 数据备份

1. **定期备份**
   - 每日自动备份关键数据
   - 每周完整备份数据库
   - 每月备份系统配置

2. **备份验证**
   - 定期测试备份文件完整性
   - 验证恢复流程可用性

#### 数据恢复

1. **故障恢复**
   - 评估数据损失范围
   - 选择合适的备份点
   - 执行数据恢复操作

2. **验证恢复结果**
   - 检查数据完整性
   - 验证系统功能正常
   - 确认用户可正常访问

---

*本手册版本：v1.0*
*最后更新时间：2024年12月*
*如有疑问，请联系系统管理员*
