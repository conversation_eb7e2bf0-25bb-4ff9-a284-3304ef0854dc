# Request 工具函数重构文档

## 📋 重构概述

将 `src/utils/request.ts` 中的部分工具函数移动到 `src/utils/requestUtil.ts` 中，以提高代码的模块化和可维护性。

## 🔧 移动的函数

### 缓存相关函数
- `stableJsonStringify` - 稳定地将对象转换为 JSON 字符串
- `generateCacheKey` - 生成安全的缓存键
- `safeStorageSet` - 尝试存储到 sessionStorage，失败时降级到 localStorage
- `safeStorageGet` - 从 sessionStorage 或 localStorage 获取数据
- `safeStorageRemove` - 从 sessionStorage 和 localStorage 移除数据
- `withCacheRequest` - 使用缓存包装请求方法
- `clearWithCacheRequestCache` - 清理 withCacheRequest 生成的所有缓存

### 数据转换函数
- `convertStringToArray` - 将逗号分隔的字符串转换为数组

## 📁 文件结构

### `src/utils/requestUtil.ts` (新文件)
包含所有移动的工具函数，每个函数都有完整的 TypeScript 类型定义和中文注释。

### `src/utils/request.ts` (修改)
- 移除了上述工具函数的实现
- 添加了从 `requestUtil.ts` 的导入
- 重新导出这些函数以保持向后兼容性

## 🔄 导入导出关系

```typescript
// requestUtil.ts 导出
export function withCacheRequest(...)
export function clearWithCacheRequestCache(...)
export function convertStringToArray(...)

// request.ts 重新导出
import { withCacheRequest, clearWithCacheRequestCache, convertStringToArray } from '@/utils/requestUtil'
export { axiosRequest, withCacheRequest, clearWithCacheRequestCache, convertStringToArray }
```

## ✅ 兼容性保证

- 现有代码无需修改导入路径
- 所有函数签名保持不变
- 功能完全一致

## 🎯 优势

1. **模块化**: 将相关功能分组到独立文件中
2. **可维护性**: 更容易定位和修改特定功能
3. **可复用性**: 工具函数可以独立导入使用
4. **向后兼容**: 不破坏现有代码

## 📝 使用示例

```typescript
// 方式1: 从 request.ts 导入 (推荐，保持兼容性)
import { withCacheRequest } from '@/utils/request'

// 方式2: 直接从 requestUtil.ts 导入
import { withCacheRequest } from '@/utils/requestUtil'
```

## 🔍 注意事项

- IDE 可能需要重启以清除缓存
- TypeScript 编译检查应该通过
- 所有现有的 API 调用应该继续正常工作
