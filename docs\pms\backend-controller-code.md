# 后端控制器代码补充

## 需要在 PmsAwardCoefficientConfigController.java 中添加的代码

```java
/**
 * 批量更新指标详情查看权限
 */
@ApiOperation("批量更新指标详情查看权限")
@PutMapping("/batchUpdateAllowViewItemDetail")
public CommonResult<?> batchUpdateAllowViewItemDetail(@RequestBody PmsItemTemplateRelationDto dto) {
    pmsAwardCoefficientConfigWriteService.batchUpdateAllowViewItemDetail(dto);
    return CommonResult.success();
}
```

## 需要在 PmsAwardCoefficientConfigWriteServiceImpl.java 中修复的代码

```java
@Override
@Transactional(rollbackFor = Exception.class)
public void batchUpdateAllowViewItemDetail(PmsItemTemplateRelationDto dto) {
    if (dto.getTemplateIds() == null || dto.getTemplateIds().isEmpty() || dto.getItemCode() == null) {
        return;
    }

    // 将String类型的allowViewItemDetail转换为Integer
    Integer allowViewItemDetailValue = "1".equals(dto.getAllowViewItemDetail()) ? 1 : 0;

    var updateWrapper = new UpdateWrapper<PmsAwardCoefficientConfigDto>()
            .eq("item_code", dto.getItemCode())
            .in("template_id", dto.getTemplateIds())
            .set("allow_view_item_detail", allowViewItemDetailValue);
    update(updateWrapper);
}
```

## 完整的功能流程

1. **前端操作** → 用户选择表格行 → 点击批量开启/关闭按钮
2. **API调用** → 调用 `/pms/pmsAwardCoefficientConfig/batchUpdateAllowViewItemDetail` 接口
3. **后端处理** → 控制器接收请求 → 调用服务层方法 → 批量更新数据库
4. **结果返回** → 返回操作结果 → 前端显示提示信息 → 刷新数据

## API参数格式

```json
{
  "itemCode": "ITEM001",
  "templateIds": [1, 2, 3],
  "allowViewItemDetail": "1"
}
```

- `itemCode`: 指标编码
- `templateIds`: 模板ID数组
- `allowViewItemDetail`: "1"表示开启，"0"表示关闭
