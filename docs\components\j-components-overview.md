---
description:
globs:
alwaysApply: false
---
# 自定义组件系统概述

## 📦 组件系统架构
- 项目使用前缀 `j-` 标识自定义组件
- 组件定义在 [`src/types/common/jcomponents.ts`](mdc:src/types/common/jcomponents.ts)
- 组件类型声明在 [`src/types/jp.d.ts`](mdc:src/types/jp.d.ts)
- 组件实现位于 [`src/components/common`](mdc:src/components/common) 目录下

## 🧩 组件分类

### 基础组件
- `j-container`: 容器组件，提供统一的内容包裹
- `j-title-line`: 标题行组件
- `j-select`: 下拉选择组件
- `j-context-menu`: 右键菜单组件
- `j-upload`: 文件上传组件
- `j-export`: 数据导出组件
- `j-icon`: 图标组件
- `j-image`: 图片组件
- `j-modal`: 模态框组件
- `j-preview`/`j-npreview`: 文件预览组件
- `j-column`: 列组件
- `j-search-title`: 搜索标题组件
- `j-form-item`: 表单项组件
- `j-upload-preview`: 上传预览组件

### 业务组件
- `j-crud`: 增删改查组件
- `j-flow`: 流程组件
- `j-org-flow`: 组织流程组件
- `j-organization-flow`: 组织架构流程组件
- `j-bpmn`: 业务流程模型组件
- `j-action-form`: 操作表单组件
- `j-audit-flow`: 审计流程组件
- `j-sign`: 签名组件

### 业务搜索组件
- `j-bus-emp-search`: 员工搜索组件
- `j-bus-hos-org`: 医院组织搜索
- `j-bus-asset-search`: 资产搜索组件
- `j-bus-audit-select`: 审计选择组件
- `j-bus-audit-progress`: 审计进度组件
- `j-bus-audit-execution`: 审计执行组件
- `j-bus-audit-steps`: 审计步骤组件
- `j-bus-addr`: 地址组件
- `j-bus-invoice-identify`: 发票识别组件

### 其他组件
- `j-hospital`: 医院组件
- `j-org`: 组织组件
- `j-text-edit`: 文本编辑组件
- `j-financial-account`: 财务账户组件
- `ContentWrap`: 内容包装组件
- `dict-tag`: 字典标签组件

## 🔧 组件使用方式

### 全局注册组件
所有组件均已全局注册，可直接在模板中使用:
```vue
<template>
  <j-crud></j-crud>
  <j-modal></j-modal>
</template>
```

### TypeScript 支持
组件类型已在全局声明文件中定义，提供完整的 TypeScript 类型支持:
```typescript
// 在 setup 中引用组件实例
const crudRef = ref<InstanceType<typeof import('@/types/common/jcomponents').CRUD>>()
```

## 📚 组件文档
每个组件都有详细的使用文档，请参考各组件的规则文件了解详细用法。
