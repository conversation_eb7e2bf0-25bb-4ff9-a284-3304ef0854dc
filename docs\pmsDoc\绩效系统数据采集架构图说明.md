# 绩效系统数据采集流程与架构图说明 📊

## 概述

本文档详细说明了绩效系统数据采集的完整流程和架构设计。该系统采用ETL（Extract-Transform-Load）模式，实现了从外部数据源到绩效数据库的自动化数据采集和处理。

## 系统架构组件

### 1. 数据源层 🗄️

#### 外部数据源（万能数据库）
- **功能**：提供原始的绩效相关数据
- **数据类型**：包含成本控制数据和绩效计算所需的基础数据
- **访问方式**：通过Magic API进行数据查询和获取

### 2. 接口层 🔌

#### Magic API数据接口层
- **功能**：作为数据访问的统一接口
- **职责**：
  - 执行数据查询请求
  - 处理文件下载
  - 数据格式初步处理
- **特点**：提供标准化的API接口，屏蔽底层数据源的复杂性

#### Redis缓存
- **功能**：缓存已下载的文件路径
- **优势**：
  - 避免重复下载相同文件
  - 提高系统性能
  - 减少网络开销
- **缓存策略**：基于文件内容的SHA256哈希值进行缓存键管理

### 3. 调度层 ⚙️

#### BatchPmsCaptureScheduler（批量采集调度器）
- **核心功能**：
  - **定时调度**：基于Cron表达式的定时任务执行
  - **批次管理**：为每个执行批次生成唯一的批次编号
  - **并行执行**：使用线程池并行处理多个采集任务
  - **任务监控**：跟踪批次执行状态和进度
- **执行模式**：
  - 自动定时执行
  - 手动触发执行
  - 失败任务重试

#### PmsCaptureScheduler（单任务调度器）
- **核心功能**：
  - **ETL流程执行**：协调数据抽取、转换、加载的完整流程
  - **状态监控**：实时更新任务执行状态
  - **错误处理**：处理执行过程中的异常情况
- **执行流程**：
  1. 接收批量调度器的任务分配
  2. 执行具体的ETL操作
  3. 更新任务状态
  4. 返回执行结果

### 4. ETL处理层 🔄

#### 数据抽取（Extract）
- **数据源**：通过Magic API从外部数据源获取数据
- **处理方式**：
  - API调用获取数据
  - 文件下载和缓存
  - 数据完整性验证
- **缓存机制**：利用Redis缓存避免重复下载

#### 数据转换（Transform）
- **转换规则**：
  - 数据格式标准化
  - 业务规则应用
  - 数据质量检查
- **转换类型**：
  - JavaScript脚本转换
  - Magic API转换
  - 外部API转换
- **映射处理**：根据配置的数据映射规则进行字段转换

#### 数据加载（Load）
- **目标存储**：
  - 成本控制数据表（pmsIaeReportItem）
  - 绩效计算数据表（pmsAwardCalcItem）
- **加载策略**：
  - 批量插入提高性能
  - 数据去重处理
  - 事务保证数据一致性

### 5. 存储层 💾

#### PMS数据库
- **成本控制数据表（pmsIaeReportItem）**：
  - 存储成本相关的绩效数据
  - 支持成本分析和控制
- **绩效计算数据表（pmsAwardCalcItem）**：
  - 存储绩效计算所需的基础数据
  - 支持绩效奖励计算

### 6. 管理层 🎛️

#### 配置管理Controller
- **功能**：
  - 采集配置的CRUD操作
  - 手动任务执行触发
  - 配置同步更新
- **配置内容**：
  - 数据源配置
  - 转换规则配置
  - 调度策略配置

#### 状态监控Service
- **监控内容**：
  - 批次执行状态
  - 任务执行进度
  - 错误信息跟踪
- **功能特性**：
  - 实时状态更新
  - 历史记录查询
  - 异常告警

## 数据流向

```
外部数据源 → Magic API → Redis缓存
                ↓
BatchPmsCaptureScheduler → PmsCaptureScheduler
                ↓
数据抽取 → 数据转换 → 数据加载
                ↓
成本控制数据表 ← → 绩效计算数据表
```

## 关键特性

### 🔄 自动化处理
- 定时自动执行数据采集
- 无需人工干预的ETL流程
- 自动错误恢复和重试机制

### 📈 高性能
- 并行任务执行
- 智能缓存机制
- 批量数据处理

### 🛡️ 可靠性
- 完整的状态监控
- 事务保证数据一致性
- 详细的错误日志记录

### 🔧 可配置性
- 灵活的调度策略配置
- 可定制的数据转换规则
- 动态的数据源配置

## 使用场景

1. **定时数据同步**：每日/每周/每月的定期数据采集
2. **手动数据更新**：按需触发的数据刷新
3. **历史数据迁移**：大批量历史数据的一次性导入
4. **实时数据监控**：数据采集过程的实时状态跟踪

## 技术栈

- **后端框架**：Spring Boot
- **调度框架**：Spring Task Scheduler
- **缓存**：Redis
- **数据库**：MySQL/PostgreSQL
- **API接口**：Magic API
- **并发处理**：Java CompletableFuture
- **配置管理**：JSON配置文件

---

*该架构图展示了绩效系统数据采集的完整技术架构，为系统的维护、扩展和优化提供了清晰的指导。*
