---
description: 
globs: 
alwaysApply: false
---
# j-bus-emp-search (员工搜索组件)

## 📝 组件概述
`j-bus-emp-search` 是一个员工搜索选择组件，基于 Naive UI 的 `n-select` 实现。它允许用户通过输入员工姓名或工号进行远程搜索，并选择一个或多个员工。组件支持显示员工头像、姓名、工号和所属组织等信息。

## 🔧 Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| `value` | `string` \| `string[]` | - | 选中的员工工号。支持 `v-model:value` 双向绑定。 |
| `defaultEmpCode` | `string` | `''` | 默认选中的员工工号 (非受控模式)。 |
| `option` | `any` | - | (此 prop 定义但未在核心逻辑中使用，主要通过 `update:option` 事件对外暴露选中项的完整数据) |
| `remote` | `boolean` | `true` | 是否启用远程搜索。如果为 `false`，则不进行搜索。 |
| `dept` | `string` | - | 筛选指定部门的员工 (组织ID)。 |
| `depts` | `string[]` | - | 筛选指定多个部门的员工 (组织ID列表)。 |
| `multiple` | `boolean` | `false` | 是否支持多选。 |
| `clearable` | `boolean` | `true` | 是否显示清空按钮。 |
| `disabled` | `boolean` | `false` | 是否禁用组件。 |
| `renderAvatar` | `boolean` | `true` | 在多选标签中是否渲染员工头像。 |
| `restrictedEmpType` | `string[]` | - | 限制员工类型 (员工类型代码列表)。 |
| `restrictedEmps` | `string[]` | - | 限制可选的员工 (员工工号列表)。 |
| `autoSelect` | `boolean` | `true` | 当搜索结果只有一项时，是否自动选中该项。 |
| `placeholder` | `string` | (根据 `multiple` 动态生成) | 输入框的占位提示文字。多选时为"搜索职工"，单选时为"选择职工"。 |

## 🎯 事件 (Emits)

| 事件名 | 参数 | 说明 |
| --- | --- | --- |
| `update:value` | `(value: string | string[] | null)` | 选中值更新时触发，用于 `v-model:value`。 |
| `update:option` | `(option: any | any[] | null)` | 选中的员工完整信息对象或对象数组更新时触发。 |
| `changeValue` | `(option: any | any[] | null)` | 选中值发生改变时触发，参数为选中的员工完整信息对象或对象数组。 |
| `search` | `(query: string)` | (由 `n-select` 内部触发) 用户输入搜索关键词时触发。组件内部通过此事件调用 `handleSearch` 方法。 |

## ✨ 插槽 (Slots)

该组件不直接提供自定义插槽，但它基于 `n-select`，`n-select` 本身可能支持一些内部渲染相关的插槽，但在此组件中未特别暴露或使用。

## 🌟 使用示例

### 单选模式
```vue
<template>
  <j-bus-emp-search v-model:value="selectedEmp" @update:option="handleOptionUpdate" />
  <p v-if="selectedEmpInfo">选中的员工: {{ selectedEmpInfo.empName }} ({{ selectedEmpInfo.empCode }})</p>
</template>

<script lang="tsx" setup>
import { ref } from 'vue';

const selectedEmp = ref<string | null>(null);
const selectedEmpInfo = ref<any | null>(null);

const handleOptionUpdate = (option: any) => {
  selectedEmpInfo.value = option;
  console.log('Selected employee option:', option);
};
</script>
```

### 多选模式并限制部门
```vue
<template>
  <j-bus-emp-search 
    v-model:value="selectedEmps" 
    multiple 
    :dept="'ORG001'" 
    placeholder="请搜索并选择技术部员工"
    @update:option="handleMultiOptionUpdate"
  />
  <div v-if="selectedEmpsInfo.length > 0">
    <p>选中的员工列表:</p>
    <ul>
      <li v-for="emp in selectedEmpsInfo" :key="emp.empCode">
        {{ emp.empName }} ({{ emp.empCode }}) - {{ emp.orgName }}
      </li>
    </ul>
  </div>
</template>

<script lang="tsx" setup>
import { ref } from 'vue';

const selectedEmps = ref<string[]>([]);
const selectedEmpsInfo = ref<any[]>([]);

const handleMultiOptionUpdate = (options: any[]) => {
  selectedEmpsInfo.value = options;
  console.log('Selected employees options:', options);
};
</script>
```


## ⚠️ 注意事项


- **受控与非受控**: 可以通过 `v-model:value` 实现受控组件，或仅使用 `defaultEmpCode` 配合事件监听实现非受控组件。

  import {HosOrg} from '@jcomponents'
tsx写法为引入 HosOrg 
