# 自适应数据表格组件 📱💻

一个完全继承NaiveUI DataTable的自适应组件，在PC端保持完全一致的体验，在移动端自动切换为现代简洁的卡片模式。

## 🎯 功能特性

### 核心特性
- ✅ **PC端完全继承** - 100%兼容NaiveUI DataTable的所有功能和API
- ✅ **移动端自动适配** - 根据设备类型自动切换卡片视图
- ✅ **视图切换功能** - 移动端支持卡片/表格视图自由切换
- ✅ **智能列配置** - 支持移动端专用的列显示和布局配置
- ✅ **XxN布局支持** - 默认2列布局，支持1-3列可配置
- ✅ **现代iOS风格** - 简洁现代的设计，支持毛玻璃效果和触摸反馈

### 移动端增强
- 🔄 **智能字段布局** - 支持主标题、副标题、头部、主体、底部位置配置
- ⚙️ **可视化配置** - 提供直观的移动端列配置界面
- 📱 **触摸优化** - 44px最小触摸目标，支持触摸反馈动画
- 🎨 **响应式设计** - 适配不同屏幕尺寸和设备方向

## 📦 安装使用

### 基础用法

```vue
<template>
  <adaptive-data-table
    :data="tableData"
    :columns="tableColumns"
    :loading="loading"
    mobile-title="用户列表"
    :card-columns="2"
    @row-click="handleRowClick"
  />
</template>

<script setup>
import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'

const tableData = ref([
  { id: 1, name: '张三', age: 25, status: '在职' }
])

const tableColumns = ref([
  {
    key: 'name',
    title: '姓名',
    mobileTitle: true, // 移动端主标题
    mobileOrder: 1
  },
  {
    key: 'age',
    title: '年龄',
    mobileOrder: 2
  },
  {
    key: 'status',
    title: '状态',
    mobilePosition: 'header', // 移动端头部显示
    mobileOrder: 3
  }
])
</script>
```

### 移动端列配置

```typescript
interface MobileColumnConfig {
  mobileTitle?: boolean      // 是否作为卡片主标题
  mobileSubtitle?: boolean   // 是否作为卡片副标题
  mobileShow?: boolean       // 是否在移动端显示
  mobileOrder?: number       // 移动端显示顺序
  mobilePosition?: 'header' | 'body' | 'footer'  // 在卡片中的位置
  mobileSpan?: number        // 卡片跨度（用于XxN布局）
}
```

### 完整配置示例

```vue
<template>
  <adaptive-data-table
    :data="tableData"
    :columns="tableColumns"
    :loading="loading"
    :pagination="pagination"
    row-key="id"
    :checked-row-keys="checkedRowKeys"
    
    <!-- 移动端配置 -->
    mobile-title="员工列表"
    :show-view-toggle="true"
    :show-mobile-config="true"
    :card-columns="2"
    :show-actions="true"
    
    <!-- 事件监听 -->
    @update:checked-row-keys="checkedRowKeys = $event"
    @row-click="handleRowClick"
    @update:columns="handleColumnsUpdate"
  >
    <!-- 操作按钮插槽 -->
    <template #actions="{ row }">
      <n-button size="tiny" type="primary" @click.stop="handleEdit(row)">
        编辑
      </n-button>
      <n-popconfirm @positive-click="handleDelete(row)">
        <template #trigger>
          <n-button size="tiny" type="error" @click.stop>
            删除
          </n-button>
        </template>
        确定要删除这条数据吗？
      </n-popconfirm>
    </template>
  </adaptive-data-table>
</template>

<script setup>
const tableColumns = ref([
  {
    key: 'name',
    title: '姓名',
    width: 100,
    mobileTitle: true,        // 主标题
    mobileOrder: 1
  },
  {
    key: 'position',
    title: '职位',
    width: 120,
    mobileSubtitle: true,     // 副标题
    mobileOrder: 2
  },
  {
    key: 'department',
    title: '部门',
    width: 100,
    mobilePosition: 'header', // 头部标签
    mobileOrder: 3,
    render: (row) => {
      return h('n-tag', { type: 'success', size: 'small' }, row.department)
    }
  },
  {
    key: 'status',
    title: '状态',
    width: 80,
    mobilePosition: 'header', // 头部标签
    mobileOrder: 4,
    render: (row) => {
      return h('n-tag', { 
        type: row.status === '在职' ? 'success' : 'error', 
        size: 'small' 
      }, row.status)
    }
  },
  {
    key: 'phone',
    title: '电话',
    width: 120,
    mobileOrder: 5           // 主体内容
  },
  {
    key: 'joinDate',
    title: '入职日期',
    width: 120,
    mobilePosition: 'footer', // 底部信息
    mobileOrder: 6
  },
  {
    key: 'salary',
    title: '薪资',
    width: 100,
    mobileShow: false,        // 移动端隐藏
    mobileOrder: 7
  }
])
</script>
```

## 📋 Props配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| data | Array | [] | 表格数据 |
| columns | Array | [] | 列配置，支持移动端扩展属性 |
| loading | Boolean | false | 加载状态 |
| useMobileView | Boolean | true | 是否启用移动端视图 |
| showMobileHeader | Boolean | true | 是否显示移动端头部 |
| mobileTitle | String | '数据列表' | 移动端标题 |
| showViewToggle | Boolean | true | 是否显示视图切换按钮 |
| showMobileConfig | Boolean | true | 是否显示移动端配置按钮 |
| cardColumns | Number\|String | 2 | 卡片列数，支持响应式 |
| showActions | Boolean | false | 是否显示操作按钮 |

*其他所有NaiveUI DataTable的Props都完全支持*

## 🎪 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| row-click | (row, index) | 行点击事件 |
| update:checked-row-keys | (keys) | 选中行变化事件 |
| update:columns | (columns) | 列配置更新事件 |
| update:filters | (filters) | 筛选器更新事件 |
| update:sorter | (sorter) | 排序器更新事件 |
| update:page | (page) | 页码变化事件 |
| update:page-size | (pageSize) | 页面大小变化事件 |

*其他所有NaiveUI DataTable的事件都完全支持*

## 🎨 样式定制

### TailwindCSS类名

组件使用TailwindCSS进行样式管理，支持以下自定义：

```css
/* 卡片样式定制 */
.mobile-card {
  @apply transition-all duration-200 ease-in-out;
  @apply rounded-lg border-2 border-gray-200;
  @apply hover:shadow-lg hover:-translate-y-0.5;
}

/* iOS风格毛玻璃效果 */
.mobile-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* 触摸反馈 */
.mobile-card:active {
  @apply scale-98;
  transition: transform 0.1s ease-in-out;
}
```

### 响应式断点

```css
/* 小屏幕（手机） */
@media (max-width: 640px) {
  .mobile-card {
    @apply mx-1; /* 减少边距 */
  }
}

/* 中等屏幕（平板） */
@media (max-width: 1024px) and (min-width: 641px) {
  .mobile-card {
    @apply mx-2;
  }
}
```

## 🔧 高级用法

### 自定义渲染函数

```javascript
const columns = [
  {
    key: 'avatar',
    title: '头像',
    mobilePosition: 'header',
    render: (row) => {
      return h('n-avatar', {
        size: 'small',
        src: row.avatar,
        fallbackSrc: '/default-avatar.png'
      })
    }
  },
  {
    key: 'tags',
    title: '标签',
    mobilePosition: 'footer',
    render: (row) => {
      return h('n-space', { size: 4 }, 
        row.tags.map(tag => 
          h('n-tag', { size: 'tiny', type: 'info' }, tag)
        )
      )
    }
  }
]
```

### 动态列配置

```javascript
// 根据用户权限动态显示列
const dynamicColumns = computed(() => {
  const baseColumns = [...staticColumns]
  
  if (userStore.hasPermission('view_salary')) {
    baseColumns.push({
      key: 'salary',
      title: '薪资',
      mobileShow: true,
      mobileOrder: 10
    })
  }
  
  return baseColumns
})
```

## 🚀 最佳实践

### 1. 移动端优化建议

- **主标题选择**：选择最重要的标识字段作为主标题
- **副标题配置**：选择次要但重要的描述字段作为副标题
- **头部标签**：状态、类型等关键信息放在头部
- **底部信息**：时间、统计等次要信息放在底部
- **字段数量**：移动端建议显示6-8个核心字段

### 2. 性能优化

- 使用虚拟滚动处理大量数据
- 合理设置分页大小
- 避免在render函数中进行复杂计算

### 3. 用户体验

- 确保触摸目标至少44px×44px
- 使用适当的加载状态和空状态
- 提供清晰的操作反馈

## 🔍 故障排除

### 常见问题

1. **移动端不显示卡片视图**
   - 检查是否正确注入了`isMobileDevice`
   - 确认`useMobileView`属性为true

2. **列配置不生效**
   - 确保列配置中包含`key`属性
   - 检查`mobileShow`、`mobileOrder`等属性设置

3. **样式显示异常**
   - 确认已正确引入TailwindCSS
   - 检查CSS作用域和优先级

## 📚 相关文档

- [NaiveUI DataTable文档](https://www.naiveui.com/zh-CN/os-theme/components/data-table)
- [移动端适配指南](../mobile/demo/mobile-adaptation.md)
- [TailwindCSS文档](https://tailwindcss.com/docs)
