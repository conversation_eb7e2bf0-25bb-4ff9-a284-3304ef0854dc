import { resolve } from 'path'
import Vue from '@vitejs/plugin-vue'
import VueJsx from '@vitejs/plugin-vue-jsx'
import progress from 'vite-plugin-progress'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver, ElementPlusResolver, NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import viteCompression from 'vite-plugin-compression'
import { CodeInspectorPlugin } from 'code-inspector-plugin'
import AutoImport from 'unplugin-auto-import/vite'
import tailwindcss from '@tailwindcss/vite'

import { visualizer } from 'rollup-plugin-visualizer'
export function createVitePlugins() {
  return [
    Vue(),
    VueJsx(),
    tailwindcss(),
    progress(),
    CodeInspectorPlugin({
      bundler: 'vite',
    }),
    AutoImport({
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        /\.[tj]s$/, // .ts, .js
        /\.[tj]sx$/, // .tsx, .jsx
        /\.vue$/,
        /\.vue\?vue/, // .vue
        /\.md$/, // .md
      ],
      imports: ['vue', 'vue-router', 'pinia'],
      dirs: ['src/store/**'],
      dts: 'src/types/auto-imports.d.ts',
      resolvers: [ElementPlusResolver(), NaiveUiResolver(), AntDesignVueResolver()],
    }),
    // Components({
    // 生成自定义 `auto-components.d.ts` 全局声明
    // dts: 'src/types/auto-components.d.ts',
    // 自定义组件的解析器
    // resolvers: [ElementPlusResolver(), NaiveUiResolver(), AntDesignVueResolver()],
    // directoryAsNamespace: true,
    // dirs: ['src/components'],
    // deep: true,
    // extensions: ['vue'],
    // directives: true,
    // include: [/\.vue$/, /\.vue\?vue/],
    // exclude: [/[\\/]node_modules[\\/]/, /[\\/]\.git[\\/]/],
    // transformer: 'vue3',
    // importPathTransform: (path: string) => path.replace(/\/index$/, ''),
    // }),
    viteCompression({
      verbose: true, // 是否在控制台输出压缩结果
      disable: true, // 是否禁用
      threshold: 4096, // 体积大于 threshold 才会被压缩,单位 b
      algorithm: 'gzip', // 压缩算法,可选 [ 'gzip' , 'brotliCompress' ,'deflate' , 'deflateRaw']
      ext: '.gz', // 生成的压缩包后缀
      deleteOriginFile: false, //压缩后是否删除源文件
    }),
  ]
}
