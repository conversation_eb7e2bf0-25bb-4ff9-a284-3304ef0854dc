<template>
  <div class="demo-page">
    <n-space vertical :size="24">
      <!-- 页面标题 -->
      <div class="demo-header">
        <n-text class="text-2xl font-bold">自适应数据表格组件演示 📱💻</n-text>
        <n-text depth="3" class="mt-2">
          PC端完全继承NaiveUI DataTable，移动端自动切换为卡片模式，支持视图切换和列配置
        </n-text>
      </div>

      <!-- 功能特性 -->
      <n-card title="🎯 功能特性" size="small">
        <n-grid :cols="2" :x-gap="16" :y-gap="12">
          <n-gi>
            <n-space align="center" :size="8">
              <n-icon :component="DesktopOutline" color="#18a058" />
              <n-text>PC端完全继承NaiveUI DataTable</n-text>
            </n-space>
          </n-gi>
          <n-gi>
            <n-space align="center" :size="8">
              <n-icon :component="PhonePortraitOutline" color="#2080f0" />
              <n-text>移动端自动切换卡片模式</n-text>
            </n-space>
          </n-gi>
          <n-gi>
            <n-space align="center" :size="8">
              <n-icon :component="SwapHorizontalOutline" color="#f0a020" />
              <n-text>支持卡片/表格视图切换</n-text>
            </n-space>
          </n-gi>
          <n-gi>
            <n-space align="center" :size="8">
              <n-icon :component="OptionsOutline" color="#e74c3c" />
              <n-text>可配置列显示和布局</n-text>
            </n-space>
          </n-gi>
          <n-gi>
            <n-space align="center" :size="8">
              <n-icon :component="GridOutline" color="#9c27b0" />
              <n-text>支持XxN布局（默认2列）</n-text>
            </n-space>
          </n-gi>
          <n-gi>
            <n-space align="center" :size="8">
              <n-icon :component="ColorPaletteOutline" color="#ff5722" />
              <n-text>现代简洁iOS风格设计</n-text>
            </n-space>
          </n-gi>
        </n-grid>
      </n-card>

      <!-- 设备检测信息 -->
      <n-card title="📱 当前设备信息" size="small">
        <n-descriptions :column="2" size="small">
          <n-descriptions-item label="设备类型">
            <n-tag :type="deviceInfo.type === 'mobile' ? 'success' : 'info'">
              {{ deviceInfo.type === 'mobile' ? '移动端' : '桌面端' }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="屏幕尺寸">
            {{ deviceInfo.width }} × {{ deviceInfo.height }}
          </n-descriptions-item>
          <n-descriptions-item label="当前视图">
            <n-tag :type="deviceInfo.type === 'mobile' ? 'warning' : 'primary'">
              {{ deviceInfo.type === 'mobile' ? '移动端卡片视图' : 'PC端表格视图' }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="建议操作">
            <n-text depth="3">
              {{ deviceInfo.type === 'mobile' ? '可切换表格视图或配置卡片布局' : '缩小浏览器窗口体验移动端效果' }}
            </n-text>
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 演示数据表格 -->
      <n-card title="📊 数据表格演示" size="small">
        <template #header-extra>
          <n-space :size="8">
            <n-button size="small" @click="refreshData">
              <template #icon>
                <n-icon :component="RefreshOutline" />
              </template>
              刷新数据
            </n-button>
            <n-button size="small" @click="addRandomData">
              <template #icon>
                <n-icon :component="AddOutline" />
              </template>
              添加数据
            </n-button>
          </n-space>
        </template>

        <!-- 自适应数据表格 -->
        <adaptive-data-table
          :data="tableData"
          :columns="tableColumns"
          :loading="loading"
          :pagination="pagination"
          row-key="id"
          :checked-row-keys="checkedRowKeys"
          mobile-title="用户列表"
          :show-view-toggle="true"
          :show-mobile-config="true"
          :card-columns="2"
          :show-actions="true"
          @update:checked-row-keys="checkedRowKeys = $event"
          @row-click="handleRowClick"
          @update:columns="handleColumnsUpdate"
        >
          <!-- 操作按钮插槽 -->
          <template #actions="{ row }">
            <n-button size="tiny" type="primary" @click.stop="handleEdit(row)">
              编辑
            </n-button>
            <n-popconfirm @positive-click="handleDelete(row)">
              <template #trigger>
                <n-button size="tiny" type="error" @click.stop>
                  删除
                </n-button>
              </template>
              确定要删除这条数据吗？
            </n-popconfirm>
          </template>
        </adaptive-data-table>
      </n-card>

      <!-- 使用说明 -->
      <n-card title="📖 使用说明" size="small">
        <n-tabs type="line" animated>
          <n-tab-pane name="basic" tab="基础用法">
            <n-code language="vue" :code="basicUsageCode" />
          </n-tab-pane>
          <n-tab-pane name="props" tab="Props配置">
            <n-table :data="propsData" :columns="propsColumns" size="small" />
          </n-tab-pane>
          <n-tab-pane name="events" tab="事件说明">
            <n-table :data="eventsData" :columns="eventsColumns" size="small" />
          </n-tab-pane>
          <n-tab-pane name="mobile" tab="移动端配置">
            <n-code language="typescript" :code="mobileConfigCode" />
          </n-tab-pane>
        </n-tabs>
      </n-card>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  DesktopOutline, 
  PhonePortraitOutline, 
  SwapHorizontalOutline,
  OptionsOutline,
  GridOutline,
  ColorPaletteOutline,
  RefreshOutline,
  AddOutline
} from '@vicons/ionicons5'
import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'
import { getScreenInfo } from '@/utils/device'

// 响应式数据
const loading = ref(false)
const checkedRowKeys = ref<(string | number)[]>([])
const deviceInfo = ref(getScreenInfo())

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: '张三',
    age: 25,
    email: '<EMAIL>',
    phone: '13800138001',
    department: '技术部',
    position: '前端工程师',
    status: '在职',
    joinDate: '2023-01-15',
    salary: 12000
  },
  {
    id: 2,
    name: '李四',
    age: 30,
    email: '<EMAIL>',
    phone: '13800138002',
    department: '产品部',
    position: '产品经理',
    status: '在职',
    joinDate: '2022-08-20',
    salary: 15000
  },
  {
    id: 3,
    name: '王五',
    age: 28,
    email: '<EMAIL>',
    phone: '13800138003',
    department: '设计部',
    position: 'UI设计师',
    status: '离职',
    joinDate: '2023-03-10',
    salary: 10000
  },
  {
    id: 4,
    name: '赵六',
    age: 32,
    email: '<EMAIL>',
    phone: '13800138004',
    department: '技术部',
    position: '后端工程师',
    status: '在职',
    joinDate: '2021-12-05',
    salary: 14000
  }
])

// 表格列配置
const tableColumns = ref([
  {
    key: 'name',
    title: '姓名',
    width: 100,
    mobileTitle: true, // 移动端作为主标题
    mobileOrder: 1
  },
  {
    key: 'position',
    title: '职位',
    width: 120,
    mobileSubtitle: true, // 移动端作为副标题
    mobileOrder: 2
  },
  {
    key: 'department',
    title: '部门',
    width: 100,
    mobilePosition: 'header', // 移动端显示在头部
    mobileOrder: 3,
    render: (row: any) => {
      const colors: any = {
        '技术部': 'success',
        '产品部': 'info',
        '设计部': 'warning'
      }
      return h('n-tag', { type: colors[row.department] || 'default', size: 'small' }, row.department)
    }
  },
  {
    key: 'status',
    title: '状态',
    width: 80,
    mobilePosition: 'header', // 移动端显示在头部
    mobileOrder: 4,
    render: (row: any) => {
      return h('n-tag', { 
        type: row.status === '在职' ? 'success' : 'error', 
        size: 'small' 
      }, row.status)
    }
  },
  {
    key: 'age',
    title: '年龄',
    width: 80,
    mobileOrder: 5
  },
  {
    key: 'email',
    title: '邮箱',
    width: 180,
    mobileOrder: 6
  },
  {
    key: 'phone',
    title: '电话',
    width: 120,
    mobileOrder: 7
  },
  {
    key: 'joinDate',
    title: '入职日期',
    width: 120,
    mobilePosition: 'footer', // 移动端显示在底部
    mobileOrder: 8
  },
  {
    key: 'salary',
    title: '薪资',
    width: 100,
    mobileShow: false, // 移动端默认隐藏
    mobileOrder: 9,
    render: (row: any) => `¥${row.salary.toLocaleString()}`
  }
])

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: ({ itemCount }: any) => `共 ${itemCount} 条`
})

// 方法：刷新数据
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    window.$message?.success('数据刷新成功')
  }, 1000)
}

// 方法：添加随机数据
const addRandomData = () => {
  const names = ['钱七', '孙八', '周九', '吴十']
  const departments = ['技术部', '产品部', '设计部', '运营部']
  const positions = ['工程师', '经理', '设计师', '专员']
  const statuses = ['在职', '离职']
  
  const newId = Math.max(...tableData.value.map(item => item.id)) + 1
  const randomName = names[Math.floor(Math.random() * names.length)]
  const randomDept = departments[Math.floor(Math.random() * departments.length)]
  const randomPos = positions[Math.floor(Math.random() * positions.length)]
  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
  
  tableData.value.push({
    id: newId,
    name: randomName,
    age: Math.floor(Math.random() * 20) + 25,
    email: `${randomName.toLowerCase()}@example.com`,
    phone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
    department: randomDept,
    position: randomPos,
    status: randomStatus,
    joinDate: '2024-01-01',
    salary: Math.floor(Math.random() * 10000) + 8000
  })
  
  window.$message?.success('添加数据成功')
}

// 方法：处理行点击
const handleRowClick = (row: any) => {
  window.$message?.info(`点击了 ${row.name} 的数据`)
}

// 方法：处理编辑
const handleEdit = (row: any) => {
  window.$message?.info(`编辑 ${row.name}`)
}

// 方法：处理删除
const handleDelete = (row: any) => {
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index > -1) {
    tableData.value.splice(index, 1)
    window.$message?.success(`删除 ${row.name} 成功`)
  }
}

// 方法：处理列配置更新
const handleColumnsUpdate = (columns: any[]) => {
  tableColumns.value = columns
  window.$message?.success('列配置更新成功')
}

// 更新设备信息
const updateDeviceInfo = () => {
  deviceInfo.value = getScreenInfo()
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', updateDeviceInfo)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateDeviceInfo)
})

// 使用说明代码
const basicUsageCode = `<template>
  <adaptive-data-table
    :data="tableData"
    :columns="tableColumns"
    :loading="loading"
    :pagination="pagination"
    row-key="id"
    mobile-title="数据列表"
    :show-view-toggle="true"
    :show-mobile-config="true"
    :card-columns="2"
    @row-click="handleRowClick"
  />
</template>

<script setup>
import AdaptiveDataTable from '@/components/common/crud/components/AdaptiveDataTable.vue'

const tableData = ref([
  { id: 1, name: '张三', age: 25, status: '在职' }
])

const tableColumns = ref([
  {
    key: 'name',
    title: '姓名',
    mobileTitle: true, // 移动端主标题
    mobileOrder: 1
  },
  {
    key: 'age',
    title: '年龄',
    mobileOrder: 2
  },
  {
    key: 'status',
    title: '状态',
    mobilePosition: 'header', // 移动端头部显示
    mobileOrder: 3
  }
])
</script>`

const mobileConfigCode = `// 移动端列配置说明
interface MobileColumnConfig {
  mobileTitle?: boolean      // 是否作为卡片主标题
  mobileSubtitle?: boolean   // 是否作为卡片副标题
  mobileShow?: boolean       // 是否在移动端显示
  mobileOrder?: number       // 移动端显示顺序
  mobilePosition?: 'header' | 'body' | 'footer'  // 在卡片中的位置
  mobileSpan?: number        // 卡片跨度（用于XxN布局）
}

// 示例配置
const columns = [
  {
    key: 'name',
    title: '姓名',
    mobileTitle: true,        // 主标题
    mobileOrder: 1
  },
  {
    key: 'position',
    title: '职位',
    mobileSubtitle: true,     // 副标题
    mobileOrder: 2
  },
  {
    key: 'status',
    title: '状态',
    mobilePosition: 'header', // 头部标签
    mobileOrder: 3
  },
  {
    key: 'phone',
    title: '电话',
    mobilePosition: 'body',   // 主体内容（默认）
    mobileOrder: 4
  },
  {
    key: 'joinDate',
    title: '入职日期',
    mobilePosition: 'footer', // 底部信息
    mobileOrder: 5
  },
  {
    key: 'salary',
    title: '薪资',
    mobileShow: false,        // 移动端隐藏
    mobileOrder: 6
  }
]`

// Props配置数据
const propsData = ref([
  { prop: 'data', type: 'Array', default: '[]', description: '表格数据' },
  { prop: 'columns', type: 'Array', default: '[]', description: '列配置，支持移动端扩展属性' },
  { prop: 'loading', type: 'Boolean', default: 'false', description: '加载状态' },
  { prop: 'useMobileView', type: 'Boolean', default: 'true', description: '是否启用移动端视图' },
  { prop: 'showMobileHeader', type: 'Boolean', default: 'true', description: '是否显示移动端头部' },
  { prop: 'mobileTitle', type: 'String', default: '数据列表', description: '移动端标题' },
  { prop: 'showViewToggle', type: 'Boolean', default: 'true', description: '是否显示视图切换按钮' },
  { prop: 'showMobileConfig', type: 'Boolean', default: 'true', description: '是否显示移动端配置按钮' },
  { prop: 'cardColumns', type: 'Number|String', default: '2', description: '卡片列数，支持响应式' },
  { prop: 'showActions', type: 'Boolean', default: 'false', description: '是否显示操作按钮' }
])

const propsColumns = ref([
  { key: 'prop', title: '属性', width: 150 },
  { key: 'type', title: '类型', width: 120 },
  { key: 'default', title: '默认值', width: 100 },
  { key: 'description', title: '说明', width: 200 }
])

// 事件说明数据
const eventsData = ref([
  { event: 'row-click', params: '(row, index)', description: '行点击事件' },
  { event: 'update:checked-row-keys', params: '(keys)', description: '选中行变化事件' },
  { event: 'update:columns', params: '(columns)', description: '列配置更新事件' },
  { event: 'update:filters', params: '(filters)', description: '筛选器更新事件' },
  { event: 'update:sorter', params: '(sorter)', description: '排序器更新事件' },
  { event: 'update:page', params: '(page)', description: '页码变化事件' },
  { event: 'update:page-size', params: '(pageSize)', description: '页面大小变化事件' }
])

const eventsColumns = ref([
  { key: 'event', title: '事件名', width: 200 },
  { key: 'params', title: '参数', width: 150 },
  { key: 'description', title: '说明', width: 200 }
])
</script>

<style scoped>
@reference "tailwindcss";

.demo-page {
  @apply p-6 max-w-7xl mx-auto;
}

.demo-header {
  @apply text-center py-8;
}

/* 代码块样式优化 */
:deep(.n-code) {
  @apply rounded-lg;
}

/* 表格样式优化 */
:deep(.n-data-table) {
  @apply rounded-lg overflow-hidden;
}

/* 卡片样式优化 */
:deep(.n-card) {
  @apply shadow-sm border border-gray-100;
}

:deep(.n-card .n-card__header) {
  @apply bg-gray-50;
}

/* 标签样式优化 */
:deep(.n-tag) {
  @apply rounded-full;
}

/* 按钮样式优化 */
:deep(.n-button) {
  @apply rounded-lg;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .demo-page {
    @apply p-4;
  }

  :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
}
</style>
