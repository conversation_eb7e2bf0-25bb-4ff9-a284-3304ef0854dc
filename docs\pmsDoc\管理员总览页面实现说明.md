# 管理员科室人员上报总览页面实现说明 📊

## 概述

本文档描述了管理员科室人员上报总览页面的完整实现，包括前端Vue组件和后端Java接口的开发。

## 前端实现

### 1. 文件结构

```
src/views/modules/pms/PmsMonthlyDeptStaffNumber/
├── index-new.vue                    # 主页面组件
├── useAdminStaffOverview.ts         # Hook函数
├── components/
│   └── DetailModal.vue              # 详细数据弹窗组件
└── styles/                          # 样式文件（可选）
```

### 2. 主要功能

#### 2.1 总览统计卡片
- 总上报科室数
- 总计算人数
- 已上报类型数
- 最新上报时间

#### 2.2 筛选功能
- 上报月份选择（必选）
- 科室多选筛选
- 上报类型多选筛选

#### 2.3 总览表格
- 科室名称
- 上报类型
- 各类人员数量统计
- 上报状态
- 最后更新时间
- 操作按钮（查看详情）

#### 2.4 详细数据查看
- 弹窗展示科室详细上报数据
- 按人员类型分组显示
- 支持数据刷新

#### 2.5 导出功能
- 导出总览数据到Excel

### 3. 技术特点

- 使用Vue 3 Setup语法糖
- 子组件采用TSX render函数模式
- Hook函数抽离业务逻辑
- TypeScript类型定义完整
- 响应式设计支持

## 后端实现

### 1. 需要添加的文件

#### 1.1 VO类
```java
// 文件路径: ../sfm_back/med-pms/src/main/java/com/jp/med/pms/modules/pmsMonthlyStaffNumberReport/vo/AdminStaffOverviewVO.java
// 包含总览数据和统计信息的响应VO

// 文件路径: ../sfm_back/med-pms/src/main/java/com/jp/med/pms/modules/pmsMonthlyStaffNumberReport/vo/DeptOption.java  
// 科室选项VO
```

#### 1.2 请求体类
```java
// 文件路径: ../sfm_back/med-pms/src/main/java/com/jp/med/pms/modules/pmsMonthlyStaffNumberReport/requestBody/AdminStaffOverviewRequestBody.java
// 管理员总览请求参数
```

### 2. Controller层新增方法

在 `PmsMonthlyStaffReportController.java` 中添加以下接口：

- `POST /getAdminOverview` - 获取管理员总览数据
- `POST /getAdminDeptList` - 获取科室列表
- `POST /exportAdminOverview` - 导出总览数据
- `POST /getDeptReportStatus` - 获取上报状态统计
- `POST /batchDeleteReports` - 批量删除上报数据
- `POST /getDeptReportHistory` - 获取上报历史记录

### 3. Service层新增方法

在 `PmsMonthlyStaffReportService.java` 中添加对应的业务逻辑方法。

### 4. 主要业务逻辑

#### 4.1 数据聚合
- 按科室和上报类型分组统计
- 计算各类人员数量汇总
- 获取最新批次数据

#### 4.2 统计计算
- 总科室数统计
- 总人数计算
- 上报类型数统计
- 最新上报时间获取

#### 4.3 导出功能
- 使用EasyExcel导出Excel文件
- 设置合适的文件名和响应头

## API接口说明

### 1. 获取管理员总览数据

**接口地址**: `POST /pms/pmsMonthlyStaffNumberReport/getAdminOverview`

**请求参数**:
```json
{
  "reportMonth": "2024-01-01",
  "deptNames": ["科室1", "科室2"],
  "reportTypeCodes": ["CLINICAL_DOCTOR", "CLINICAL_NURSE"]
}
```

**响应数据**:
```json
{
  "code": 200,
  "data": {
    "overviewList": [...],
    "statsData": {
      "totalDeptCount": 10,
      "totalCalculatedCount": 150.5,
      "reportedTypeCount": 4,
      "latestReportTime": "2024-01-15 10:30:00"
    }
  }
}
```

### 2. 获取科室列表

**接口地址**: `POST /pms/pmsMonthlyStaffNumberReport/getAdminDeptList`

**响应数据**:
```json
{
  "code": 200,
  "data": [
    {
      "pmsDeptName": "心电图诊断室",
      "hrpOrgId": "ORG001",
      "hrpOrgName": "心电图诊断室"
    }
  ]
}
```

## 部署说明

### 1. 前端部署
1. 确保所有依赖已安装
2. 检查API接口路径配置
3. 测试页面功能

### 2. 后端部署
1. 添加VO和RequestBody类
2. 在Controller中添加新接口
3. 在Service中实现业务逻辑
4. 测试接口功能

## 注意事项

1. **权限控制**: 确保只有管理员可以访问此页面
2. **数据安全**: 敏感数据需要适当的权限验证
3. **性能优化**: 大数据量时考虑分页和缓存
4. **错误处理**: 完善的异常处理和用户提示
5. **兼容性**: 确保与现有系统的兼容性

## 测试建议

1. **功能测试**: 测试所有筛选、查看、导出功能
2. **性能测试**: 测试大数据量下的响应时间
3. **兼容性测试**: 测试不同浏览器的兼容性
4. **权限测试**: 测试权限控制是否正确

## 后续优化

1. 添加数据缓存机制
2. 支持更多的筛选条件
3. 添加数据可视化图表
4. 支持批量操作功能
5. 添加数据审核流程 