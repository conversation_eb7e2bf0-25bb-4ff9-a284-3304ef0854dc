<template>
  <j-container :showHeader="false">
    <template #content>
      <div class="bpm-workspace">
        <!-- 顶部统计卡片 -->
        <div class="stat-cards-row">
          <div class="stat-card todo-card" @click="viewMore('todo')">
            <div class="card-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ todoCount }}</div>
              <div class="card-link">
                <span>待办任务</span>
                <span class="view-all">查看全部</span>
              </div>
            </div>
          </div>
          <div class="stat-card done-card" @click="viewMore('done')">
            <div class="card-icon">
              <el-icon><Checked /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ doneCount }}</div>
              <div class="card-link">
                <span>已办任务</span>
                <span class="view-all">查看全部</span>
              </div>
            </div>
          </div>
          <div class="stat-card my-card" @click="viewMore('my')">
            <div class="card-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ myCount }}</div>
              <div class="card-link">
                <span>我的流程</span>
                <span class="view-all">查看全部</span>
              </div>
            </div>
          </div>
          <div class="stat-card copy-card" @click="viewMore('copy')">
            <div class="card-icon">
              <el-icon><CopyDocument /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ copyCount }}</div>
              <div class="card-link">
                <span>抄送我的</span>
                <span class="view-all">查看全部</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作栏 -->
        <div class="action-bar">
          <div class="left-actions">
            <el-button type="primary" class="action-btn" @click="handleStartProcess">
              <el-icon><Plus /></el-icon>发起流程
            </el-button>
            <el-button plain class="action-btn">
              <el-icon><Search /></el-icon>流程查询
            </el-button>
          </div>
          <div class="right-actions">
            <el-radio-group v-model="dateFilter" size="small">
              <el-radio-button label="today">今天</el-radio-button>
              <el-radio-button label="week">本周</el-radio-button>
              <el-radio-button label="month">本月</el-radio-button>
              <el-radio-button label="all">全部</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 待办任务列表 -->
        <div class="task-section" v-if="activeTab === 'todo'">
          <div class="section-header">
            <el-icon><Calendar /></el-icon>
            <span>待办任务</span>
          </div>
          <div class="task-list">
            <el-empty v-if="todoList.length === 0" description="暂无待办任务"></el-empty>
            <div v-else>
              <div v-for="(item, index) in todoList" :key="'todo-' + index" class="task-item task-item-todo">
                <div class="task-left-border"></div>
                <div class="task-main">
                  <div class="task-title">
                    <span class="title-text">{{ item.processInstance?.name }}</span>
                    <!-- <span class="process-id">#{{ item.processInstance?.id?.substr(-6) }}</span> -->
                  </div>
                  <div class="task-desc">
                    <span class="task-icon"
                      ><el-icon><Operation /></el-icon
                    ></span>
                    当前任务：{{ item.name }}
                    <!-- 发起原因 - 同一行显示 -->
                    <span class="task-reason-inline" v-if="item.createReason">
                      <span class="reason-separator">|</span>
                      <span class="reason-icon">
                        <el-icon><InfoFilled /></el-icon>
                      </span>
                      发起原因：{{ item.createReason }}
                    </span>
                  </div>
                  <div class="task-info">
                    <span class="task-creator">
                      <el-icon><User /></el-icon>
                      <el-tag type="info"> {{ item.processInstance?.startUser?.empName || '未知用户' }}</el-tag>
                    </span>
                    <span class="task-dept">
                      <el-icon><OfficeBuilding /></el-icon>

                      <el-tag type="info"> {{ item.processInstance?.startUser?.deptName || '未知科室' }}</el-tag>
                    </span>
                    <span class="task-time">
                      <el-icon><Clock /></el-icon>
                      {{ formatDate(item.createTime) }}
                    </span>
                  </div>
                </div>
                <div class="task-status">
                  <span class="status-tag waiting">待处理</span>
                </div>
                <div class="task-actions">
                  <el-button type="primary" @click="handleAudit(item)" class="operation-btn">
                    <el-icon><Check /></el-icon>
                    办理
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 已办任务列表 -->
        <div class="task-section" v-if="activeTab === 'done'">
          <div class="section-header">
            <el-icon><Checked /></el-icon>
            <span>已办任务</span>
          </div>
          <div class="task-list">
            <el-empty v-if="doneList.length === 0" description="暂无已办任务"></el-empty>
            <div v-else>
              <div v-for="(item, index) in doneList" :key="'done-' + index" class="task-item task-item-done">
                <div class="task-left-border"></div>
                <div class="task-main">
                  <div class="task-title">
                    <span class="title-text">{{ item.processInstance?.name }}</span>
                    <span class="process-id">#{{ item.processInstance?.id?.substr(-6) }}</span>
                  </div>
                  <div class="task-desc">
                    <span class="task-icon"
                      ><el-icon><Checked /></el-icon
                    ></span>
                    审批任务：{{ item.name }}
                    <!-- 发起原因 - 同一行显示 -->
                    <span class="task-reason-inline" v-if="item.createReason">
                      <span class="reason-separator">|</span>
                      <span class="reason-icon">
                        <el-icon><InfoFilled /></el-icon>
                      </span>
                      发起原因：{{ item.createReason }}
                    </span>
                  </div>
                  <div class="task-info">
                    <span class="task-creator">
                      <el-icon><User /></el-icon>
                      <span>{{ item.processInstance?.startUser?.empName || '未知用户' }}</span>
                    </span>
                    <span class="task-dept">
                      <el-icon><OfficeBuilding /></el-icon>
                      <span>{{ item.processInstance?.startUser?.deptName || '未知科室' }}</span>
                    </span>
                    <span class="task-time">
                      <el-icon><Clock /></el-icon>
                      {{ formatDate(item.createTime) }}
                    </span>
                  </div>
                </div>
                <div class="task-status">
                  <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="item.status" />
                </div>
                <div class="task-actions">
                  <el-button type="info" @click="handleViewHistory(item)" class="operation-btn">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 我的流程列表 -->
        <div class="task-section" v-if="activeTab === 'my'">
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>我的流程</span>
          </div>
          <div class="task-list">
            <el-empty v-if="myList.length === 0" description="暂无流程"></el-empty>
            <div v-else>
              <div v-for="(item, index) in myList" :key="'my-' + index" class="task-item">
                <div class="task-main">
                  <div class="task-title">{{ item.name }}</div>
                  <div class="task-desc">
                    流程分类：{{ item.categoryName }}
                    <!-- 发起原因 - 同一行显示 -->
                    <span class="task-reason-inline" v-if="item.createReason">
                      <span class="reason-separator">|</span>
                      <span class="reason-icon">
                        <el-icon><InfoFilled /></el-icon>
                      </span>
                      发起原因：{{ item.createReason }}
                    </span>
                  </div>
                  <div class="task-info">
                    <span class="task-creator">
                      <el-icon><User /></el-icon>
                      <span>{{ item.startUser?.empName || '未知用户' }}</span>
                    </span>
                    <span class="task-dept">
                      <el-icon><OfficeBuilding /></el-icon>
                      <span>{{ item.startUser?.deptName || '未知科室' }}</span>
                    </span>
                    <span class="task-time">
                      <el-icon><Clock /></el-icon>
                      {{ formatDate(item.startTime) }}
                    </span>
                  </div>
                </div>
                <div class="task-status">
                  <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="item.status" />
                </div>
                <div class="task-actions">
                  <el-button type="primary" size="small" @click="handleDetail(item)">详情</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 抄送我的列表 -->
        <div class="task-section" v-if="activeTab === 'copy'">
          <div class="section-header">
            <el-icon><CopyDocument /></el-icon>
            <span>抄送我的</span>
          </div>
          <div class="task-list">
            <el-empty v-if="copyList.length === 0" description="暂无抄送"></el-empty>
            <div v-else>
              <div v-for="(item, index) in copyList" :key="'copy-' + index" class="task-item">
                <div class="task-main">
                  <div class="task-title">{{ item.processInstanceName }}</div>
                  <div class="task-desc">
                    抄送任务：{{ item.taskName }}
                    <!-- 发起原因 - 同一行显示 -->
                    <span class="task-reason-inline" v-if="item.createReason">
                      <span class="reason-separator">|</span>
                      <span class="reason-icon">
                        <el-icon><InfoFilled /></el-icon>
                      </span>
                      发起原因：{{ item.createReason }}
                    </span>
                  </div>
                  <div class="task-info">
                    <span class="task-creator">
                      <el-icon><User /></el-icon>
                      <span>{{ item.startUserName || '未知用户' }}</span>
                    </span>
                    <span class="task-dept">
                      <el-icon><OfficeBuilding /></el-icon>
                      <span>{{ item.startDeptName || '未知科室' }}</span>
                    </span>
                    <span class="task-time">
                      <el-icon><Clock /></el-icon>
                      {{ formatDate(item.createTime) }}
                    </span>
                  </div>
                </div>
                <div class="task-status">
                  <span class="status-tag copied">已抄送</span>
                </div>
                <div class="task-actions">
                  <el-button type="info" size="small" @click="handleCopyDetail(item)">详情</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </j-container>

  <!-- 流程详情弹窗 -->
  <ProcessInstanceDetailModal
    :processInstanceId="currentProcessInstanceId"
    v-model:show="processDetailVisible"
    @close="handleModalClose"
  />
</template>

<script lang="tsx" setup>
  import { ref, reactive, onMounted, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import * as TaskApi from '@/api/bpm/task'
  import * as ProcessInstanceApi from '@/api/bpm/processInstance'
  import { dateFormatter } from '@/utils/bpmAdapter/formatTime'
  import { DICT_TYPE } from '@/utils/bpmAdapter/bpmDictAdapter'
  import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'
  import {
    Calendar,
    Checked,
    Document,
    CopyDocument,
    Plus,
    Search,
    User,
    Clock,
    Check,
    View,
    InfoFilled,
    Briefcase,
    Operation,
    OfficeBuilding,
  } from '@element-plus/icons-vue'

  import { Tag } from 'ant-design-vue'
  const router = useRouter()

  // 数据统计
  const todoCount = ref(0)
  const doneCount = ref(0)
  const myCount = ref(0)
  const copyCount = ref(0)

  // 列表数据
  const todoList = ref([])
  const doneList = ref([])
  const myList = ref([])
  const copyList = ref([])

  // 当前选中的标签页
  const activeTab = ref('todo')
  const loading = ref(false)
  const dateFilter = ref('all')

  // 分页配置
  const queryParams = reactive({
    pageNo: 1,
    pageSize: 20,
  })

  // 详情弹窗
  const currentProcessInstanceId = ref('')
  const processDetailVisible = ref(false)

  // 初始化所有数据
  onMounted(() => {
    fetchAllData()
  })

  // 监听日期筛选器变化
  watch(dateFilter, () => {
    fetchAllData()
  })

  // 格式化日期
  const formatDate = date => {
    if (!date) return ''
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // 获取所有模块数据
  const fetchAllData = async () => {
    loading.value = true
    try {
      // 并行获取四个模块的数据
      await Promise.all([fetchTodoData(), fetchDoneData(), fetchMyData(), fetchCopyData()])
    } finally {
      loading.value = false
    }
  }

  // 根据日期筛选获取日期范围
  const getDateRange = () => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    switch (dateFilter.value) {
      case 'today':
        return [today.toISOString().split('T')[0] + ' 00:00:00', now.toISOString().split('T')[0] + ' 23:59:59']
      case 'week':
        const firstDay = new Date(today)
        firstDay.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1))
        return [firstDay.toISOString().split('T')[0] + ' 00:00:00', now.toISOString().split('T')[0] + ' 23:59:59']
      case 'month':
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
        return [
          firstDayOfMonth.toISOString().split('T')[0] + ' 00:00:00',
          now.toISOString().split('T')[0] + ' 23:59:59',
        ]
      default:
        return []
    }
  }

  // 获取待办任务数据
  const fetchTodoData = async () => {
    const params = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
    } as any

    // 添加日期筛选
    if (dateFilter.value !== 'all') {
      const createTime = getDateRange()
      params.createTime = createTime.join(',')
    }

    const data = await TaskApi.getTaskTodoPage(params)
    console.log('待办任务数据:', data) // 调试日志
    todoList.value = data.list
    todoCount.value = data.total
  }

  // 获取已办任务数据
  const fetchDoneData = async () => {
    const params = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
    } as any

    // 添加日期筛选
    if (dateFilter.value !== 'all') {
      params.createTime = getDateRange()
    }

    const data = await TaskApi.getTaskDonePage(params)
    console.log('已办任务数据:', data) // 调试日志
    doneList.value = data.list
    doneCount.value = data.total
  }

  // 获取我的流程数据
  const fetchMyData = async () => {
    const params = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
    } as any

    // 添加日期筛选
    if (dateFilter.value !== 'all') {
      params.createTime = getDateRange()
    }

    const data = await ProcessInstanceApi.getProcessInstanceMyPage(params)
    console.log('我的流程数据:', data) // 调试日志
    myList.value = data.list
    myCount.value = data.total
  }

  // 获取抄送我的数据
  const fetchCopyData = async () => {
    const params = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
    } as any

    // 添加日期筛选
    if (dateFilter.value !== 'all') {
      params.createTime = getDateRange()
    }

    const data = await ProcessInstanceApi.getProcessInstanceCopyPage(params)
    console.log('抄送我的数据:', data) // 调试日志
    copyList.value = data.list
    copyCount.value = data.total
  }

  // 办理待办任务
  const handleAudit = row => {
    currentProcessInstanceId.value = row.processInstance.id
    processDetailVisible.value = true
  }

  // 查看历史任务
  const handleViewHistory = row => {
    router.push({
      path: '/bpm/processInstance/detail/index',
      query: {
        id: row.processInstance.id,
      },
    })
  }

  // 查看流程详情
  const handleDetail = row => {
    currentProcessInstanceId.value = row.id
    processDetailVisible.value = true
  }

  // 查看抄送任务详情
  const handleCopyDetail = row => {
    router.push({
      path: '/bpm/processInstance/detail/index',
      query: {
        id: row.processInstanceId,
      },
    })
  }

  // 发起流程
  const handleStartProcess = () => {
    router.push({
      path: '/bpm/processInstance/create/index',
    })
  }

  // 查看更多
  const viewMore = type => {
    activeTab.value = type

    const routeMap = {
      todo: '/bpm/task/todo',
      done: '/bpm/task/done',
      my: '/bpm/processInstance',
      copy: '/bpm/task/copy',
    }

    // router.push(routeMap[type])
  }

  // 弹窗关闭后刷新数据
  const handleModalClose = () => {
    fetchAllData()
  }
</script>

<style lang="scss" scoped>
  .bpm-workspace {
    padding: 16px;

    .stat-cards-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .stat-card {
        flex: 1;
        padding: 20px;
        background-color: #fff;
        border-radius: 4px;
        border: 1px solid #ebeef5;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;

        &:hover {
          .view-all {
            color: #409eff;
          }
        }

        .card-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;

          .el-icon {
            font-size: 22px;
          }
        }

        .card-content {
          flex: 1;

          .card-value {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            line-height: 1.2;
          }

          .card-link {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            font-size: 14px;
            color: #606266;

            .view-all {
              color: #909399;
            }
          }
        }

        &.todo-card .card-icon {
          color: #409eff;
        }

        &.done-card .card-icon {
          color: #67c23a;
        }

        &.my-card .card-icon {
          color: #e6a23c;
        }

        &.copy-card .card-icon {
          color: #909399;
        }
      }
    }

    .action-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .left-actions {
        display: flex;
        gap: 8px;

        .action-btn {
          padding: 8px 16px;

          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }

    .task-section {
      margin-bottom: 24px;

      .section-header {
        display: flex;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #ebeef5;

        .el-icon {
          margin-right: 8px;
          font-size: 18px;
        }

        span {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }
      }

      .task-list {
        margin-top: 8px;

        .task-item {
          display: flex;
          padding: 16px 0;
          border-bottom: 1px solid #ebeef5;

          &:last-child {
            border-bottom: none;
          }

          .task-main {
            flex: 1;

            .task-title {
              font-size: 15px;
              font-weight: 500;
              color: #303133;
              margin-bottom: 8px;
            }

            .task-desc {
              font-size: 14px;
              color: #606266;
              margin-bottom: 8px;
            }

            .task-reason-inline {
              display: inline-flex;
              align-items: center;
              font-size: 13px;
              color: #409eff;
              margin-left: 12px;

              .reason-separator {
                color: #dcdfe6;
                margin: 0 8px;
                font-weight: normal;
              }

              .reason-icon {
                display: inline-flex;
                align-items: center;
                margin-right: 4px;

                .el-icon {
                  font-size: 13px;
                }
              }
            }

            .task-info {
              display: flex;
              align-items: center;
              font-size: 13px;
              color: #909399;

              .task-creator,
              .task-dept,
              .task-time {
                display: flex;
                align-items: center;
                margin-right: 16px;

                .el-icon {
                  margin-right: 4px;
                  font-size: 14px;
                }
              }

              .time::before {
                content: '时间: ';
              }
            }
          }

          .task-status {
            display: flex;
            align-items: center;
            padding: 0 20px;

            .status-tag {
              display: inline-block;
              padding: 0 10px;
              height: 24px;
              line-height: 22px;
              font-size: 12px;
              border-radius: 4px;
              border: 1px solid;
              box-sizing: border-box;
              white-space: nowrap;

              &.waiting {
                color: #e6a23c;
                border-color: #faecd8;
                background-color: #fdf6ec;
              }

              &.copied {
                color: #909399;
                border-color: #e9e9eb;
                background-color: #f4f4f5;
              }
            }
          }

          .task-actions {
            display: flex;
            align-items: center;
            min-width: 80px;
            justify-content: flex-end;
          }
        }
      }
    }
  }
</style>
