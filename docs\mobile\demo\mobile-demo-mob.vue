<template>
  <div class="mobile-demo-page">
    <!-- 页面头部 -->
    <div class="demo-header bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-b-2xl mb-6">
      <h1 class="text-xl-mobile font-bold mb-2">移动端适配演示</h1>
      <p class="text-sm-mobile opacity-90">展示响应式设计和移动端优化功能</p>
    </div>

    <!-- 设备信息卡片 -->
    <n-card class="mb-6" :class="deviceClasses">
      <template #header>
        <div class="flex items-center gap-3">
          <n-icon size="24" class="text-blue-500">
            <PhonePortraitOutline v-if="isMobile" />
            <TabletPortraitOutline v-else-if="isTablet" />
            <DesktopOutline v-else />
          </n-icon>
          <span class="font-semibold">设备信息</span>
        </div>
      </template>
      
      <n-space vertical :size="spacingConfig.gap.md">
        <div class="info-item">
          <span class="label">设备类型:</span>
          <n-tag :type="getDeviceTagType()" class="ml-2">
            {{ getDeviceTypeText() }}
          </n-tag>
        </div>
        
        <div class="info-item">
          <span class="label">屏幕尺寸:</span>
          <span class="value">{{ screenInfo.width }} × {{ screenInfo.height }}</span>
        </div>
        
        <div class="info-item">
          <span class="label">触摸设备:</span>
          <n-tag :type="isTouchDevice ? 'success' : 'default'" class="ml-2">
            {{ isTouchDevice ? '是' : '否' }}
          </n-tag>
        </div>
      </n-space>
    </n-card>

    <!-- 组件尺寸演示 -->
    <n-card class="mb-6">
      <template #header>
        <div class="flex items-center gap-3">
          <n-icon size="24" class="text-green-500">
            <ResizeOutline />
          </n-icon>
          <span class="font-semibold">响应式组件</span>
        </div>
      </template>
      
      <n-space vertical :size="spacingConfig.gap.lg">
        <!-- 按钮组 -->
        <div class="demo-section">
          <h3 class="section-title">按钮尺寸</h3>
          <n-space :size="spacingConfig.gap.sm">
            <n-button :size="componentSizes.button" type="primary">
              主要按钮
            </n-button>
            <n-button :size="componentSizes.button" type="default">
              默认按钮
            </n-button>
            <n-button :size="componentSizes.button" type="tertiary">
              次要按钮
            </n-button>
          </n-space>
        </div>

        <!-- 输入框 -->
        <div class="demo-section">
          <h3 class="section-title">输入组件</h3>
          <n-space vertical :size="spacingConfig.gap.sm">
            <n-input 
              :size="componentSizes.input"
              placeholder="响应式输入框"
              v-model:value="inputValue"
            />
            <n-select 
              :size="componentSizes.select"
              placeholder="响应式选择器"
              :options="selectOptions"
              v-model:value="selectValue"
            />
          </n-space>
        </div>

        <!-- 表单 -->
        <div class="demo-section">
          <h3 class="section-title">表单组件</h3>
          <n-form :size="componentSizes.form" label-placement="top">
            <n-form-item label="用户名">
              <n-input 
                :size="componentSizes.input"
                placeholder="请输入用户名"
                v-model:value="formData.username"
              />
            </n-form-item>
            <n-form-item label="邮箱">
              <n-input 
                :size="componentSizes.input"
                placeholder="请输入邮箱"
                v-model:value="formData.email"
              />
            </n-form-item>
          </n-form>
        </div>
      </n-space>
    </n-card>

    <!-- 触摸交互演示 -->
    <n-card class="mb-6" v-if="isTouchDevice">
      <template #header>
        <div class="flex items-center gap-3">
          <n-icon size="24" class="text-orange-500">
            <HandLeftOutline />
          </n-icon>
          <span class="font-semibold">触摸交互</span>
        </div>
      </template>
      
      <n-space vertical :size="spacingConfig.gap.md">
        <div class="touch-demo-grid">
          <div 
            v-for="(item, index) in touchItems" 
            :key="index"
            class="touch-item"
            @touchstart="handleTouchStart(index)"
            @touchend="handleTouchEnd(index)"
            :class="{ 'active': activeTouchIndex === index }"
          >
            <n-icon size="32" class="mb-2">
              <component :is="item.icon" />
            </n-icon>
            <span class="text-sm-mobile">{{ item.label }}</span>
          </div>
        </div>
        
        <n-alert type="info" class="mt-4">
          <template #icon>
            <n-icon><InformationCircleOutline /></n-icon>
          </template>
          触摸上方的图标体验触摸反馈效果
        </n-alert>
      </n-space>
    </n-card>

    <!-- 间距配置演示 -->
    <n-card class="mb-6">
      <template #header>
        <div class="flex items-center gap-3">
          <n-icon size="24" class="text-purple-500">
            <GridOutline />
          </n-icon>
          <span class="font-semibold">间距配置</span>
        </div>
      </template>
      
      <div class="spacing-demo">
        <div class="spacing-item" :style="{ padding: spacingConfig.padding.xs }">
          <span class="spacing-label">XS Padding</span>
        </div>
        <div class="spacing-item" :style="{ padding: spacingConfig.padding.sm }">
          <span class="spacing-label">SM Padding</span>
        </div>
        <div class="spacing-item" :style="{ padding: spacingConfig.padding.md }">
          <span class="spacing-label">MD Padding</span>
        </div>
        <div class="spacing-item" :style="{ padding: spacingConfig.padding.lg }">
          <span class="spacing-label">LG Padding</span>
        </div>
      </div>
    </n-card>

    <!-- 操作按钮 -->
    <div class="demo-actions">
      <n-space :size="spacingConfig.gap.md">
        <n-button 
          type="primary" 
          :size="componentSizes.button"
          class="flex-1"
          @click="handleRefresh"
        >
          刷新演示
        </n-button>
        <n-button 
          type="default" 
          :size="componentSizes.button"
          class="flex-1"
          @click="handleToggleTheme"
        >
          切换主题
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { NCard, NSpace, NTag, NIcon, NButton, NInput, NSelect, NForm, NFormItem, NAlert } from 'naive-ui'
import { 
  PhonePortraitOutline,
  TabletPortraitOutline,
  DesktopOutline,
  ResizeOutline,
  HandLeftOutline,
  GridOutline,
  InformationCircleOutline,
  HeartOutline,
  StarOutline,
  ThumbsUpOutline,
  BookmarkOutline
} from '@vicons/ionicons5'
import { useResponsiveTheme } from '@/composables/useResponsiveTheme'
import { getScreenInfo } from '@/utils/device'

// 使用响应式主题
const {
  currentDeviceType,
  isMobile,
  isTablet,
  isDesktop,
  isTouchDevice,
  deviceClasses,
  componentSizes,
  spacingConfig,
  fontConfig
} = useResponsiveTheme()

// 响应式数据
const inputValue = ref('')
const selectValue = ref(null)
const formData = ref({
  username: '',
  email: ''
})
const activeTouchIndex = ref(-1)
const screenInfo = ref(getScreenInfo())

// 选择器选项
const selectOptions = [
  { label: '选项一', value: 'option1' },
  { label: '选项二', value: 'option2' },
  { label: '选项三', value: 'option3' }
]

// 触摸交互项目
const touchItems = [
  { icon: HeartOutline, label: '喜欢' },
  { icon: StarOutline, label: '收藏' },
  { icon: ThumbsUpOutline, label: '点赞' },
  { icon: BookmarkOutline, label: '书签' }
]

// 计算属性
const getDeviceTypeText = () => {
  switch (currentDeviceType.value) {
    case 'mobile': return '移动端'
    case 'tablet': return '平板'
    case 'desktop': return '桌面端'
    default: return '未知'
  }
}

const getDeviceTagType = () => {
  switch (currentDeviceType.value) {
    case 'mobile': return 'success'
    case 'tablet': return 'warning'
    case 'desktop': return 'info'
    default: return 'default'
  }
}

// 方法
const handleTouchStart = (index: number) => {
  activeTouchIndex.value = index
}

const handleTouchEnd = (index: number) => {
  setTimeout(() => {
    activeTouchIndex.value = -1
  }, 150)
}

const handleRefresh = () => {
  screenInfo.value = getScreenInfo()
  window.$message?.success('演示数据已刷新')
}

const handleToggleTheme = () => {
  // 这里可以添加主题切换逻辑
  window.$message?.info('主题切换功能演示')
}

// 生命周期
onMounted(() => {
  // 监听屏幕尺寸变化
  const updateScreenInfo = () => {
    screenInfo.value = getScreenInfo()
  }
  
  window.addEventListener('resize', updateScreenInfo)
  
  // 清理监听器
  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenInfo)
  })
})
</script>

<style scoped>

@reference "tailwindcss"; 

.mobile-demo-page {
  @apply p-4 min-h-screen bg-gray-50;

}

.demo-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.info-item {
  @apply flex items-center justify-between;
}

.label {
  @apply text-gray-600 font-medium;
}

.value {
  @apply text-gray-900 font-mono;
}

.demo-section {
  @apply space-y-3;
}

.section-title {
  @apply text-base font-semibold text-gray-800 mb-3;
  /* @apply text-lg-mobile; */
}

.touch-demo-grid {
  @apply grid grid-cols-4 gap-4;
  /* @apply grid-cols-2 mobile:gap-6; */
}

.touch-item {
  @apply flex flex-col items-center justify-center p-4 rounded-xl bg-gray-100 transition-all duration-200 cursor-pointer;
  @apply hover:bg-gray-200 active:scale-95;
  min-height: 80px;
}

.touch-item.active {
  @apply bg-blue-100 text-blue-600 scale-95;
}

.spacing-demo {
  @apply space-y-3;
}

.spacing-item {
  @apply bg-blue-50 border border-blue-200 rounded-lg;
}

.spacing-label {
  @apply text-blue-700 font-medium text-sm;
}

.demo-actions {
  @apply mt-8;
  /* pb-safe-bottom; */
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .touch-item:hover {
    @apply bg-gray-100;
  }
}
</style>
